<?php

declare(strict_types=1);

use App\Actions\Collection\GenerateCollectionPdf;
use App\Actions\Collection\GenerateCollectionReceipt;
use App\Actions\Collection\GenerateCollectionReceipts;
use App\Actions\Contract\BuildContractLatitudeLongitudeTable;
use App\Actions\Delivery\GenerateDeliveryPdf;
use App\Actions\Delivery\GenerateDeliveryReceipt;
use App\Actions\Delivery\GenerateDeliveryReceipts;
use App\Actions\Receivable\LoadWhatsAppWaMeLink;
use App\Actions\Reports\GenerateCollectionDeviationReport;
use App\Actions\Reports\GenerateCollectionsTable;
use App\Actions\Reports\GenerateCustomersTable;
use App\Actions\Reports\GenerateDeliveriesTable;
use App\Actions\Reports\GenerateReceivablesTable;
use App\Actions\Reports\GenerateStockStatementReport;
use App\Actions\Webhook\Mailgun\Message\ProcessMailgunDeliveredMessage;
use App\Actions\Webhook\Mailgun\Message\ProcessMailgunOpenedMessage;
use App\Actions\Webhook\Omie\ReceivableSettlement\ProcessOmieReceivableSettlementWebhook;
use App\Actions\Webhook\Omie\ReceivableSettlement\ProcessOmieUndoReceivableSettlementWebhook;
use App\Actions\Webhook\Tecnospeed\PlugBoleto\ProcessTecnospeedPlugBoletoWebhook;
use App\Filament\Pages\BankSlip\CreateBankSlipFromReceivable;
use App\Filament\Pages\CreateCustomerProductsReadjustment;
use App\Filament\Pages\Delivery\GenerateDeliveryBatch;
use App\Filament\Pages\Delivery\GenerateDeliveryMap;
use App\Filament\Pages\Receivable\SettleReceivable;
use App\Filament\Pages\Report\LoadCollectionDeviationReport;
use App\Filament\Pages\Report\LoadCollectionsTable;
use App\Filament\Pages\Report\LoadCustomersTable;
use App\Filament\Pages\Report\LoadDeliveriesTable;
use App\Filament\Pages\Report\LoadReceivablesTable;
use App\Filament\Pages\Report\LoadStockStatementReport;
use App\Filament\Pages\UpdatePassword;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Reports\DeliveryMapController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::get('privacy-policy', fn () => view('app.general.privacy_policy'));


Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::middleware('csrf')->group(function () {
        Route::get('/', function () {
            return 'This is your multi-tenant application. The id of the current tenant is ' . tenant('id');
        });

        Route::get('customers/{customer}/create-customer-products-readjustment', CreateCustomerProductsReadjustment::class)->name('customers.create_customer_products_readjustment');

        Route::get('contracts/latitude-longitude-table', BuildContractLatitudeLongitudeTable::class)->name('contracts.build_latitude_longitude_table');

        Route::get('collections/{collection}/pdf', GenerateCollectionPdf::class)->name('collections.generate_pdf');
        Route::get('collections/{collection}/receipt', GenerateCollectionReceipt::class)->name('collections.generate_receipt');
        Route::get('collections/receipts/{recordsToken}', GenerateCollectionReceipts::class)->name('collections.generate_receipts');

        Route::get('deliveries/generate-batch', GenerateDeliveryBatch::class)->name('deliveries.generate_batch');
        Route::get('deliveries/generate-map', GenerateDeliveryMap::class)->name('deliveries.generate_map');
        Route::get('deliveries/{delivery}/pdf', GenerateDeliveryPdf::class)->name('deliveries.generate_pdf');
        Route::get('deliveries/{delivery}/receipt', GenerateDeliveryReceipt::class)->name('deliveries.generate_receipt');
        Route::get('deliveries/receipts/{recordsToken}', GenerateDeliveryReceipts::class)->name('deliveries.generate_receipts');

        Route::get('receivables/{records}/create-bank-slip', CreateBankSlipFromReceivable::class)->name('receivables.create_bank_slip');
        Route::get('receivables/{record}/settle', SettleReceivable::class)->name('receivables.settle');
        Route::get('receivables/{record}/load-wa-me-link', LoadWhatsAppWaMeLink::class)->name('receivables.load_wa_me_link');

        Route::prefix('reports')->group(function () {
            Route::get('stock-statement-report', LoadStockStatementReport::class)->name('reports.load_supply_chain_stock_statement_report');
            Route::get('generate-stock-statement-report/{token}', GenerateStockStatementReport::class)->name('reports.generate_supply_chain_stock_statement_report');
            Route::get('collection-deviation-report', LoadCollectionDeviationReport::class)->name('reports.load_supply_chain_collection_deviation_report');
            Route::get('generate-collection-deviation-report/{token}', GenerateCollectionDeviationReport::class)->name('reports.generate_supply_chain_collection_deviation_report');

            Route::get('reports/generate-customers-table/{token}', GenerateCustomersTable::class)->name('reports.generate_customers_table');
            Route::get('reports/generate-collections-table/{token}', GenerateCollectionsTable::class)->name('reports.generate_collections_table');
            Route::get('reports/generate-deliveries-table/{token}', GenerateDeliveriesTable::class)->name('reports.generate_deliveries_table');
            Route::get('reports/generate-receivables-table/{token}', GenerateReceivablesTable::class)->name('reports.generate_receivables_table');
        });

        Route::get('tables/customers-table', LoadCustomersTable::class)->name('tables.load_customers_table');
        Route::get('tables/collections-table', LoadCollectionsTable::class)->name('tables.load_collections_table');
        Route::get('tables/deliveries-table', LoadDeliveriesTable::class)->name('tables.load_deliveries_table');
        Route::get('tables/receivables-table', LoadReceivablesTable::class)->name('tables.load_receivables_table');
    });

    Route::prefix('app')->group(function () {
        Route::get('change-password', UpdatePassword::class)->name('filament.pages.change-password');
    });

    Route::get('receivables/{token}/billing', \App\Actions\Income\GenerateIncomeCollectionsReport::class)->name('receivables.billing');
    Route::get('receivables/{token}/bank-slip', \App\Actions\BankSlip\Integrations\Omie\OpenOmieBankSlip::class)->name('receivables.bank_slip');

    Route::prefix('webhook')->group(function () {
        Route::prefix('mailgun')->group(function () {
            Route::post('delivered-message', ProcessMailgunDeliveredMessage::class);
            Route::post('opened-message', ProcessMailgunOpenedMessage::class);
        });

        Route::prefix('omie')->group(function () {
            Route::post('receivable-settlement', ProcessOmieReceivableSettlementWebhook::class);
            Route::post('undo-receivable-settlement', ProcessOmieUndoReceivableSettlementWebhook::class);
        });

        Route::prefix('tecnospeed')->group(function () {
            Route::post('plugboleto', ProcessTecnospeedPlugBoletoWebhook::class);
        });
    });

    Route::get('/collections/history', function () {
        return App\Actions\Collections\BuildCollectionsHistoryTable::run();
    })->name('collections.history');
});
