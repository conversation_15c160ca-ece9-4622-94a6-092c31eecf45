APP_NAME="P4M"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

TENANCY_CENTRAL_DOMAIN=

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=p4m_main
DB_USERNAME=root
DB_PASSWORD=

ERPFLEX_DB_CONNECTION=
ERPFLEX_DB_HOST=
ERPFLEX_DB_PORT=
ERPFLEX_DB_DATABASE=
ERPFLEX_DB_USERNAME=
ERPFLEX_DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Brasil API
BRASIL_API_API_V1_URL=https://brasilapi.com.br/api

# ERPFlex
ERP_FLEX_API_URL=https://sistema.erpflex.com.br

# Filament
FILAMENT_PATH=app

# Google Maps
GOOGLE_MAPS_API_KEY=

# IBGE
IBGE_API_V1_URL=https://servicodados.ibge.gov.br/api/v1/localidades/

# Mailgun
MAILGUN_API_URL=https://api.mailgun.net/v3
MAILGUN_API_KEY=
MAILGUN_API_DOMAIN=
MAILGUN_SEND_THROUGH_API=false

# Omie
OMIE_API_URL=https://app.omie.com.br/api

# Receita
RECEITA_API_URL=https://www.receitaws.com.br/v1/cnpj/

# Tecnospeed
TECNOSPEED_PLUGBOLETO_API_LOCAL_URL=https://homologacao.plugboleto.com.br/api
TECNOSPEED_PLUGBOLETO_API_PRODUCTION_URL=https://plugboleto.com.br/api
TECNOSPEED_PLUGBOLETO_SOFTWARE_HOUSE_TAX_ID_NUMBER=
TECNOSPEED_PLUGBOLETO_SOFTWARE_HOUSE_TOKEN=

# ViaCEP
VIA_CEP_API_URL=https://viacep.com.br/ws/
