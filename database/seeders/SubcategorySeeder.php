<?php

namespace Database\Seeders;

use App\Models\Subcategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubcategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Subcategory::create(['category_id' => 1, 'name' => 'Subcategoria 11']);
        Subcategory::create(['category_id' => 1, 'name' => 'Subcategoria 12']);
        Subcategory::create(['category_id' => 2, 'name' => 'Subcategoria 21']);
        Subcategory::create(['category_id' => 2, 'name' => 'Subcategoria 22']);
        Subcategory::create(['category_id' => 3, 'name' => 'Subcategoria 31']);
    }
}
