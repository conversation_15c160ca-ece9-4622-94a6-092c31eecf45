<?php

namespace Database\Seeders;

use App\Actions\Collection\CreateCollection;
use App\Actions\Delivery\CreateDelivery;
use App\Enums\ContractProcessTypeEnum;
use App\Models\Contract;
use App\Models\ContractItem;
use App\Models\Delivery;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class StockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Contract::query()
            ->with('contractItems')
            ->where('process_type', ContractProcessTypeEnum::Loan->value)
            ->get()
            ->each(function (Contract $contract) {
                $referenceDate = now()->subDays(28);

                $this->createDelivery($contract, $referenceDate);

                $referenceDate->addDays(7);

                $this->createCollection($contract, $referenceDate, true);
                $this->createDelivery($contract, $referenceDate);

                $referenceDate->addDays(7);

                while (now()->gt($referenceDate)) {
                    $this->createCollection($contract, $referenceDate);
                    $this->createDelivery($contract, $referenceDate);

                    $referenceDate->addDays(7);
                }
            });
    }

    /**
     * Create a delivery.
     *
     * @param  \App\Models\Contract $contract
     * @param  \Carbon\Carbon $referenceDate
     * @return void
     */
    protected function createDelivery(Contract $contract, Carbon $referenceDate): void
    {
        CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => $referenceDate->format('Y-m-d'),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => $contractItem->quantity
                ];
            })->toArray()
        ], 1, false);
    }

    /**
     * Create a collection.
     *
     * @param  \App\Models\Contract $contract
     * @param  \Carbon\Carbon $referenceDate
     * @param  bool $first
     * @return void
     */
    protected function createCollection(Contract $contract, Carbon $referenceDate, bool $first = false): void
    {
        CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => $referenceDate->format('Y-m-d'),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) use ($first) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => $first
                        ? $contractItem->quantity
                        : random_int(1, $contractItem->quantity)
                ];
            })->toArray()
        ], 1, false);
    }
}
