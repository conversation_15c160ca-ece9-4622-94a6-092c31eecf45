<?php

namespace Database\Seeders;

use App\Enums\StockNatureLocationTypeEnum;
use App\Models\StockNature;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StockNatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        StockNature::create(['name' => 'Próprio', 'location_type' => StockNatureLocationTypeEnum::Own->value]);
        StockNature::create(['name' => 'Em terceiros', 'location_type' => StockNatureLocationTypeEnum::InThirdParty->value]);
        StockNature::create(['name' => 'Material de terceiro', 'location_type' => StockNatureLocationTypeEnum::FromThirdParty->value]);
    }
}
