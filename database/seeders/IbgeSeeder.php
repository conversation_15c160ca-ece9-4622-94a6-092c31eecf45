<?php

namespace Database\Seeders;

use App\Integrations\Api\Ibge\Services\IbgeLocationService;
use App\Models\City;
use App\Models\Region;
use App\Models\State;
use Illuminate\Database\Seeder;

class IbgeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ibgeLocationService = new IbgeLocationService();

        foreach ($ibgeLocationService->getRegions() as $region) {
            Region::create([
                'id' => $region->id,
                'abbreviation' => $region->sigla,
                'name' => $region->nome
            ]);
        }

        foreach ($ibgeLocationService->getStates() as $state) {
            State::create([
                'id' => $state->id,
                'region_id' => $state->regiao->id,
                'abbreviation' => $state->sigla,
                'name' => $state->nome
            ]);
        }

        foreach ($ibgeLocationService->getCities() as $city) {
            City::create([
                'id' => $city->id,
                'state_id' => $city->microrregiao->mesorregiao->UF->id,
                'name' => $city->nome
            ]);
        }
    }
}
