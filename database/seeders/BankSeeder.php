<?php

namespace Database\Seeders;

use App\Models\AvailableBank;
use App\Models\Bank;
use Illuminate\Database\Seeder;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        collect(explode(',', env('BANK_SEEDING_CODES')))->each(function (string $bankCode): void {
            /** @var \App\Models\AvailableBank $availableBank */
            $availableBank = AvailableBank::query()
                ->where('code', $bankCode)
                ->first();

            /** @var \App\Models\Bank $bank */
            $bank = Bank::create([
                'code' => $availableBank->code,
                'name' => $availableBank->name
            ]);

            /** @var \App\Models\BankAccount $bankAccount */
            $bankAccount = $bank->bankAccounts()->create([
                'branch_number' => fake()->randomNumber(4, true),
                'branch_digit' => fake()->randomDigit(),
                'number' => fake()->randomNumber(7, true),
                'digit' => fake()->randomDigit(),
            ]);

            $bankAccount->bankAccountWallets()->create([
                'number' => fake()->randomNumber(7, true),
            ]);
        });
    }
}
