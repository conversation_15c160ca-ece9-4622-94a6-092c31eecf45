<?php

namespace Database\Seeders;

use App\Actions\AvailableBank\BrasilApi\GetAvailableBanksFromBrasilApi;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        GetAvailableBanksFromBrasilApi::run();

        $this->call([
            RoleSeeder::class,
            IbgeSeeder::class,
            UserSeeder::class,
            PaymentMethodSeeder::class,
            BankSeeder::class,
            StockNatureSeeder::class,
            CategorySeeder::class,
            SubcategorySeeder::class,
            ProductSeeder::class,
            CustomerSeeder::class,
            SupplierSeeder::class,
            ContractSeeder::class,
            SmtpConfigurationSeeder::class,
            StockSeeder::class,
            BillingSeeder::class,
        ]);
    }
}
