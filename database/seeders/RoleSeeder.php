<?php

namespace Database\Seeders;

use App\Enums\RoleEnum;
use App\Models\Permission;
use Illuminate\Database\Seeder;
use ReflectionClass;
use Spatie\Permission\PermissionRegistrar;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        \Spatie\Permission\Models\Role::create(['name' => RoleEnum::Administrator->value, 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => RoleEnum::Operator->value, 'guard_name' => 'web']);
        \Spatie\Permission\Models\Role::create(['name' => RoleEnum::Salesman->value, 'guard_name' => 'web']);

        $permissionReflectionClass = new ReflectionClass(Permission::class);
        $permissionsToBeAdded = array_filter(
            array_values($permissionReflectionClass->getConstants()),
            fn ($item) => !in_array($item, ['created_at', 'updated_at'])
        );

        foreach ($permissionsToBeAdded as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        \Spatie\Permission\Models\Role::query()
            ->where('name', RoleEnum::Administrator->value)
            ->get()
            ->each(fn (\Spatie\Permission\Models\Role $role) => $role->givePermissionTo($permissionsToBeAdded));
    }
}
