<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Subcategory;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Subcategory::query()
            ->get()
            ->each(function (Subcategory $subcategory) {
                Product::factory(5)
                    ->subcategory($subcategory)
                    ->create();
            });
    }
}
