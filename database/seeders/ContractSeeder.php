<?php

namespace Database\Seeders;

use App\Actions\Contract\CreateContract;
use App\Enums\ContractProcessTypeEnum;
use App\Enums\ContractTypeEnum;
use App\Models\Customer;
use App\Models\Product;
use Illuminate\Database\Seeder;

class ContractSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Customer::all()->each(function (Customer $customer) {
            $baseDate = now()->subMonths(random_int(0, 11));

            $data = [
                'alternate_id' => null,
                'type' => ContractTypeEnum::Customer->value,
                'process_type' => ContractProcessTypeEnum::Loan->value,
                'entity_id' => $customer->id,
                'term_started_at' => $baseDate->format('Y-m-d'),
                'term_ended_at' => carbon($baseDate)->addYear()->format('Y-m-d'),
                'signed_at' => $baseDate->format('Y-m-d'),
                'items' => Product::query()
                    ->inRandomOrder()
                    ->take(random_int(2, 5))
                    ->get()
                    ->map(fn (Product $product) => [
                        'item_id' => (string) $product->id,
                        'quantity' => (string) random_int(10, 100),
                        'unit_amount' => (string) (random_int(100, 1000) / 100),
                        'visible_in_collections' => true
                    ])
                    ->toArray()
            ];

            CreateContract::run($data);
        });
    }
}
