<?php

namespace Database\Seeders;

use App\Enums\CategoryTypeEnum;
use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Category::create(['name' => 'Categoria Padrão Cliente', 'type' => CategoryTypeEnum::Customer->value]);
        Category::create(['name' => 'Categoria Padrão Fornecedor', 'type' => CategoryTypeEnum::Supplier->value]);
        Category::create(['name' => 'Categoria Padrão Produto', 'type' => CategoryTypeEnum::Product->value]);
    }
}
