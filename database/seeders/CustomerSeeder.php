<?php

namespace Database\Seeders;

use App\Enums\StockNatureLocationTypeEnum;
use App\Models\Customer;
use App\Models\StockNature;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Customer::factory(20)
            ->create()
            ->each(function (Customer $customer) {
                $customer->stockLocations()->create([
                    'stock_nature_id' => StockNature::query()
                        ->where('location_type', StockNatureLocationTypeEnum::InThirdParty->value)
                        ->first()
                        ->id,
                    'name' => $customer->name
                ]);
            });
    }
}
