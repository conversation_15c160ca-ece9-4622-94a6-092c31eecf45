<?php

namespace Database\Seeders;

use App\Actions\Customer\GetCustomerActiveLoanContract;
use App\Models\BankAccountWallet;
use App\Models\Billing;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\Contract;
use App\Models\ContractItem;
use App\Models\Customer;
use Illuminate\Database\Seeder;

class BillingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Customer::query()
            ->get()
            ->each(function (Customer $customer) {
                $collections = Collection::query()
                    ->with('collectionItems')
                    ->where('customer_id', $customer->id)
                    ->whereDoesntHave('billingItem')
                    ->get();

                if ($collections->count() === 0) {
                    return;
                }

                /** @var \App\Models\Contract $contract */
                $activeLoanContract = GetCustomerActiveLoanContract::run($collections->first()->customer);

                $activeLoanContract->load('contractItems');

                $this->createBilling($collections, $activeLoanContract);
            });
    }

    /**
     * Create the billing.
     *
     * @param  \Illuminate\Support\Collection $collections
     * @param  \App\Models\Contract $activeLoanContract
     * @return void
     */
    protected function createBilling(\Illuminate\Support\Collection $collections, Contract $activeLoanContract): void
    {
        $originalAmount = 0;
        $billingItemsData = [];

        $collections->each(function (Collection $collection) use (&$billingItemsData, &$originalAmount, $activeLoanContract) {
            array_push(
                $billingItemsData,
                ...$collection->collectionItems
                    ->map(function (CollectionItem $collectionItem) use ($collection, &$originalAmount, $activeLoanContract) {
                        /** @var \App\Models\ContractItem $contractItem */
                        $contractItem = $activeLoanContract->contractItems
                            ->filter(function (ContractItem $contractItem) use ($collectionItem): bool {
                                return $contractItem->item_id === $collectionItem->product_id;
                            })->first();

                        $originalAmount += ($collectionItem->quantity * $contractItem->unit_amount);

                        return [
                            'document_id' => $collection->id,
                            'document_type' => get_class($collection),
                            'quantity' => $collectionItem->quantity,
                            'unit_amount' => $contractItem->unit_amount,
                            'total_amount' => $collectionItem->quantity * $contractItem->unit_amount
                        ];
                    })
                    ->toArray()
            );
        });

        /** @var \App\Models\Billing $billing */
        $billing = Billing::create([
            'customer_id' => $activeLoanContract->entity_id,
            'contract_id' => $activeLoanContract->id,
            'original_amount' => $originalAmount,
            'pis_wht_amount' => 0,
            'cofins_wht_amount' => 0,
            'csll_wht_amount' => 0,
            'irrf_wht_amount' => 0,
            'inss_wht_amount' => 0,
            'iss_wht_amount' => 0,
            'addition_amount' => 0,
            'discount_amount' => 0,
            'updated_amount' => $originalAmount,
            'billed_at' => now()
        ]);

        collect($billingItemsData)->each(function (array $billingItemData) use (&$billing) {
            $billing->billingItems()->create($billingItemData);
        });

        /** @var \App\Models\Receivable $receivable */
        $receivable = $billing->receivables()->create([
            'customer_id' => $billing->customer_id,
            'sequence' => 1,
            'original_amount' => $billing->original_amount,
            'pis_wht_amount' => $billing->pis_wht_amount,
            'cofins_wht_amount' => $billing->cofins_wht_amount,
            'csll_wht_amount' => $billing->csll_wht_amount,
            'irrf_wht_amount' => $billing->irrf_wht_amount,
            'inss_wht_amount' => $billing->inss_wht_amount,
            'iss_wht_amount' => $billing->iss_wht_amount,
            'addition_amount' => $billing->addition_amount,
            'discount_amount' => $billing->discount_amount,
            'updated_amount' => $billing->updated_amount,
            'issued_at' => now(),
            'expires_at' => now()->addDays(10)
        ]);

        /** @var \App\Models\BankAccountWallet $bankAccountWallet */
        $bankAccountWallet = BankAccountWallet::query()
            ->inRandomOrder()
            ->first();

        $receivable->bankSlips()->create([
            'customer_id' => $receivable->customer_id,
            'bank_id' => $bankAccountWallet->bankAccount->bank_id,
            'bank_code' => $bankAccountWallet->bankAccount->bank->code,
            'bank_name' => $bankAccountWallet->bankAccount->bank->name,
            'bank_account_id' => $bankAccountWallet->bank_account_id,
            'bank_account_branch_number' => $bankAccountWallet->bankAccount->branch_number,
            'bank_account_branch_digit' => $bankAccountWallet->bankAccount->branch_digit,
            'bank_account_number' => $bankAccountWallet->bankAccount->number,
            'bank_account_digit' => $bankAccountWallet->bankAccount->digit,
            'bank_account_wallet_id' => $bankAccountWallet->id,
            'bank_account_wallet_number' => $bankAccountWallet->number,
            'amount' => $receivable->updated_amount,
            'expires_at' => $receivable->expires_at,
            'issued_at' => now()
        ]);
    }
}
