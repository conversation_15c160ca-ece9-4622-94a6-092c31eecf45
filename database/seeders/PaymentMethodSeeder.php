<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        PaymentMethod::factory()
            ->name('À Vista')
            ->generatesInstallments(false)
            ->create();

        PaymentMethod::factory()
            ->name('Boleto')
            ->generatesInstallments(false)
            ->create();
    }
}
