<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $generatesInstallments = random_int(0, 1) === 1;

        $installmentInterval = $generatesInstallments
            ? $this->randomizeInstallmentInterval()
            : null;

        return [
            'name' => 'Forma de pagamento ' . random_int(1, 1000),
            'generates_installments' => $generatesInstallments,
            'installment_interval' => $installmentInterval
        ];
    }

    /**
     * Define the payment method's name.
     *
     * @param  string $name
     * @return static
     */
    public function name(string $name): static
    {
        return $this->state(function () use ($name) {
            return ['name' => $name];
        });
    }

    /**
     * Defines if the payment method generates installments or not.
     *
     * @param  bool $generatesInstallments
     * @return static
     */
    public function generatesInstallments(bool $generatesInstallments): static
    {
        return $this->state(function () use ($generatesInstallments) {
            if (!$generatesInstallments) {
                return [
                    'generates_installments' => false,
                    'installment_interval' => null
                ];
            }

            $generatesInstallments = random_int(0, 1) === 1;

            $installmentInterval = $generatesInstallments
                ? $this->randomizeInstallmentInterval()
                : null;

            return [
                'generates_installments' => $generatesInstallments,
                'installment_interval' => $installmentInterval
            ];
        });
    }

    /**
     * Create a random installment interval.
     *
     * @return string
     */
    protected function randomizeInstallmentInterval(): string
    {
        $installmentCount = random_int(1, 4);
        $interval = [];

        for ($i = 0; $i < $installmentCount; $i++) {
            $interval[] = array_rand([10, 20, 30]);
        }

        return implode(',', $interval);
    }
}
