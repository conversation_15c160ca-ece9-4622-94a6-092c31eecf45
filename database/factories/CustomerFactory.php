<?php

namespace Database\Factories;

use App\Enums\CustomerPreferredChargingMethodEnum;
use App\Models\City;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $companyName = fake()->company;

        $phone = $this->faker->randomNumber(2, true) . (random_int(0, 1) === 1
            ? '9' . $this->faker->randomNumber(8, true)
            : '3' . $this->faker->randomNumber(7, true)
        );

        /** @var \App\Models\City $city */
        $city = City::query()
            ->inRandomOrder()
            ->first();

        $stateRegistration = random_int(0, 1) === 1
            ? fake()->randomNumber(7)
            : null;

        return [
            'name' => $companyName,
            'trading_name' => $companyName,
            'tax_id_number' => fake()->randomNumber(8) . '0001' . fake()->randomNumber(2),
            'exempt_from_state_registration' => is_null($stateRegistration),
            'state_registration' => $stateRegistration,
            'city_registration' => fake()->randomNumber(6),
            'email' => fake()->companyEmail,
            'billing_email' => fake()->companyEmail,
            'operation_email' => fake()->companyEmail,
            'phone_1' => $phone,
            'in_charge_person_1' => 'Responsável 1',
            'address_zipcode' => fake()->randomNumber(8),
            'address_address' => fake()->address,
            'address_number' => fake()->randomNumber(3),
            'address_district' => 'Bairro 1',
            'address_city_id' => $city->id,
            'address_state_id' => $city->state_id,
            'preferred_charging_method' => array_rand(CustomerPreferredChargingMethodEnum::getTranslated())
        ];
    }
}
