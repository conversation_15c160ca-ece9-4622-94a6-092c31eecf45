<?php

namespace Database\Factories;

use App\Models\Subcategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $code = fake()->randomNumber(4);

        return [
            'code' => $code . mb_strtoupper(fake()->randomLetter()),
            'name' => 'Produto ' . $code,
            'description' => fake()->sentence,
            'gross_weight' => 170,
            'net_weight' => 170,
        ];
    }

    /**
     * Define the product subcategory.
     *
     * @param  \App\Models\Subcategory $subcategory
     * @return static
     */
    public function subcategory(Subcategory $subcategory): static
    {
        return $this->state(fn () => ['subcategory_id' => $subcategory->id]);
    }
}
