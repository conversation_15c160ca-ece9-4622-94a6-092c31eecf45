<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bank_slips', function (Blueprint $table) {
            $table->json('omie_data')->nullable()->after('tecnospeed_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bank_slips', function (Blueprint $table) {
            $table->dropColumn('omie_data');
        });
    }
};
