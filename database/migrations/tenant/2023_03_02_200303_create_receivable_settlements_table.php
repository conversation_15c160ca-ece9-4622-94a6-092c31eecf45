<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('receivable_settlements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('operator_id')->nullable()->constrained();
            $table->string('operator_name');
            $table->foreignId('receivable_id')->constrained();
            $table->foreignId('payment_method_id')->constrained();
            $table->decimal('original_amount');
            $table->decimal('addition_amount')->default(0);
            $table->decimal('discount_amount')->default(0);
            $table->decimal('updated_amount');
            $table->string('additional_info')->nullable();
            $table->date('settled_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('receivable_settlements');
    }
};
