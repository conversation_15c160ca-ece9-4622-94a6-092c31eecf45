<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_contact_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_contact_id')->constrained()->onDelete('cascade');
            $table->string('type'); // This will store the enum values
            $table->timestamps();
            
            // Ensure unique combination of contact and type
            $table->unique(['customer_contact_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_contact_types');
    }
};
