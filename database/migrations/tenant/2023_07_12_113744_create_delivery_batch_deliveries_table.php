<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_batch_deliveries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('delivery_batch_id')->constrained();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('collection_id')->nullable()->constrained();
            $table->date('delivery_reference_date');
            $table->foreignId('delivery_id')->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_batch_deliveries');
    }
};
