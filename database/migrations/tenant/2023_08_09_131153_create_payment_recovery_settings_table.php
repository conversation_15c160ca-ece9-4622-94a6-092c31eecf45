<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_recovery_settings', function (Blueprint $table) {
            $table->id();
            $table->integer('overdue_day_count_from');
            $table->integer('overdue_day_count_to')->nullable();
            $table->text('email_template')->nullable();
            $table->text('whatsapp_template')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_recovery_settings');
    }
};
