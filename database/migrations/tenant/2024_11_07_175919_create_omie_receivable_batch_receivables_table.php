<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('omie_receivable_batch_receivables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('omie_receivable_batch_id');
            $table->foreignId('receivable_id')->constrained();
            $table->timestamps();

            $table->foreign('omie_receivable_batch_id', 'orbr_orbi_foreign')
                ->references('id')
                ->on('omie_receivable_batches');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('omie_receivable_batch_receivables');
    }
};
