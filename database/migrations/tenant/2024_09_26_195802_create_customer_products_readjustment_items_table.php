<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_products_readjustment_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_products_readjustment_id');
            $table->foreignId('customer_product_id')->constrained();
            $table->decimal('old_amount');
            $table->decimal('new_amount');
            $table->timestamps();

            $table->foreign('customer_products_readjustment_id', 'cpri_cpri_foreign')
                ->references('id')
                ->on('customer_products_readjustments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_products_readjustment_items');
    }
};
