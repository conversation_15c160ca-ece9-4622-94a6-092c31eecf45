<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lending_stock_adjustment_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lending_stock_adjustment_id');
            $table->foreignId('product_id')->constrained();
            $table->decimal('old_stock_quantity')->nullable();
            $table->decimal('collected_quantity')->nullable();
            $table->decimal('delivered_quantity')->nullable();
            $table->decimal('out_of_movement_quantity')->nullable();
            $table->decimal('adjustment_quantity')->nullable();
            $table->decimal('current_stock_quantity_before_update');
            $table->decimal('current_stock_quantity')->nullable();
            $table->timestamps();

            $table->foreign('lending_stock_adjustment_id', 'lsai_lsa_id_foreign')
                ->references('id')
                ->on('lending_stock_adjustments');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lending_stock_adjustment_items');
    }
};
