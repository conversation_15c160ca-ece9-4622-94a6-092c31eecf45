<?php

use App\Enums\ReceivableStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('receivables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('document_id');
            $table->string('document_type');
            $table->integer('sequence');
            $table->decimal('original_amount');
            $table->decimal('pis_wht_amount')->default(0);
            $table->decimal('cofins_wht_amount')->default(0);
            $table->decimal('csll_wht_amount')->default(0);
            $table->decimal('irrf_wht_amount')->default(0);
            $table->decimal('inss_wht_amount')->default(0);
            $table->decimal('iss_wht_amount')->default(0);
            $table->decimal('addition_amount')->default(0);
            $table->decimal('discount_amount')->default(0);
            $table->decimal('updated_amount');
            $table->string('status')->default(ReceivableStatusEnum::Open->value);
            $table->date('issued_at');
            $table->date('expires_at');
            $table->date('settled_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('receivables');
    }
};
