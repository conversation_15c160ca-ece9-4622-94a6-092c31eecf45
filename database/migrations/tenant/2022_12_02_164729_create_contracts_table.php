<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->string('alternate_id')->nullable();
            $table->foreignId('entity_id');
            $table->string('entity_type');
            $table->string('type');
            $table->string('process_type');
            $table->string('tax_condition')->nullable();
            $table->integer('due_day')->nullable();
            $table->decimal('amount')->default(0);
            $table->decimal('minimum_billing_amount')->nullable();
            $table->string('status');
            $table->date('term_started_at');
            $table->date('term_ended_at');
            $table->date('signed_at');
            $table->date('cancelled_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contracts');
    }
};
