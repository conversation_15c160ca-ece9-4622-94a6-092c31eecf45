<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_products_readjustments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('operator_id')->nullable()->constrained();
            $table->foreignId('index_id')->nullable()->constrained();
            $table->integer('batch_no');
            $table->integer('referring_month');
            $table->integer('referring_year');
            $table->decimal('old_amount');
            $table->decimal('new_amount');
            $table->date('old_ended_at')->nullable();
            $table->date('old_billing_ended_at')->nullable();
            $table->date('new_ended_at')->nullable();
            $table->date('new_billing_ended_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_products_readjustments');
    }
};
