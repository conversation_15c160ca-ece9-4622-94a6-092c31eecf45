<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('billings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('contract_id')->nullable()->constrained();
            $table->decimal('original_amount');
            $table->decimal('pis_wht_amount')->default(0);
            $table->decimal('cofins_wht_amount')->default(0);
            $table->decimal('csll_wht_amount')->default(0);
            $table->decimal('irrf_wht_amount')->default(0);
            $table->decimal('inss_wht_amount')->default(0);
            $table->decimal('iss_wht_amount')->default(0);
            $table->decimal('addition_amount')->default(0);
            $table->decimal('discount_amount')->default(0);
            $table->decimal('updated_amount');
            $table->date('billed_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('billings');
    }
};
