<?php

use App\Enums\CouponTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customer_coupons', function (Blueprint $table) {
            $table->string('type')->default(CouponTypeEnum::AMOUNT->value)->after('customer_id');
            $table->date('starting_at')->nullable()->after('additional_info');
            $table->boolean('apply_to_minimum_amount')->default(false)->after('expires_at');
            $table->boolean('apply_to_regular_amount')->default(false)->after('apply_to_minimum_amount');
        });

        // Set starting_at to created_at for existing records
        DB::statement('UPDATE customer_coupons SET starting_at = created_at WHERE starting_at IS NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customer_coupons', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('starting_at');
            $table->dropColumn('apply_to_minimum_amount');
            $table->dropColumn('apply_to_regular_amount');
        });
    }
};