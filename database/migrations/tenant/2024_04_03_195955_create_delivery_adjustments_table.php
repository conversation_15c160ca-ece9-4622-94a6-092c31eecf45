<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_adjustments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('delivery_id')->nullable()->constrained();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('product_id')->constrained();
            $table->decimal('desired_withdraw_quantity');
            $table->decimal('max_withdraw_percentage');
            $table->string('additional_info', 500)->nullable();
            $table->timestamps();
        });

        Schema::table('delivery_adjustments', function (Blueprint $table) {
            $table->foreignId('parent_delivery_adjustment_id')
                ->nullable()
                ->after('max_withdraw_percentage')
                ->constrained('delivery_adjustments');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_adjustments');
    }
};
