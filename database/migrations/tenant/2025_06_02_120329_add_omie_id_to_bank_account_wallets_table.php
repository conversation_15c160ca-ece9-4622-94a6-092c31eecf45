<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bank_account_wallets', function (Blueprint $table) {
            $table->foreignId('omie_id')->nullable()->after('erp_flex_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bank_account_wallets', function (Blueprint $table) {
            $table->dropColumn('omie_id');
        });
    }
};
