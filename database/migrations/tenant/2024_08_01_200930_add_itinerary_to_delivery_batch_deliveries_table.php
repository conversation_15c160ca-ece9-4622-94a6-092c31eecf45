<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_batch_deliveries', function (Blueprint $table) {
            $table->string('itinerary')->nullable()->after('delivery_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_batch_deliveries', function (Blueprint $table) {
            $table->dropColumn('itinerary');
        });
    }
};
