<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tecnospeed_plug_boleto_integration_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('integratable_id');
            $table->string('integratable_type');
            $table->json('response');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tecnospeed_plug_boleto_integration_logs');
    }
};
