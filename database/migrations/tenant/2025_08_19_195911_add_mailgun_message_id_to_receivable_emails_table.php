<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('receivable_emails', function (Blueprint $table) {
            $table->string('mailgun_message_id')->nullable()->after('receivable_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('receivable_emails', function (Blueprint $table) {
            $table->dropColumn('mailgun_message_id');
        });
    }
};
