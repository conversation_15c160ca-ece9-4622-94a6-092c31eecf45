<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('trading_name');
            $table->string('tax_id_number', 14);
            $table->boolean('exempt_from_state_registration')->default(false);
            $table->string('state_registration')->nullable();
            $table->string('city_registration')->nullable();
            $table->string('email')->nullable();
            $table->string('phone_1', 15)->nullable();
            $table->string('phone_2', 15)->nullable();
            $table->string('address_zipcode', 8)->nullable();
            $table->string('address_address')->nullable();
            $table->string('address_number', 6)->nullable();
            $table->string('address_additional_info')->nullable();
            $table->string('address_district')->nullable();
            $table->foreignId('address_city_id')->nullable()->constrained('cities');
            $table->foreignId('address_state_id')->nullable()->constrained('states');
            $table->string('preferred_earning_method', 20)->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('suppliers');
    }
};
