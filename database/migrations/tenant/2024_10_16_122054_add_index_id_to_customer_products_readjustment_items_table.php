<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_products_readjustment_items', function (Blueprint $table) {
            $table->foreignId('index_id')->nullable()->after('customer_product_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_products_readjustment_items', function (Blueprint $table) {
            $table->dropConstrainedForeignId('index_id');
        });
    }
};
