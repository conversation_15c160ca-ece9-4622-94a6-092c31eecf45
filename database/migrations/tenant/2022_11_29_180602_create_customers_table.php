<?php

use App\Enums\CustomerDefaultDeliveryModelEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('salesman_id')->nullable()->constrained();
            $table->string('name');
            $table->string('trading_name');
            $table->string('tax_id_number', 14);
            $table->boolean('exempt_from_state_registration')->default(false);
            $table->string('state_registration')->nullable();
            $table->string('city_registration')->nullable();
            $table->string('email')->nullable();
            $table->string('billing_email')->nullable();
            $table->string('operation_email')->nullable();
            $table->string('phone_1', 15)->nullable();
            $table->string('in_charge_person_1')->nullable();
            $table->string('phone_2', 15)->nullable();
            $table->string('in_charge_person_2')->nullable();
            $table->string('address_zipcode', 8)->nullable();
            $table->string('address_address')->nullable();
            $table->string('address_number', 6)->nullable();
            $table->string('address_additional_info')->nullable();
            $table->string('address_district')->nullable();
            $table->foreignId('address_city_id')->nullable()->constrained('cities');
            $table->foreignId('address_state_id')->nullable()->constrained('states');
            $table->string('preferred_charging_method', 20)->nullable();
            $table->integer('previous_collection_count')->default(0);
            $table->decimal('minimum_billing_amount')->nullable();
            $table->integer('default_due_day')->nullable();
            $table->string('default_tax_condition')->nullable();
            $table->string('default_delivery_model')->default(CustomerDefaultDeliveryModelEnum::Manual->value);
            $table->decimal('stock_safety_percentage')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customers');
    }
};
