<?php

use App\Enums\BankSlipStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bank_slips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('receivable_id')->constrained();
            $table->foreignId('customer_id')->constrained('customers');
            $table->foreignId('bank_id')->constrained();
            $table->string('bank_code');
            $table->string('bank_name');
            $table->foreignId('bank_account_id')->constrained();
            $table->string('bank_account_branch_number');
            $table->string('bank_account_branch_digit')->nullable();
            $table->string('bank_account_number');
            $table->string('bank_account_digit')->nullable();
            $table->foreignId('bank_account_wallet_id')->constrained();
            $table->string('bank_account_wallet_number');
            $table->string('tecnospeed_id')->nullable();
            $table->decimal('amount');
            $table->json('tecnospeed_data')->nullable();
            $table->string('status')->default(BankSlipStatusEnum::Open->value);
            $table->date('expires_at');
            $table->date('issued_at');
            $table->date('settled_at')->nullable();
            $table->date('cancelled_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bank_slips');
    }
};
