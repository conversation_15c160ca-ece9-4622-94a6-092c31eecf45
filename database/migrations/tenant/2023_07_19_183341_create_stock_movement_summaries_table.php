<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stock_movement_summaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('product_id')->constrained();
            $table->date('movement_date');
            $table->decimal('previous_stock_quantity');
            $table->decimal('collected_quantity')->nullable();
            $table->decimal('delivered_quantity')->nullable();
            $table->decimal('out_of_movement_quantity')->nullable();
            $table->decimal('adjustment_quantity')->nullable();
            $table->decimal('stock_quantity');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stock_movement_summaries');
    }
};
