<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('third_party_id_mappings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_id');
            $table->string('model_type');
            $table->string('third_party_id');
            $table->string('integration_id');
            $table->string('integration_type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('third_party_id_mappings');
    }
};
