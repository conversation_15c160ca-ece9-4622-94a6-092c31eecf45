<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('delivery_address_zipcode', 8)->nullable()->after('longitude');
            $table->string('delivery_address_address')->nullable()->after('delivery_address_zipcode');
            $table->string('delivery_address_number', 6)->nullable()->after('delivery_address_address');
            $table->string('delivery_address_additional_info')->nullable()->after('delivery_address_number');
            $table->string('delivery_address_district')->nullable()->after('delivery_address_additional_info');
            $table->foreignId('delivery_address_city_id')->nullable()->after('delivery_address_district')->constrained('cities');
            $table->foreignId('delivery_address_state_id')->nullable()->after('delivery_address_city_id')->constrained('states');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn('delivery_address_state_id');
            $table->dropColumn('delivery_address_city_id');
            $table->dropColumn('delivery_address_district');
            $table->dropColumn('delivery_address_additional_info');
            $table->dropColumn('delivery_address_number');
            $table->dropColumn('delivery_address_address');
            $table->dropColumn('delivery_address_zipcode');
        });
    }
};
