<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CustomersTableExport extends BaseExport implements FromArray, WithHeadings
{
    /**
     * Create a new instance.
     *
     * @param  array $reportData
     */
    public function __construct(private array $reportData)
    {
    }

    /**
     * Return the sheet header row.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'id',
            'salesman_id',
            'customer_group_id',
            'payment_recovery_setting_id',
            'name',
            'trading_name',
            'tax_id_number',
            'exempt_from_state_registration',
            'state_registration',
            'city_registration',
            'email',
            'billing_email',
            'billing_phone',
            'operation_email',
            'phone_1',
            'in_charge_person_1',
            'phone_2',
            'in_charge_person_2',
            'address_zipcode',
            'address_address',
            'address_number',
            'address_additional_info',
            'address_district',
            'address_city_id',
            'address_state_id',
            'latitude',
            'longitude',
            'preferred_charging_method',
            'previous_collection_count',
            'minimum_billing_amount',
            'default_due_day',
            'default_billing_period',
            'default_tax_condition',
            'default_delivery_model',
            'stock_safety_percentage',
            'active',
            'created_at',
            'updated_at',
        ];
    }

    /**
     * Return the array containing the data.
     *
     * @return array
     */
    public function array(): array
    {
        return $this->reportData;
    }
}
