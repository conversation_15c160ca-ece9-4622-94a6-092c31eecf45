<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CollectionsTableExport extends BaseExport implements FromArray, WithHeadings
{
    /**
     * Create a new instance.
     *
     * @param  array $reportData
     */
    public function __construct(private array $reportData)
    {
    }

    /**
     * Return the sheet header row.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'id',
            'customer_id',
            'contract_id',
            'additional_info',
            'email_sent',
            'collected_at',
            'created_at',
            'updated_at',
            'collection_item_id',
            'collection_item_collection_id',
            'collection_item_product_id',
            'collection_item_quantity',
            'collection_item_created_at',
            'collection_item_updated_at',
            'product_id',
            'product_subcategory_id',
            'product_code',
            'product_name',
            'product_description',
            'product_gross_weight',
            'product_net_weight',
            'product_default_price',
            'product_created_at',
            'product_updated_at',
        ];
    }

    /**
     * Return the array containing the data.
     *
     * @return array
     */
    public function array(): array
    {
        return $this->reportData;
    }
}
