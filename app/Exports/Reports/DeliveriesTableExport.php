<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DeliveriesTableExport extends BaseExport implements FromArray, WithHeadings
{
    /**
     * Create a new instance.
     *
     * @param  array $reportData
     */
    public function __construct(private array $reportData)
    {
    }

    /**
     * Return the sheet header row.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'id',
            'delivery_id',
            'product_id',
            'quantity',
            'created_at',
            'updated_at',
            'delivery_item_id',
            'delivery_item_delivery_id',
            'delivery_item_product_id',
            'delivery_item_quantity',
            'delivery_item_created_at',
            'delivery_item_updated_at',
            'product_id',
            'product_subcategory_id',
            'product_code',
            'product_name',
            'product_description',
            'product_gross_weight',
            'product_net_weight',
            'product_default_price',
            'product_created_at',
            'product_updated_at',
        ];
    }

    /**
     * Return the array containing the data.
     *
     * @return array
     */
    public function array(): array
    {
        return $this->reportData;
    }
}
