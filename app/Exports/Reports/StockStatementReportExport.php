<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class StockStatementReportExport extends BaseExport implements FromArray, WithHeadings
{
    /**
     * Create a new instance.
     *
     * @param  array $reportData
     */
    public function __construct(private array $reportData)
    {
    }

    /**
     * Return the sheet header row.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'Data',
            'Item',
            'Estoque anterior',
            'Quantidade coletada',
            'Quantidade entregue',
            'Ajuste',
            'Estoque atual',
        ];
    }

    /**
     * Return the array containing the data.
     *
     * @return array
     */
    public function array(): array
    {
        return $this->reportData;
    }
}
