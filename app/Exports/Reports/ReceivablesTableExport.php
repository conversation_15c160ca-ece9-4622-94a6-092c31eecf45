<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ReceivablesTableExport extends BaseExport implements FromArray, WithHeadings
{
    /**
     * Create a new instance.
     *
     * @param  array $reportData
     */
    public function __construct(private array $reportData)
    {
    }

    /**
     * Return the sheet header row.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'id',
            'customer_id',
            'document_number',
            'amount',
            'additional_info',
            'issued_at',
            'created_at',
            'updated_at',
            'product_id',
            'product_subcategory_id',
            'product_code',
            'product_name',
            'product_description',
            'product_gross_weight',
            'product_net_weight',
            'product_default_price',
            'product_created_at',
            'product_updated_at',
            'receivable_id',
            'receivable_customer_id',
            'receivable_document_id',
            'receivable_document_type',
            'receivable_sequence',
            'receivable_original_amount',
            'receivable_pis_wht_amount',
            'receivable_cofins_wht_amount',
            'receivable_csll_wht_amount',
            'receivable_irrf_wht_amount',
            'receivable_inss_wht_amount',
            'receivable_iss_wht_amount',
            'receivable_addition_amount',
            'receivable_discount_amount',
            'receivable_updated_amount',
            'receivable_status',
            'receivable_email_sent',
            'receivable_collection_count',
            'receivable_issued_at',
            'receivable_expires_at',
            'receivable_settled_at',
            'receivable_created_at',
            'receivable_updated_at',
        ];
    }

    /**
     * Return the array containing the data.
     *
     * @return array
     */
    public function array(): array
    {
        return $this->reportData;
    }
}
