<?php

namespace App\Notifications\DeliveryBatch;

use App\Models\DeliveryBatch;
use App\Models\DeliveryBatchDelivery;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class NewDeliveryBatchGeneratedNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\DeliveryBatch $deliveryBatch
     */
    public function __construct(protected DeliveryBatch $deliveryBatch)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $contractIds = implode('', array_map(function (string $item): string {
            return "<li>$item</li>";
        }, explode("\n", $this->deliveryBatch->contract_ids)));

        $deliveries = $this->deliveryBatch->deliveryBatchDeliveries
            ->implode(function (DeliveryBatchDelivery $deliveryBatchDelivery): string {
                return "<li>$deliveryBatchDelivery->delivery_id ({$deliveryBatchDelivery->customer->trading_name})</li>";
            });

        return (new MailMessage)
            ->subject('P4M | Geração de novo lote de entregas')
            ->greeting('Olá!')
            ->line("Foi gerado um novo lote de entregas com o ID #{$this->deliveryBatch->id}. Os parâmetros de geração constam abaixo:")
            ->line(new HtmlString("<ul><li>IDs dos contratos:<ul>$contractIds</ul></li><li>Data da entrega: {$this->deliveryBatch->friendly_delivered_at}</li></ul>"))
            ->line('As entregas abaixo foram geradas:')
            ->line(new HtmlString("<ul>$deliveries</ul>"))
            ->salutation('Tenha um ótimo dia!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [];
    }
}
