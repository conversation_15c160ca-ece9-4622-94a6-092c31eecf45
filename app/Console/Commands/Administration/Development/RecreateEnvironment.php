<?php

namespace App\Console\Commands\Administration\Development;

use Illuminate\Console\Command;

class RecreateEnvironment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:env {tenant_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recreate the development environment.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->call('tenants:migrate-fresh', ['--tenants' => $this->argument('tenant_name')]);
        $this->call('tenants:seed', ['--tenants' => $this->argument('tenant_name')]);

        return Command::SUCCESS;
    }
}
