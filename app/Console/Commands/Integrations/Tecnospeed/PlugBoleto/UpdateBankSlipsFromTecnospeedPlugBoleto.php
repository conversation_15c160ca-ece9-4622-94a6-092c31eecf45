<?php

namespace App\Console\Commands\Integrations\Tecnospeed\PlugBoleto;

use Illuminate\Console\Command;

class UpdateBankSlipsFromTecnospeedPlugBoleto extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plugboleto:update_bank_slips_from_tecnospeed_plugboleto';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the bank slips from Tecnospeed.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        tenancy()->initialize('brisa');

        \App\Actions\BankSlip\Tecnospeed\PlugBoleto\UpdateBankSlipsFromTecnospeedPlugBoleto::run(
            '**************'
        );

        return Command::SUCCESS;
    }
}
