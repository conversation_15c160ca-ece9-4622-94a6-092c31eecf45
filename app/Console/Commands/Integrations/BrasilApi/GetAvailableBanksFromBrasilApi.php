<?php

namespace App\Console\Commands\Integrations\BrasilApi;

use App\Models\Tenant;
use Illuminate\Console\Command;

class GetAvailableBanksFromBrasilApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brasil_api:get_available_banks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the available banks from Brasil API.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        tenancy()->runForMultiple(Tenant::all(), function () {
            \App\Actions\AvailableBank\BrasilApi\GetAvailableBanksFromBrasilApi::run();
        });

        return Command::SUCCESS;
    }
}
