<?php

namespace App\Console\Commands\P4M;

use App\Actions\Core\CalculateAddressLatitudeLongitude;
use App\Models\Customer;
use Illuminate\Console\Command;

class AutofixCustomersLatitudeLongitude extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'p4m:autofix_customers_latitude_longitude';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Autofix the customers\' latitude and longitude based on their addresses.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        tenancy()->initialize('brisa');

        Customer::query()
            ->get()
            ->each(function (Customer $customer): void {
                $latitudeLongitude = CalculateAddressLatitudeLongitude::run(
                    $customer->address_address,
                    $customer->address_number,
                    $customer->address_district,
                    $customer->city->name,
                    $customer->state->abbreviation,
                );

                $customer->update([
                    'latitude' => $latitudeLongitude['latitude'],
                    'longitude' => $latitudeLongitude['longitude']
                ]);
            });

        return Command::SUCCESS;
    }
}
