<?php

namespace App\Console\Commands\P4M;

use App\Notifications\P4M\TestNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;

class SendTestNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'p4m:send_test_notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test notification.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Notification::route('mail', '<EMAIL>')->notify(new TestNotification());
        return Command::SUCCESS;
    }
}
