<?php

namespace App\Core\Reports;

use App\Actions\Reports\GenerateReportExcel;
use App\Enums\ReportFormatEnum;
use Barryvdh\DomPDF\Facade\Pdf;
use Throwable;

class ReportBuilder
{
    /**
     * Create a new instance.
     *
     * @param  string|null $format
     * @param  string|null $reportName
     * @param  array|null $reportData
     * @param  string|null $moduleName
     * @param  string|null $excelFileName
     * @param  string|null $reportExportClassName
     * @param  string|null $notificationSubjectReportName
     * @param  string|null $notificationBodyReportEntity
     * @param  string $pdfReportPaper
     * @param  string $pdfReportOrientation
     */
    public function __construct(
        protected ?string $format = null,
        protected ?string $reportName = null,
        protected ?array $reportData = null,
        protected ?string $moduleName = null,
        protected ?string $excelFileName = null,
        protected ?string $reportExportClassName = null,
        protected ?string $notificationSubjectReportName = null,
        protected ?string $notificationBodyReportEntity = null,
        protected string $pdfReportPaper = 'a4',
        protected string $pdfReportOrientation = 'landscape'
    ) {
    }

    public function format(string $format): static
    {
        $this->format = $format;
        return $this;
    }

    public function reportName(string $reportName): static
    {
        $this->reportName = $reportName;
        return $this;
    }

    public function reportData(array $reportData): static
    {
        $this->reportData = $reportData;
        return $this;
    }

    public function moduleName(string $moduleName): static
    {
        $this->moduleName = $moduleName;
        return $this;
    }

    public function excelFileName(string $excelFileName): static
    {
        $this->excelFileName = $excelFileName;
        return $this;
    }

    public function reportExportClassName(string $reportExportClassName): static
    {
        $this->reportExportClassName = $reportExportClassName;
        return $this;
    }

    public function notificationSubjectReportName(string $notificationSubjectReportName): static
    {
        $this->notificationSubjectReportName = $notificationSubjectReportName;
        return $this;
    }

    public function notificationBodyReportEntity(string $notificationBodyReportEntity): static
    {
        $this->notificationBodyReportEntity = $notificationBodyReportEntity;
        return $this;
    }

    public function pdfReportPaper(string $pdfReportPaper): static
    {
        $this->pdfReportPaper = $pdfReportPaper;
        return $this;
    }

    public function pdfReportOrientation(string $pdfReportOrientation): static
    {
        $this->pdfReportOrientation = $pdfReportOrientation;
        return $this;
    }

    public function isExcelFormat(): bool
    {
        return $this->format === ReportFormatEnum::Excel->value;
    }

    public function isPdfFormat(): bool
    {
        return $this->format === ReportFormatEnum::Pdf->value;
    }

    public function isScreenFormat(): bool
    {
        return $this->format === ReportFormatEnum::Screen->value;
    }

    public function buildReport(): mixed
    {
        try {
            switch ($this->format) {
                case ReportFormatEnum::Excel->value:
                    GenerateReportExcel::dispatch(
                        auth()->id(),
                        $this->reportData,
                        $this->moduleName,
                        $this->excelFileName,
                        $this->reportExportClassName,
                        $this->notificationSubjectReportName,
                        $this->notificationBodyReportEntity
                    );

                    success_notification(__('general.responses.report.excel.success'))->send();
                    return redirect()->route('filament.app.pages.list-reports');
                case ReportFormatEnum::Pdf->value:
                    return Pdf::loadView("app.reports.$this->moduleName.{$this->reportName}_report", $this->reportData)
                        ->setPaper($this->pdfReportPaper, $this->pdfReportOrientation)
                        ->stream();
                default:
                    return view("app.reports.$this->moduleName.{$this->reportName}_report", $this->reportData);
            }
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
