<?php

namespace App\Core\Reports;

use App\Enums\ReportFormatEnum;
use Throwable;

class BaseReport
{
    protected string $decodedReportParametersToken;
    protected string $pdfReportPaper = 'a4';
    protected string $pdfReportOrientation = 'landscape';
    protected string $currentFormat;
    protected string $excelFileName;
    protected string $moduleName;
    protected string $reportName;
    protected string $reportExportClassName;
    protected string $notificationSubjectReportName;
    protected string $notificationBodyReportEntity;
    protected string $downloadFileName;
    protected array $parameters;
    protected array $reportData;

    /**
     * Get the report.
     *
     * @return mixed
     */
    protected function getReport(): mixed
    {
        try {
            return app_report()
                ->format($this->currentFormat)
                ->reportName($this->reportName)
                ->reportData($this->reportData)
                ->moduleName($this->moduleName)
                ->excelFileName($this->excelFileName)
                ->reportExportClassName($this->reportExportClassName)
                ->notificationSubjectReportName($this->notificationSubjectReportName)
                ->notificationBodyReportEntity($this->notificationBodyReportEntity)
                ->pdfReportPaper($this->pdfReportPaper)
                ->pdfReportOrientation($this->pdfReportOrientation)
                ->buildReport();
        } catch (Throwable $th) {
            throw $th;
        }
    }

    /**
     * Check if the report is being generated in a "screenable" (PDF or screen) format.
     *
     * @return bool
     */
    protected function isScreenable(): bool
    {
        return $this->currentFormat !== ReportFormatEnum::Excel->value;
    }

    /**
     * Decode the report parameters.
     *
     * @param  string $reportParametersToken
     * @return void
     */
    protected function decodeReportParameters(string $reportParametersToken): void
    {
        $this->decodedReportParametersToken = base64_decode($reportParametersToken);
        $this->parameters = explode(';', $this->decodedReportParametersToken);
    }
}
