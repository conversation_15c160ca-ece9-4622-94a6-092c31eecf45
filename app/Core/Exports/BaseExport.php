<?php

namespace App\Core\Exports;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BaseExport implements ShouldAutoSize, WithStyles
{
    /**
     * Apply styling to columns, cells and rows.
     *
     * @param  \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->getRowDimension(1)->setRowHeight(26);
        $sheet->getStyle(1)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle(1)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle(1)->getFont()->setBold(true);
        $sheet->getStyle(1)->getFont()->getColor()->setRGB('FFFFFF');
        $sheet->getStyle(1)->getFill()->setFillType(Fill::FILL_SOLID);
        $sheet->getStyle(1)->getFill()->getStartColor()->setRGB('1D5C97');
        $sheet->getStyle(1)->getFill()->getEndColor()->setRGB('1D5C97');
    }
}
