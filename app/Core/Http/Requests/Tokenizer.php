<?php

namespace App\Core\Http\Requests;

class Tokenizer
{
    /**
     * Create a default token to be used in URLs.
     *
     * @param  array $ids
     * @return string
     */
    public static function make(array $ids): string
    {
        return base64_encode('ids:' . implode(',', $ids));
    }

    /**
     * Decrypt the token.
     *
     * @param  string $token
     * @return array
     */
    public static function decrypt(string $token): array
    {
        $implodedIds = explode(':', base64_decode($token))[1];

        return $implodedIds !== ""
            ? explode(',', $implodedIds)
            : [];
    }
}
