<?php

namespace App\Core\Http\Requests;

use Lorisleiva\Actions\ActionRequest;
use Symfony\Component\HttpFoundation\Request;

class ReportActionRequest extends ActionRequest
{
    /**
     * Check if the current request is coming from a GET method.
     *
     * @return bool
     */
    public function comesFromGetMethod(): bool
    {
        return $this->method() === Request::METHOD_GET;
    }

    /**
     * Get the report format.
     *
     * @return string
     */
    public function getReportFormat(): string
    {
        return $this->input('format');
    }
}
