<?php

namespace App\Core\Actions\Interfaces;

interface Reportable
{
    /**
     * Build the screenable data for a given report.
     *
     * @param  mixed ...$params
     * @return array
     */
    public function buildScreenableData(mixed ...$params): array;

    /**
     * Build the Excel data for a given report.
     *
     * @param  mixed ...$params
     * @return array
     */
    public function buildExcelData(mixed ...$params): array;

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array;
}
