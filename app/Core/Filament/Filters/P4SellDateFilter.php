<?php

namespace App\Core\Filament\Filters;

use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class P4SellDateFilter extends Filter
{
    public static function buildFrom(string $resource, string $field, string $filterField, bool $defaultNow = true): Filter
    {
        return Filter::make("$filterField")
            ->form([
                DatePicker::make("$filterField")
                    ->label(__("$resource.forms.fields.$filterField"))
                    ->default($defaultNow ? now() : null)
            ])
            ->query(function (Builder $query, array $data) use ($field, $filterField): Builder {
                return $query->when(!is_null($data[$filterField]) && $data[$filterField] !== '', function (Builder $query) use ($field, $filterField, $data): Builder {
                    return $query->whereDate($field, '>=', carbon($data[$filterField])->format('Y-m-d'));
                });
            });
    }

    public static function buildTo(string $resource, string $field, string $filterField, bool $defaultNow = true): Filter
    {
        return Filter::make("$filterField")
            ->form([
                DatePicker::make("$filterField")
                    ->label(__("$resource.forms.fields.$filterField"))
                    ->default($defaultNow ? now() : null)
            ])
            ->query(function (Builder $query, array $data) use ($field, $filterField): Builder {
                return $query->when(!is_null($data[$filterField]) && $data[$filterField] !== '', function (Builder $query) use ($field, $filterField, $data): Builder {
                    return $query->whereDate($field, '<=', carbon($data[$filterField])->format('Y-m-d'));
                });
            });
    }
}
