<?php

namespace App\Core\Filament\Form\Fields;

class Select extends \Filament\Forms\Components\Select
{
    public static function build(
        string $fieldName,
        string $pluralResource,
        array $options,
        int $size = 1,
        bool $required = true,
        bool $disabled = false
    ): static {
        return self::make($fieldName)
            ->label(__("$pluralResource.forms.fields.$fieldName"))
            ->options($options)
            ->columnSpan($size)
            ->required($required)
            ->disabled($disabled);
    }
}
