<?php

namespace App\Core\Filament\Form\Fields;

use App\Models\Customer;
use Closure;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

class EntityIdSelect extends Select
{
    /**
     * Build a default instance.
     *
     * @param  string $fieldName
     * @return static
     */
    public static function makeDefault(string $fieldName = 'entity_id'): static
    {
        return self::make($fieldName)
            ->label(__("collections.forms.fields.$fieldName"))
            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
            ->columnSpan(2)
            ->reactive()
            ->required()
            ->searchable()
            ->getSearchResultsUsing(function (string $search) {
                $taxIdNumber = get_numbers($search);

                return Customer::query()
                    ->where('name', 'like', "%$search%")
                    ->orWhere('trading_name', 'like', "%$search%")
                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                    })
                    ->get()
                    ->map(function (Customer $customer) {
                        return [
                            'id' => $customer->id,
                            'name' => "$customer->name | $customer->trading_name"
                        ];
                    })
                    ->pluck('name', 'id');
            });
    }

    /**
     * Set the value for additional fields.
     *
     * @param  string|null $state
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    public static function setDefaultEntityAdditionalFieldValues(?string $state, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($state)) {
            return null;
        }

        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($state);

        $set('entity_trading_name', $customer?->trading_name ?? '');
        $set('entity_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
