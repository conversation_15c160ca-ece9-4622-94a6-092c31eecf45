<?php

namespace App\Core\Filament\Form\Fields;

class TextInput extends \Filament\Forms\Components\TextInput
{
    public static function build(
        string $fieldName,
        string $pluralResource,
        int $size = 1,
        bool $required = true,
        bool $disabled = false
    ): static {
        return self::make($fieldName)
            ->label(__("$pluralResource.forms.fields.$fieldName"))
            ->placeholder('Escolha uma opção')
            ->required($required)
            ->disabled($disabled)
            ->columnSpan($size);
    }
}
