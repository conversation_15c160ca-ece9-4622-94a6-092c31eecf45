<?php

namespace App\Enums;

use App\Enums\Interfaces\TranslatesCases;

enum CategoryTypeEnum: string implements TranslatesCases
{
    case Customer = 'customer';
    case Supplier = 'supplier';
    case Product = 'product';

    public static function getTranslated(): array
    {
        return [
            self::Customer->value => 'Cliente',
            self::Supplier->value => 'Fornecedor',
            self::Product->value => 'Produto'
        ];
    }
}
