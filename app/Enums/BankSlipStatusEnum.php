<?php

namespace App\Enums;

use App\Enums\Interfaces\TranslatesCases;

enum BankSlipStatusEnum: string implements TranslatesCases
{
    case Open = 'open';
    case Settled = 'settled';
    case Cancelled = 'cancelled';

    public static function getTranslated(): array
    {
        return [
            self::Open->value => 'Em aberto',
            self::Settled->value => 'Pago',
            self::Cancelled->value => 'Baixado',
        ];
    }
}
