<?php

namespace App\Enums;

enum CustomerContactTypeEnum: string
{
    case Billing = 'billing';
    case Operation = 'operation';
    case Contact = 'contact';
    case FrontDesk = 'front-desk';
    case Stock = 'stock';
    case Owner = 'owner';

    public static function getTranslated(): array
    {
        return [
            self::Contact->value => 'Contato',
            self::Stock->value => 'Estoque',
            self::Billing->value => 'Faturamento',
            self::Operation->value => 'Operacional',
            self::Owner->value => 'Proprietário',
            self::FrontDesk->value => 'Recepção',
        ];
    }
}
