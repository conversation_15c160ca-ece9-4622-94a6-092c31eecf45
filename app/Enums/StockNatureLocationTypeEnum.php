<?php

namespace App\Enums;

use App\Enums\Interfaces\TranslatesCases;

enum StockNatureLocationTypeEnum: string implements TranslatesCases
{
    case Own = 'own';
    case InThirdParty = 'in-third-party';
    case FromThirdParty = 'from-third-party';

    /**
     * @inheritDoc
     */
    public static function getTranslated(): array
    {
        return [
            self::Own->value => 'Próprio',
            self::InThirdParty->value => '<PERSON> (Poder de Terceiros)',
            self::FromThirdParty->value => '<PERSON> (Nosso Poder)'
        ];
    }
}
