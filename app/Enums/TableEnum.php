<?php

namespace App\Enums;

enum TableEnum: string
{
    case Customers = 'customers';
    case Collections = 'collections';
    case Deliveries = 'deliveries';
    case Receivables = 'receivables';

    public static function getTranslated(): array
    {
        return [
            self::Customers->value => 'Clientes',
            self::Collections->value => 'Coletas',
            self::Deliveries->value => 'Entregas',
            self::Receivables->value => 'Faturas',
        ];
    }
}
