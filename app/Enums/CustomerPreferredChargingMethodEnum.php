<?php

namespace App\Enums;

use App\Enums\Interfaces\TranslatesCases;

enum CustomerPreferredChargingMethodEnum: string implements TranslatesCases
{
    case BankSlip = 'bank-slip';
    case Pix = 'pix';
    case Transfer = 'transfer';

    public static function getTranslated(): array
    {
        return [
            self::BankSlip->value => 'Boleto bancário',
            self::Pix->value => 'PIX',
            self::Transfer->value => 'Transferência'
        ];
    }
}
