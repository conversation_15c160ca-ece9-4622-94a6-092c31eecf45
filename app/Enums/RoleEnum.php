<?php

namespace App\Enums;

use App\Enums\Interfaces\TranslatesCases;

enum RoleEnum: string implements TranslatesCases
{
    case Administrator = 'administrator';
    case Operator = 'operator';
    case Salesman = 'salesman';

    /**
     * @inheritDoc
     */
    public static function getTranslated(): array
    {
        return [
            self::Administrator->value => 'Administrador',
            self::Operator->value => 'Operador',
            self::Salesman->value => 'Vendedor',
        ];
    }
}
