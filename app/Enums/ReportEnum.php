<?php

namespace App\Enums;

enum ReportEnum: string
{
    case CollectionDeviation = 'collection_deviation';
    case StockStatement = 'stock_statement';

    public static function getTranslated(): array
    {
        return [
            self::CollectionDeviation->value => 'Desvio de coletas',
            self::StockStatement->value => 'Extrato de estoque',
        ];
    }

    public static function getMapped(): array
    {
        return [
            'supply_chain' => [
                'title' => 'Suprimentos',
                'items' => [
                    ReportEnum::CollectionDeviation->value => 'Desvio de coletas',
                    ReportEnum::StockStatement->value => 'Extrato de estoque',
                ]
            ]
        ];
    }
}
