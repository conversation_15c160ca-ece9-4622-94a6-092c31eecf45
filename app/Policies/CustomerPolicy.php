<?php

namespace App\Policies;

use App\Enums\RoleEnum;
use App\Models\Permission;
use App\Models\User;

class CustomerPolicy
{
    public function before(User $user)
    {
        if ($user->hasRole(RoleEnum::Administrator->value)) {
            return true;
        }
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can(Permission::GET_CUSTOMERS);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user): bool
    {
        return $user->can(Permission::GET_CUSTOMERS);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can(Permission::CREATE_CUSTOMERS);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user): bool
    {
        return $user->can(Permission::UPDATE_CUSTOMERS);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user): bool
    {
        return $user->can(Permission::DELETE_CUSTOMERS);
    }
}
