<?php

namespace App\Policies;

use App\Models\BankSlip;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class BankSlipPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can(Permission::GET_BANK_SLIPS);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user): bool
    {
        return $user->can(Permission::GET_BANK_SLIPS);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can(Permission::CREATE_BANK_SLIPS);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user): bool
    {
        return $user->can(Permission::UPDATE_BANK_SLIPS);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user): bool
    {
        return $user->can(Permission::DELETE_BANK_SLIPS);
    }
}
