<?php

namespace App\Models;

use App\Models\Concerns\ReceivableEmail\HandlesReceivableEmailRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Receivable email model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $receivable_id
 * @property  string $mailgun_message_id
 * @property  array $to_emails
 * @property  string $message
 * @property  \Carbon\Carbon $email_sent_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Receivable $receivable
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ReceivableEmailInteraction[] $receivableEmailInteractions
 */
class ReceivableEmail extends Model
{
    use HandlesReceivableEmailRelationships;
    use HasFactory;

    protected $fillable = [
        'receivable_id',
        'mailgun_message_id',
        'to_emails',
        'message',
        'email_sent_at',
    ];

    protected $casts = [
        'to_emails' => 'array'
    ];
}
