<?php

namespace App\Models;

use App\Models\Concerns\CustomerPaymentRecoveryLog\HandlesCustomerPaymentRecoveryLogAttributes;
use App\Models\Concerns\CustomerPaymentRecoveryLog\HandlesCustomerPaymentRecoveryLogRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer payment recovery log model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $receivable_id
 * @property string $type
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $friendly_type
 * @property \App\Models\Customer $customer
 * @property \App\Models\Receivable $receivable
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerPaymentRecoveryLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerPaymentRecoveryLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerPaymentRecoveryLog query()
 * @mixin \Eloquent
 */
class CustomerPaymentRecoveryLog extends Model
{
    use HandlesCustomerPaymentRecoveryLogAttributes;
    use HandlesCustomerPaymentRecoveryLogRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'receivable_id',
        'type',
    ];
}
