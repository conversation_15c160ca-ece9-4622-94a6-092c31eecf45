<?php

namespace App\Models;

use App\Models\Concerns\CollectionReceiptEmail\HandlesCollectionReceiptEmailRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Collection receipt email model.
 *
 * @package App\Models
 * @property int $id
 * @property int $collection_id
 * @property array $to_emails
 * @property string $message
 * @property \Carbon\Carbon $email_sent_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Collection $collection
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionReceiptEmail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionReceiptEmail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionReceiptEmail query()
 * @mixin \Eloquent
 */
class CollectionReceiptEmail extends Model
{
    use HandlesCollectionReceiptEmailRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'collection_id',
        'to_emails',
        'message',
        'email_sent_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'to_emails' => 'array'
    ];
}
