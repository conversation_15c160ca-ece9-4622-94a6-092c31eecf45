<?php

namespace App\Models;

use App\Models\Concerns\CustomerContact\HandlesCustomerContactRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer contact model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $name
 * @property  string $email
 * @property  string $phone
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerContactType[] $customerContactTypes
 */
class CustomerContact extends Model
{
    use HandlesCustomerContactRelationships;

    protected $fillable = [
        'customer_id',
        'name',
        'email',
        'phone'
    ];
}
