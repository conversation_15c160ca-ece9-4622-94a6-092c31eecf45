<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Omie receivable batch model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $operator_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Operator $operator
 *
 * @property  \Illuminate\Support\Collection|\App\Models\OmieReceivableBatchReceivable[] $omieReceivableBatchReceivables
 */
class OmieReceivableBatch extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
    ];

    /**
     * Load the operator relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }

    /**
     * Load the Omie receivable batch receivables.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function omieReceivableBatchReceivables(): HasMany
    {
        return $this->hasMany(OmieReceivableBatchReceivable::class);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (self $omieReceivableBatch): void {
            $omieReceivableBatch->operator_id ??= auth()->user()->operator->id;
        });
    }
}
