<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Available bank model.
 *
 * @package App\Models
 * @property int $id
 * @property string $code
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AvailableBank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AvailableBank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AvailableBank query()
 * @mixin \Eloquent
 */
class AvailableBank extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name'
    ];
}
