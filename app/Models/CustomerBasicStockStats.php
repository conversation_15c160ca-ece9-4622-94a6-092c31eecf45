<?php

namespace App\Models;

use App\Models\Concerns\CustomerBasicStockStats\HandlesCustomerBasicStockStatsRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer basic stock stats model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  int $product_id
 * @property  \Carbon\Carbon $date_from
 * @property  \Carbon\Carbon $date_to
 * @property  int $weekday
 * @property  float $amount
 * @property  string $stats_type
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class CustomerBasicStockStats extends Model
{
    use HandlesCustomerBasicStockStatsRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'product_id',
        'date_from',
        'date_to',
        'weekday',
        'amount',
        'stats_type',
    ];
}
