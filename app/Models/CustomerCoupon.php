<?php

namespace App\Models;

use App\Enums\CouponTypeEnum;
use App\Models\Concerns\CustomerCoupon\HandlesCustomerCouponRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer coupon model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $type
 * @property  float $amount
 * @property  string $additional_info
 * @property  \Carbon\Carbon $starting_at
 * @property  \Carbon\Carbon $expires_at
 * @property  bool $apply_to_minimum_amount
 * @property  bool $apply_to_regular_amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\IncomeCustomerCoupon[] $incomeCustomerCoupons
 */
class CustomerCoupon extends Model
{
    use HandlesCustomerCouponRelationships;
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'amount',
        'additional_info',
        'starting_at',
        'expires_at',
        'apply_to_minimum_amount',
        'apply_to_regular_amount',
    ];

    protected $casts = [
        'customer_id' => 'int',
        'type' => CouponTypeEnum::class,
        'amount' => 'float',
        'apply_to_minimum_amount' => 'bool',
        'apply_to_regular_amount' => 'bool',
    ];

    public function setAmountAttribute(mixed $value)
    {
        $this->attributes['amount'] = unmask_money($value);
    }
}
