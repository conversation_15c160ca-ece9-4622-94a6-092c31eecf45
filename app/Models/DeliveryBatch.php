<?php

namespace App\Models;

use App\Models\Concerns\DeliveryBatch\HandlesDeliveryBatchAttributes;
use App\Models\Concerns\DeliveryBatch\HandlesDeliveryBatchRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Delivery batch model.
 *
 * @package App\Models
 * @property int $id
 * @property int $operator_id
 * @property string $contract_ids
 * @property \Carbon\Carbon $delivered_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $friendly_delivered_at
 * @property \Illuminate\Support\Collection|\App\Models\DeliveryBatchDelivery[] $deliveryBatchDeliveries
 * @property-read int|null $delivery_batch_deliveries_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryBatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryBatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryBatch query()
 * @mixin \Eloquent
 */
class DeliveryBatch extends Model
{
    use HandlesDeliveryBatchAttributes;
    use HandlesDeliveryBatchRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
        'contract_ids',
        'delivered_at',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (self $deliveryBatch): void {
            $deliveryBatch->operator_id = auth()->user()->operator->id;
        });
    }
}
