<?php

namespace App\Models;

use App\Models\Concerns\ReceivableSettlement\HandlesReceivableSettlementRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Receivable settlement model.
 *
 * @package App\Models
 * @property int $id
 * @property int $operator_id
 * @property string $operator_name
 * @property int $receivable_id
 * @property int $payment_method_id
 * @property float $original_amount
 * @property float $addition_amount
 * @property float $discount_amount
 * @property float $updated_amount
 * @property string $additional_info
 * @property \Carbon\Carbon $settled_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Operator $operator
 * @property \App\Models\Receivable $receivable
 * @property \App\Models\PaymentMethod $paymentMethod
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ReceivableSettlement newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ReceivableSettlement newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ReceivableSettlement query()
 * @mixin \Eloquent
 */
class ReceivableSettlement extends Model
{
    use HandlesReceivableSettlementRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
        'operator_name',
        'receivable_id',
        'payment_method_id',
        'original_amount',
        'addition_amount',
        'discount_amount',
        'updated_amount',
        'additional_info',
        'settled_at'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (self $receivableSettlement) {
            $receivableSettlement->operator_id ??= auth()->check()
                ? auth()->user()->operator->id
                : null;

            $receivableSettlement->operator_name ??= auth()->check()
                ? auth()->user()->operator->name
                : 'SISTEMA';
        });
    }
}
