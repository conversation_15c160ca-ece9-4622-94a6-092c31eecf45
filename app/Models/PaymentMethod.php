<?php

namespace App\Models;

use App\Models\Concerns\PaymentMethod\HandlesPaymentMethodRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Payment method model.
 *
 * @package App\Models
 * @property int $id
 * @property int $bank_account_wallet_id
 * @property string $name
 * @property float $discount_fee
 * @property bool $generates_installments
 * @property string $installment_interval
 * @property bool $active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\BankAccountWallet $bankAccountWallet
 * @method static \Database\Factories\PaymentMethodFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentMethod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentMethod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentMethod query()
 * @mixin \Eloquent
 */
class PaymentMethod extends Model
{
    use HandlesPaymentMethodRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'bank_account_wallet_id',
        'name',
        'discount_fee',
        'generates_installments',
        'installment_interval',
        'active'
    ];
}
