<?php

namespace App\Models;

use App\Models\Concerns\StockLocation\HandlesStockLocationRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Stock location model.
 *
 * @package App\Models
 * @property int $id
 * @property int $stock_nature_id
 * @property int $customer_id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\StockNature $stockNature
 * @property \Illuminate\Support\Collection|\App\Models\StockLocationProduct[] $stockLocationProducts
 * @property-read \App\Models\Customer|null $customer
 * @property-read int|null $stock_location_products_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocation withoutTrashed()
 * @mixin \Eloquent
 */
class StockLocation extends Model
{
    use HandlesStockLocationRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'stock_nature_id',
        'customer_id',
        'name'
    ];
}
