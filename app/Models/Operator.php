<?php

namespace App\Models;

use App\Models\Concerns\Operator\HandlesOperatorRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Operator model.
 *
 * @package App\Models
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $email
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property \App\Models\User $user
 * @property \App\Models\Salesman|null $salesman
 */
class Operator extends Model
{
    use HandlesOperatorRelationships;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'name',
        'email'
    ];
}
