<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Omie receivable batch receivable model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $omie_receivable_batch_id
 * @property  int $receivable_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\OmieReceivableBatch $omieReceivableBatch
 * @property  \App\Models\Receivable $receivable
 */
class OmieReceivableBatchReceivable extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'omie_receivable_batch_id',
        'receivable_id',
    ];

    /**
     * Load the Omie receivable batch relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function omieReceivableBatch(): BelongsTo
    {
        return $this->belongsTo(OmieReceivableBatch::class);
    }

    /**
     * Load the receivable relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function receivable(): BelongsTo
    {
        return $this->belongsTo(Receivable::class);
    }
}
