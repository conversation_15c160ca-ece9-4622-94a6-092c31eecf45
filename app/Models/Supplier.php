<?php

namespace App\Models;

use App\Models\Concerns\Supplier\HandlesSupplierRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Supplier model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $trading_name
 * @property string $tax_id_number
 * @property bool $exempt_from_state_registration
 * @property string $state_registration
 * @property string $city_registration
 * @property string $email
 * @property string $billing_email
 * @property string $phone_1
 * @property string $phone_2
 * @property string $address_zipcode
 * @property string $address_address
 * @property string $address_number
 * @property string $address_additional_info
 * @property string $address_district
 * @property int $address_city_id
 * @property int $address_state_id
 * @property string $preferred_earning_method
 * @property bool $active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\City $city
 * @property \App\Models\State $state
 * @property \Illuminate\Support\Collection|\App\Models\Contract[] $contracts
 * @property-read int|null $contracts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\IntegrationLog> $integrationLogs
 * @property-read int|null $integration_logs_count
 * @method static \Database\Factories\SupplierFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Supplier withoutTrashed()
 * @mixin \Eloquent
 */
class Supplier extends Model
{
    use HandlesSupplierRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'trading_name',
        'tax_id_number',
        'exempt_from_state_registration',
        'state_registration',
        'city_registration',
        'email',
        'billing_email',
        'phone_1',
        'phone_2',
        'address_zipcode',
        'address_address',
        'address_number',
        'address_additional_info',
        'address_district',
        'address_city_id',
        'address_state_id',
        'preferred_earning_method',
        'active'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::saving(function (self $supplier) {
            if (is_null($supplier->state_registration)) {
                $supplier->state_registration = 'ISENTO';
            }
        });
    }
}
