<?php

namespace App\Models;

use App\Models\Concerns\CustomerTag\HandlesCustomerTagRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer tag model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Illuminate\Support\Collection|\App\Models\CustomerCustomerTag[] $customerCustomerTags
 * @property-read int|null $customer_customer_tags_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerTag query()
 * @mixin \Eloquent
 */
class CustomerTag extends Model
{
    use HandlesCustomerTagRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];
}
