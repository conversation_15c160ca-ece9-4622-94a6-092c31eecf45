<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * State model.
 *
 * @package App\Models
 * @property int $id
 * @property int $region_id
 * @property string $abbreviation
 * @property string $name
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @property \App\Models\Region $region
 * @property \Illuminate\Support\Collection|\App\Models\City[] $cities
 * @property-read int|null $cities_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\State newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\State newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\State query()
 * @mixin \Eloquent
 */
class State extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'region_id',
        'abbreviation',
        'name'
    ];

    /**
     * Load the region relationship.
     *
     * @return mixed
     */
    public function region(): mixed
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Load the cities relationship.
     *
     * @return mixed
     */
    public function cities(): mixed
    {
        return $this->hasMany(City::class);
    }
}
