<?php

namespace App\Models;

use App\Models\Concerns\DeliveryItem\HandlesDeliveryItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Delivery item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $delivery_id
 * @property int $product_id
 * @property float $quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Delivery $delivery
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryItem withoutTrashed()
 * @mixin \Eloquent
 */
class DeliveryItem extends Model
{
    use HandlesDeliveryItemRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'delivery_id',
        'product_id',
        'quantity'
    ];
}
