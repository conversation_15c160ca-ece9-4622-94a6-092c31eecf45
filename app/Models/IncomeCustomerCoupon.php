<?php

namespace App\Models;

use App\Models\Concerns\IncomeCustomerCoupon\HandlesIncomeCustomerCouponRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Income customer coupon model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $income_id
 * @property  int $customer_coupon_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Income $income
 * @property  \App\Models\CustomerCoupon $customerCoupon
 */
class IncomeCustomerCoupon extends Model
{
    use HandlesIncomeCustomerCouponRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'income_id',
        'customer_coupon_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'income_id' => 'int',
        'customer_coupon_id' => 'int',
    ];
}
