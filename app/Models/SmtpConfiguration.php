<?php

namespace App\Models;

use App\Models\Concerns\SmtpConfiguration\HandlesSmtpConfigurationAttributes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * SMTP configuration model.
 *
 * @package App\Models
 * @property int $id
 * @property string $host
 * @property string $port
 * @property string $username
 * @property string $password
 * @property string $encryption
 * @property string $from_address
 * @property string $from_name
 * @property bool $active
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @method static \Database\Factories\SmtpConfigurationFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SmtpConfiguration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SmtpConfiguration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SmtpConfiguration query()
 * @mixin \Eloquent
 */
class SmtpConfiguration extends Model
{
    use HandlesSmtpConfigurationAttributes;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'host',
        'port',
        'username',
        'password',
        'encryption',
        'from_address',
        'from_name',
        'active'
    ];
}
