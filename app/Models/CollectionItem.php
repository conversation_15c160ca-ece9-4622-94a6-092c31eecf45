<?php

namespace App\Models;

use App\Models\Concerns\CollectionItem\HandlesCollectionItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Collection item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $collection_id
 * @property int $product_id
 * @property float $quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Collection $collection
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CollectionItem withoutTrashed()
 * @mixin \Eloquent
 */
class CollectionItem extends Model
{
    use HandlesCollectionItemRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'collection_id',
        'product_id',
        'quantity'
    ];
}
