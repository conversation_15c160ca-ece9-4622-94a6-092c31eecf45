<?php

namespace App\Models;

use App\Models\Concerns\ThirdPartyIdMapping\HandlesThirdPartyIdMappingRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Third party ID mapping model.
 *
 * @package App\Models
 * @property int $id
 * @property int $model_id
 * @property string $model_type
 * @property string $third_party_id
 * @property int $integration_id
 * @property string $integration_type
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $integration
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $model
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ThirdPartyIdMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ThirdPartyIdMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\ThirdPartyIdMapping query()
 * @mixin \Eloquent
 */
class ThirdPartyIdMapping extends Model
{
    use HandlesThirdPartyIdMappingRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'model_id',
        'model_type',
        'third_party_id',
        'integration_id',
        'integration_type'
    ];
}
