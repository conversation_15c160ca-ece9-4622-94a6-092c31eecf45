<?php

namespace App\Models;

use App\Models\Concerns\CustomerHistory\HandlesCustomerHistoryRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer history model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $operation_id
 * @property string $operation_type
 * @property string $history
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\Collection $operation
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerHistory query()
 * @mixin \Eloquent
 */
class CustomerHistory extends Model
{
    use HandlesCustomerHistoryRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'operation_id',
        'operation_type',
        'history'
    ];
}
