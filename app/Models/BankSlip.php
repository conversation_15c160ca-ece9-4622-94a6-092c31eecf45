<?php

namespace App\Models;

use App\Models\Concerns\BankSlip\HandlesBankSlipAttributes;
use App\Models\Concerns\BankSlip\HandlesBankSlipRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Bank slip model.
 *
 * @package App\Models
 * @property int $id
 * @property int $receivable_id
 * @property int $customer_id
 * @property string $customer_type
 * @property int $bank_id
 * @property string $bank_code
 * @property string $bank_name
 * @property int $bank_account_id
 * @property string $bank_account_branch_number
 * @property string $bank_account_branch_digit
 * @property string $bank_account_number
 * @property string $bank_account_digit
 * @property int $bank_account_wallet_id
 * @property string $bank_account_wallet_number
 * @property string $tecnospeed_id
 * @property float $amount
 * @property array $tecnospeed_data
 * @property array $omie_data
 * @property string $status
 * @property \Carbon\Carbon $expires_at
 * @property \Carbon\Carbon $issued_at
 * @property \Carbon\Carbon $settled_at
 * @property \Carbon\Carbon $cancelled_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $friendly_status
 * @property \App\Models\Receivable $receivable
 * @property \App\Models\Beneficiary|\App\Models\Company $customer
 * @property \App\Models\Bank $bank
 * @property \App\Models\BankAccount $bankAccount
 * @property \App\Models\BankAccountWallet $bankAccountWallet
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\BankSlipUpdateHistory> $bankSlipUpdateHistories
 * @property-read int|null $bank_slip_update_histories_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlip newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlip newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlip query()
 * @mixin \Eloquent
 */
class BankSlip extends Model
{
    use HandlesBankSlipAttributes;
    use HandlesBankSlipRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'receivable_id',
        'customer_id',
        'customer_type',
        'bank_id',
        'bank_code',
        'bank_name',
        'bank_account_id',
        'bank_account_branch_number',
        'bank_account_branch_digit',
        'bank_account_number',
        'bank_account_digit',
        'bank_account_wallet_id',
        'bank_account_wallet_number',
        'tecnospeed_id',
        'amount',
        'tecnospeed_data',
        'omie_data',
        'status',
        'expires_at',
        'issued_at',
        'settled_at',
        'cancelled_at',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'tecnospeed_data' => 'array',
        'omie_data' => 'array',
    ];
}
