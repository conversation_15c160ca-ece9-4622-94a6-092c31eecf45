<?php

namespace App\Models;

use App\Models\Concerns\IncomeItem\HandlesIncomeItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Income item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $income_id
 * @property int $product_id
 * @property float $quantity
 * @property float $unit_amount
 * @property float $total_amount
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Income $income
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeItem query()
 * @mixin \Eloquent
 */
class IncomeItem extends Model
{
    use HandlesIncomeItemRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'income_id',
        'product_id',
        'quantity',
        'unit_amount',
        'total_amount',
    ];
}
