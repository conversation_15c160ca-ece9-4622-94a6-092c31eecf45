<?php

namespace App\Models\Concerns\IntegrationLog;

use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesIntegrationLogRelationships
{
    /**
     * Load the integration relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function integration(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Load the entity relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function entity(): MorphTo
    {
        return $this->morphTo();
    }
}
