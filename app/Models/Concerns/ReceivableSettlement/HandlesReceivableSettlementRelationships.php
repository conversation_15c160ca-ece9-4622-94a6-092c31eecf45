<?php

namespace App\Models\Concerns\ReceivableSettlement;

use App\Models\Operator;
use App\Models\PaymentMethod;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesReceivableSettlementRelationships
{
    /**
     * Load the operator relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }

    /**
     * Load the receivable relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function receivable(): BelongsTo
    {
        return $this->belongsTo(Receivable::class);
    }

    /**
     * Load the payment method relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }
}
