<?php

namespace App\Models\Concerns\CustomerContact;

use App\Models\Customer;
use App\Models\CustomerContactType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCustomerContactRelationships
{
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function customerContactTypes(): HasMany
    {
        return $this->hasMany(CustomerContactType::class);
    }
}
