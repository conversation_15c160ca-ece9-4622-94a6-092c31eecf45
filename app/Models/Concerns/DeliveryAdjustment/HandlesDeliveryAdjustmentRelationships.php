<?php

namespace App\Models\Concerns\DeliveryAdjustment;

use App\Models\Customer;
use App\Models\DeliveryAdjustment;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesDeliveryAdjustmentRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Load the parent delivery adjustment relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parentDeliveryAdjustment(): BelongsTo
    {
        return $this->belongsTo(DeliveryAdjustment::class, 'parent_delivery_adjustment_id');
    }
}
