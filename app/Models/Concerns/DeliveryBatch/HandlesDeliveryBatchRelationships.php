<?php

namespace App\Models\Concerns\DeliveryBatch;

use App\Models\DeliveryBatchDelivery;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesDeliveryBatchRelationships
{
    /**
     * Load the delivery batch deliveries relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function deliveryBatchDeliveries(): HasMany
    {
        return $this->hasMany(DeliveryBatchDelivery::class);
    }
}
