<?php

namespace App\Models\Concerns\DeliveryBatchDelivery;

use App\Models\Customer;
use App\Models\Delivery;
use App\Models\DeliveryBatch;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesDeliveryBatchDeliveryRelationships
{
    public function deliveryBatch(): BelongsTo
    {
        return $this->belongsTo(DeliveryBatch::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }
}
