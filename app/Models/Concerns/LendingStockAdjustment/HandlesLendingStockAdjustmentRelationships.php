<?php

namespace App\Models\Concerns\LendingStockAdjustment;

use App\Models\LendingStockAdjustmentItem;
use App\Models\Customer;
use App\Models\CustomerHistory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HandlesLendingStockAdjustmentRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the lending stock adjustment items.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function lendingStockAdjustmentItems(): HasMany
    {
        return $this->hasMany(LendingStockAdjustmentItem::class);
    }

    /**
     * Load the customer histories relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function customerHistories(): MorphMany
    {
        return $this->morphMany(CustomerHistory::class, 'operation');
    }
}
