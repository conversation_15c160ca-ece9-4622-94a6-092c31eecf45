<?php

namespace App\Models\Concerns\Delivery;

use App\Models\Customer;
use App\Models\DeliveryBatchDelivery;
use App\Models\DeliveryItem;
use App\Models\DeliveryReceiptEmail;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HandlesDeliveryRelationships
{
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function deliveryBatchDelivery(): HasOne
    {
        return $this->hasOne(DeliveryBatchDelivery::class);
    }

    public function deliveryItems(): HasMany
    {
        return $this->hasMany(DeliveryItem::class);
    }

    public function deliveryReceiptEmails(): HasMany
    {
        return $this->hasMany(DeliveryReceiptEmail::class);
    }
}
