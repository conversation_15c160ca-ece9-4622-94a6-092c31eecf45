<?php

namespace App\Models\Concerns\CustomerTag;

use App\Models\CustomerCustomerTag;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCustomerTagRelationships
{
    /**
     * Load the customer customer tags relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function customerCustomerTags(): HasMany
    {
        return $this->hasMany(CustomerCustomerTag::class);
    }
}
