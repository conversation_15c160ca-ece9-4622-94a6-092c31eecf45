<?php

namespace App\Models\Concerns\Billing;

use App\Models\BillingItem;
use App\Models\Customer;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HandlesBillingRelationships
{
    /**
     * Load the customer relationship
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the billing items relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function billingItems(): HasMany
    {
        return $this->hasMany(BillingItem::class);
    }

    /**
     * Load the receivables relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function receivables(): MorphMany
    {
        return $this->morphMany(Receivable::class, 'document');
    }
}
