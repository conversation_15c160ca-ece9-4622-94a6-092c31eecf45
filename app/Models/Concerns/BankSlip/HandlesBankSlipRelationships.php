<?php

namespace App\Models\Concerns\BankSlip;

use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BankAccountWallet;
use App\Models\BankSlipUpdateHistory;
use App\Models\Customer;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesBankSlipRelationships
{
    /**
     * Load the receivable relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function receivable(): BelongsTo
    {
        return $this->belongsTo(Receivable::class);
    }

    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the bank relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    /**
     * Load the bank account relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    /**
     * Load the bank account wallet relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bankAccountWallet(): BelongsTo
    {
        return $this->belongsTo(BankAccountWallet::class);
    }

    /**
     * Load the bank slip update histories relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function bankSlipUpdateHistories(): HasMany
    {
        return $this->hasMany(BankSlipUpdateHistory::class);
    }
}
