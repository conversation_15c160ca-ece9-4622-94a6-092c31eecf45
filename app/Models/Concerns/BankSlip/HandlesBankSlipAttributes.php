<?php

namespace App\Models\Concerns\BankSlip;

use App\Enums\BankSlipStatusEnum;

trait HandlesBankSlipAttributes
{
    /**
     * Mutator for the "amount" attribute.
     *
     * @return void
     */
    public function setAmountAttribute(mixed $value): void
    {
        $this->attributes['amount'] = unmask_money($value);
    }

    /**
     * Accessor for the "friendly status" attribute.
     *
     * @return string
     */
    public function getFriendlyStatusAttribute(): string
    {
        return BankSlipStatusEnum::getTranslated()[$this->status];
    }
}
