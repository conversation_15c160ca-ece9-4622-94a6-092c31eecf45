<?php

namespace App\Models\Concerns\SmtpConfiguration;

trait HandlesSmtpConfigurationAttributes
{
    /**
     * Mutator for the "username" attribute.
     *
     * @return void
     */
    public function setUsernameAttribute(mixed $value): void
    {
        $this->attributes['username'] = openssl_encrypt(
            data: $value,
            cipher_algo: 'AES-256-CBC',
            passphrase: config('app.qi'),
            iv: config('app.ve')
        );
    }

    /**
     * Accessor for the "username" attribute.
     *
     * @return string
     */
    public function getUsernameAttribute(): string
    {
        return openssl_decrypt(
            data: $this->attributes['username'],
            cipher_algo: 'AES-256-CBC',
            passphrase: config('app.qi'),
            iv: config('app.ve')
        );
    }

    /**
     * Mutator for the "password" attribute.
     *
     * @return void
     */
    public function setPasswordAttribute(mixed $value): void
    {
        $this->attributes['password'] = openssl_encrypt(
            data: $value,
            cipher_algo: 'AES-256-CBC',
            passphrase: config('app.qi'),
            iv: config('app.ve')
        );
    }

    /**
     * Accessor for the "password" attribute.
     *
     * @return void
     */
    public function getPasswordAttribute(): string
    {
        return openssl_decrypt(
            data: $this->attributes['password'],
            cipher_algo: 'AES-256-CBC',
            passphrase: config('app.qi'),
            iv: config('app.ve')
        );
    }
}
