<?php

namespace App\Models\Concerns\Collection;

use App\Models\BillingItem;
use App\Models\CollectionItem;
use App\Models\CollectionReceiptEmail;
use App\Models\Customer;
use App\Models\CustomerHistory;
use App\Models\IncomeCollection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait HandlesCollectionRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the collection items relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function collectionItems(): HasMany
    {
        return $this->hasMany(CollectionItem::class);
    }

    /**
     * Load the customer histories relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function customerHistories(): MorphMany
    {
        return $this->morphMany(CustomerHistory::class, 'operation');
    }

    /**
     * Load the collection receipt emails relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function collectionReceiptEmails(): HasMany
    {
        return $this->hasMany(CollectionReceiptEmail::class);
    }

    /**
     * Load the billing item relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function billingItem(): MorphOne
    {
        return $this->morphOne(BillingItem::class, 'document');
    }

    /**
     * Load the income collections relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function incomeCollections(): HasMany
    {
        return $this->hasMany(IncomeCollection::class);
    }
}
