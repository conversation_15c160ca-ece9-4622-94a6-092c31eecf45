<?php

namespace App\Models\Concerns\CustomerCustomerTag;

use App\Models\Customer;
use App\Models\CustomerTag;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesCustomerCustomerTagRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the customer tag relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customerTag(): BelongsTo
    {
        return $this->belongsTo(CustomerTag::class);
    }
}
