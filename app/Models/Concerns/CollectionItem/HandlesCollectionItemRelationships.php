<?php

namespace App\Models\Concerns\CollectionItem;

use App\Models\Collection;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesCollectionItemRelationships
{
    /**
     * Load the collection relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
