<?php

namespace App\Models\Concerns\Operator;

use App\Models\Salesman;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HandlesOperatorRelationships
{
    /**
     * Load the user relationship.
     *
     * @return mixed
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Load the salesman relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function salesman(): HasOne
    {
        return $this->hasOne(Salesman::class);
    }
}
