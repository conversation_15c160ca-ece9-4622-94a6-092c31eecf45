<?php

namespace App\Models\Concerns\Customer;

use App\Models\BankAccountWallet;
use App\Models\Billing;
use App\Models\City;
use App\Models\Collection;
use App\Models\CustomerBasicStockStats;
use App\Models\CustomerContact;
use App\Models\CustomerCoupon;
use App\Models\CustomerCustomerTag;
use App\Models\CustomerGroup;
use App\Models\CustomerHistory;
use App\Models\CustomerPaymentRecoveryLog;
use App\Models\CustomerProduct;
use App\Models\CustomerProductsReadjustment;
use App\Models\Delivery;
use App\Models\Index;
use App\Models\IntegrationLog;
use App\Models\LendingStockAdjustment;
use App\Models\PaymentRecoverySetting;
use App\Models\Receivable;
use App\Models\Salesman;
use App\Models\State;
use App\Models\StockLocation;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HandlesCustomerRelationships
{
    public function salesman(): BelongsTo
    {
        return $this->belongsTo(Salesman::class);
    }

    public function customerGroup(): BelongsTo
    {
        return $this->belongsTo(CustomerGroup::class);
    }

    public function paymentRecoverySetting(): BelongsTo
    {
        return $this->belongsTo(PaymentRecoverySetting::class);
    }

    public function index(): BelongsTo
    {
        return $this->belongsTo(Index::class);
    }

    public function defaultBankAccountWallet(): BelongsTo
    {
        return $this->belongsTo(BankAccountWallet::class, 'default_bank_account_wallet_id');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'address_city_id');
    }

    public function deliveryCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'delivery_address_city_id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'address_state_id');
    }

    public function deliveryState(): BelongsTo
    {
        return $this->belongsTo(State::class, 'delivery_address_state_id');
    }

    public function customerHistories(): HasMany
    {
        return $this->hasMany(CustomerHistory::class);
    }

    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class);
    }

    public function collections(): HasMany
    {
        return $this->hasMany(Collection::class);
    }

    public function stockLocations(): HasMany
    {
        return $this->hasMany(StockLocation::class);
    }

    public function lendingStockAdjustments(): HasMany
    {
        return $this->hasMany(LendingStockAdjustment::class);
    }

    public function billings(): HasMany
    {
        return $this->hasMany(Billing::class);
    }

    public function integrationLogs(): MorphMany
    {
        return $this->morphMany(IntegrationLog::class, 'entity');
    }

    public function customerPaymentRecoveryLogs(): HasMany
    {
        return $this->hasMany(CustomerPaymentRecoveryLog::class);
    }

    public function customerCustomerTags(): HasMany
    {
        return $this->hasMany(CustomerCustomerTag::class);
    }

    public function receivables(): HasMany
    {
        return $this->hasMany(Receivable::class);
    }

    public function customerBasicStockStats(): HasMany
    {
        return $this->hasMany(CustomerBasicStockStats::class);
    }

    public function customerCoupons(): HasMany
    {
        return $this->hasMany(CustomerCoupon::class);
    }

    public function customerProducts(): HasMany
    {
        return $this->hasMany(CustomerProduct::class);
    }

    public function customerProductsReadjustments(): HasMany
    {
        return $this->hasMany(CustomerProductsReadjustment::class);
    }

    public function customerContacts(): HasMany
    {
        return $this->hasMany(CustomerContact::class);
    }
}
