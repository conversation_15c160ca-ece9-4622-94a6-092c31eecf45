<?php

namespace App\Models\Concerns\Customer;

use App\Enums\CustomerDefaultBillingPeriodEnum;

trait HandlesCustomerAttributes
{
    /**
     * Accessor for the "friendly tax ID number" attribute.
     *
     * @return string
     */
    public function getFriendlyTaxIdNumberAttribute(): string
    {
        return strlen($this->tax_id_number) === 11
            ? mask_cpf($this->tax_id_number)
            : mask_cnpj($this->tax_id_number);
    }

    /**
     * Accessor for the "friendly address zipcode" attribute.
     *
     * @return string
     */
    public function getFriendlyAddressZipcodeAttribute(): string
    {
        return mask_zipcode($this->address_zipcode);
    }

    /**
     * Accessor for the "friendly delivery address zipcode" attribute.
     *
     * @return string
     */
    public function getFriendlyDeliveryAddressZipcodeAttribute(): string
    {
        return mask_zipcode($this->delivery_address_zipcode);
    }

    /**
     * Accessor for the "long address" attribute.
     *
     * @return string
     */
    public function getLongAddressAttribute(): string
    {
        return $this->address_additional_info
            ? "$this->address_address, $this->address_number - $this->address_additional_info"
            : "$this->address_address, $this->address_number";
    }

    /**
     * Accessor for the "long delivery address" attribute.
     *
     * @return string
     */
    public function getLongDeliveryAddressAttribute(): string
    {
        return $this->delivery_address_additional_info
            ? "$this->delivery_address_address, $this->delivery_address_number - $this->delivery_address_additional_info"
            : "$this->delivery_address_address, $this->delivery_address_number";
    }

    /**
     * Mutator for the "tax ID number" attribute.
     *
     * @return void
     */
    public function setTaxIdNumberAttribute(mixed $value): void
    {
        $this->attributes['tax_id_number'] = get_numbers($value);
    }

    /**
     * Mutator for the "address zipcode" attribute.
     *
     * @return void
     */
    public function setAddressZipcodeAttribute(mixed $value): void
    {
        $this->attributes['address_zipcode'] = unmask_zipcode($value);
    }

    /**
     * Mutator for the "delivery address zipcode" attribute.
     *
     * @return void
     */
    public function setDeliveryAddressZipcodeAttribute(mixed $value): void
    {
        $this->attributes['delivery_address_zipcode'] = unmask_zipcode($value);
    }

    /**
     * Mutator for the "minimum billing amount" attribute.
     *
     * @return void
     */
    public function setMinimumBillingAmountAttribute(mixed $value): void
    {
        $this->attributes['minimum_billing_amount'] = unmask_money($value);
    }

    public function setStockSafetyPercentageAttribute(mixed $value): void
    {
        $this->attributes['stock_safety_percentage'] = unmask_percentage($value);
    }

    public function getFriendlyStockSafetyPercentageAttribute(): string
    {
        return mask_percentage($this->attributes['stock_safety_percentage']);
    }
}
