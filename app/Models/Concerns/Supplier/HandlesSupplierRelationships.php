<?php

namespace App\Models\Concerns\Supplier;

use App\Models\City;
use App\Models\Contract;
use App\Models\IntegrationLog;
use App\Models\State;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HandlesSupplierRelationships
{
    /**
     * Load the city relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'address_city_id');
    }

    /**
     * Load the state relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'address_state_id');
    }

    /**
     * Load the contracts relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function contracts(): MorphMany
    {
        return $this->morphMany(Contract::class, 'entity');
    }

    /**
     * Load the integration logs relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function integrationLogs(): MorphMany
    {
        return $this->morphMany(IntegrationLog::class, 'entity');
    }
}
