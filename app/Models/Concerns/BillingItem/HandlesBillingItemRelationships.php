<?php

namespace App\Models\Concerns\BillingItem;

use App\Models\Billing;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesBillingItemRelationships
{
    /**
     * Load the billing relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function billing(): BelongsTo
    {
        return $this->belongsTo(Billing::class);
    }

    /**
     * Load the document relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function document(): MorphTo
    {
        return $this->morphTo();
    }
}
