<?php

namespace App\Models\Concerns\CustomerPaymentRecoveryLog;

use App\Models\Customer;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesCustomerPaymentRecoveryLogRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the receivable relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function receivable(): BelongsTo
    {
        return $this->belongsTo(Receivable::class);
    }
}
