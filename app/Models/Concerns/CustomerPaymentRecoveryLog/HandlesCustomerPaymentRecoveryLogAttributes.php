<?php

namespace App\Models\Concerns\CustomerPaymentRecoveryLog;

use App\Enums\CustomerPaymentRecoveryLogTypeEnum;

trait HandlesCustomerPaymentRecoveryLogAttributes
{
    /**
     * Accessor for the "friendly type" attribute.
     *
     * @return string
     */
    public function getFriendlyTypeAttribute(): string
    {
        return CustomerPaymentRecoveryLogTypeEnum::getTranslated()[$this->type];
    }
}
