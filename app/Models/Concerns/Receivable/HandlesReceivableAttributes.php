<?php

namespace App\Models\Concerns\Receivable;

trait HandlesReceivableAttributes
{
    /**
     * Accessor for the "friendly updated amount" attribute.
     *
     * @return string
     */
    public function getFriendlyUpdatedAmountAttribute(): string
    {
        return mask_money($this->updated_amount);
    }

    /**
     * Mutator for the "updated amount" attribute.
     *
     * @return void
     */
    public function setUpdatedAmountAttribute(mixed $value)
    {
        $this->attributes['updated_amount'] = unmask_money($value);
    }
}
