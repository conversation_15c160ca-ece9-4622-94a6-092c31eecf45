<?php

namespace App\Models\Concerns\Receivable;

use App\Models\BankSlip;
use App\Models\Customer;
use App\Models\ReceivableEmail;
use App\Models\ReceivableSettlement;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesReceivableRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the document relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function document(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Load the bank slips relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function bankSlips(): HasMany
    {
        return $this->hasMany(BankSlip::class);
    }

    /**
     * Load the receivable settlements relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function receivableSettlements(): HasMany
    {
        return $this->hasMany(ReceivableSettlement::class);
    }

    /**
     * Load the receivable emails relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function receivableEmails(): HasMany
    {
        return $this->hasMany(ReceivableEmail::class);
    }
}
