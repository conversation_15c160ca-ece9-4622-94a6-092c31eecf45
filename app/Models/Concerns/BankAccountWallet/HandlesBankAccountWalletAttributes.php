<?php

namespace App\Models\Concerns\BankAccountWallet;

trait HandlesBankAccountWalletAttributes
{
    public function getFullNumberAttribute(): string
    {
        return "{$this->bankAccount->bank->code} - {$this->bankAccount->bank->name}"
            . " | ({$this->bankAccount->branch_number}-{$this->bankAccount->branch_digit} / "
            . "{$this->bankAccount->number}-{$this->bankAccount->digit}) - $this->number";
    }
}
