<?php

namespace App\Models\Concerns\CollectionReceiptEmail;

use App\Models\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesCollectionReceiptEmailRelationships
{
    /**
     * Load the collection relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }
}
