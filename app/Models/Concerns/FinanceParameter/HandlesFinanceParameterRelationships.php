<?php

namespace App\Models\Concerns\FinanceParameter;

use App\Models\PaymentMethod;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesFinanceParameterRelationships
{
    /**
     * Load the default cash payment method.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function defaultCashPaymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'default_cash_payment_method_id');
    }

    /**
     * Load the default bank slip payment method.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function defaultBankSlipPaymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'default_bank_slip_payment_method_id');
    }
}
