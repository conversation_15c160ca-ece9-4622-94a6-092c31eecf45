<?php

namespace App\Models\Concerns\CustomerProduct;

trait HandlesCustomerProductAttributes
{
    /**
     * Mutator for the "unit amount" attribute.
     *
     * @return void
     */
    public function setUnitAmountAttribute(mixed $value): void
    {
        $this->attributes['unit_amount'] = unmask_money($value);
    }

    /**
     * Mutator for the "total amount" attribute.
     *
     * @return void
     */
    public function setTotalAmountAttribute(mixed $value): void
    {
        $this->attributes['total_amount'] = unmask_money($value);
    }
}
