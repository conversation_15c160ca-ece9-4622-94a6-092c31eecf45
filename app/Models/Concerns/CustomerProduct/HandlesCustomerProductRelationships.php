<?php

namespace App\Models\Concerns\CustomerProduct;

use App\Models\Contract;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesCustomerProductRelationships
{
    /**
     * Load the contract relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Load the print product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function printProduct(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'print_product_id');
    }
}
