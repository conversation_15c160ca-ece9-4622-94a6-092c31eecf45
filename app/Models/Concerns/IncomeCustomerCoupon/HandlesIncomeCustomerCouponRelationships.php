<?php

namespace App\Models\Concerns\IncomeCustomerCoupon;

use App\Models\CustomerCoupon;
use App\Models\Income;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesIncomeCustomerCouponRelationships
{
    /**
     * Load the income relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function income(): BelongsTo
    {
        return $this->belongsTo(Income::class);
    }

    /**
     * Load the customer coupon relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customerCoupon(): BelongsTo
    {
        return $this->belongsTo(CustomerCoupon::class);
    }
}
