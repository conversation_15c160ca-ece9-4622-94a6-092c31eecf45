<?php

namespace App\Models\Concerns\PaymentMethod;

use App\Models\BankAccountWallet;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesPaymentMethodRelationships
{
    /**
     * Load the bank account wallet relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bankAccountWallet(): BelongsTo
    {
        return $this->belongsTo(BankAccountWallet::class);
    }
}
