<?php

namespace App\Models\Concerns\IncomeItem;

use App\Models\Income;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesIncomeItemRelationships
{
    /**
     * Load the income relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function income(): BelongsTo
    {
        return $this->belongsTo(Income::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
