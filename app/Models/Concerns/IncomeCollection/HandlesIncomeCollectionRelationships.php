<?php

namespace App\Models\Concerns\IncomeCollection;

use App\Models\Collection;
use App\Models\Income;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesIncomeCollectionRelationships
{
    /**
     * Load the income relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function income(): BelongsTo
    {
        return $this->belongsTo(Income::class);
    }

    /**
     * Load the collection relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }
}
