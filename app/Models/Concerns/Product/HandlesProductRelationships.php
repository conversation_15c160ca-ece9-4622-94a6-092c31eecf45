<?php

namespace App\Models\Concerns\Product;

use App\Models\StockLocationProduct;
use App\Models\Subcategory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesProductRelationships
{
    /**
     * Load the subcategory relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(Subcategory::class);
    }

    /**
     * Load the stock location products relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function stockLocationProducts(): HasMany
    {
        return $this->hasMany(StockLocationProduct::class);
    }
}
