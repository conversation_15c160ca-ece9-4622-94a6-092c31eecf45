<?php

namespace App\Models\Concerns\LendingStockAdjustmentItem;

use App\Models\LendingStockAdjustment;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesLendingStockAdjustmentItemRelationships
{
    /**
     * Load the lending stock adjustment relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function lendingStockAdjustment(): BelongsTo
    {
        return $this->belongsTo(LendingStockAdjustment::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
