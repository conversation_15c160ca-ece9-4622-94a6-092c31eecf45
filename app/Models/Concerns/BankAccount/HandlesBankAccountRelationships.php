<?php

namespace App\Models\Concerns\BankAccount;

use App\Models\Bank;
use App\Models\BankAccountWallet;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesBankAccountRelationships
{
    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    public function bankAccountWallets(): HasMany
    {
        return $this->hasMany(BankAccountWallet::class);
    }
}
