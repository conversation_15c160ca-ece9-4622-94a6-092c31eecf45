<?php

namespace App\Models\Concerns\TecnospeedPlugBoletoIntegrationLog;

use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesTecnospeedPlugBoletoIntegrationLogRelationships
{
    /**
     * Load the integratable relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function integratable(): MorphTo
    {
        return $this->morphTo();
    }
}
