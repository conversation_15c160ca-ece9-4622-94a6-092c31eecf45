<?php

namespace App\Models\Concerns\CustomerCoupon;

use App\Models\IncomeCustomerCoupon;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCustomerCouponRelationships
{
    /**
     * Load the income customer coupons relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function incomeCustomerCoupons(): HasMany
    {
        return $this->hasMany(IncomeCustomerCoupon::class);
    }
}
