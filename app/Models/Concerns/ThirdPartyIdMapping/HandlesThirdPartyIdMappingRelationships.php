<?php

namespace App\Models\Concerns\ThirdPartyIdMapping;

use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesThirdPartyIdMappingRelationships
{
    /**
     * Load the model relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Load the integration relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function integration(): MorphTo
    {
        return $this->morphTo();
    }
}
