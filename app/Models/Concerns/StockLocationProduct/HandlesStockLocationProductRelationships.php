<?php

namespace App\Models\Concerns\StockLocationProduct;

use App\Models\Product;
use App\Models\StockLocation;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesStockLocationProductRelationships
{
    /**
     * Load the stock location relationship.
     *
     * @return BelongsTo
     */
    public function stockLocation(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class);
    }

    /**
     * Load the product relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
