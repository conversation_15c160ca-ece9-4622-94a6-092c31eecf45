<?php

namespace App\Models\Concerns\StockLocation;

use App\Models\Customer;
use App\Models\StockLocationProduct;
use App\Models\StockNature;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesStockLocationRelationships
{
    /**
     * Load the stock nature relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function stockNature(): BelongsTo
    {
        return $this->belongsTo(StockNature::class);
    }

    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the stock location products relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function stockLocationProducts(): HasMany
    {
        return $this->hasMany(StockLocationProduct::class);
    }
}
