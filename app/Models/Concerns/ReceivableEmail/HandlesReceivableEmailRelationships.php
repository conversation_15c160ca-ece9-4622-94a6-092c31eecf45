<?php

namespace App\Models\Concerns\ReceivableEmail;

use App\Models\Receivable;
use App\Models\ReceivableEmailInteraction;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesReceivableEmailRelationships
{
    public function receivable(): BelongsTo
    {
        return $this->belongsTo(Receivable::class);
    }

    public function receivableEmailInteractions(): HasMany
    {
        return $this->hasMany(ReceivableEmailInteraction::class);
    }
}
