<?php

namespace App\Models\Concerns\CustomerHistory;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

trait HandlesCustomerHistoryRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the operation relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function operation(): MorphTo
    {
        return $this->morphTo();
    }
}
