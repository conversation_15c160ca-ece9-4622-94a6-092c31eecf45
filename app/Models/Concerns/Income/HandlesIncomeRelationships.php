<?php

namespace App\Models\Concerns\Income;

use App\Models\Customer;
use App\Models\IncomeCollection;
use App\Models\IncomeCustomerCoupon;
use App\Models\IncomeItem;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HandlesIncomeRelationships
{
    /**
     * Load the customer relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Load the income items relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function incomeItems(): HasMany
    {
        return $this->hasMany(IncomeItem::class);
    }

    /**
     * Load the income collections relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function incomeCollections(): HasMany
    {
        return $this->hasMany(IncomeCollection::class);
    }

    /**
     * Load the income customer coupons relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function incomeCustomerCoupons(): HasMany
    {
        return $this->hasMany(IncomeCustomerCoupon::class);
    }

    /**
     * Load the receivables relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function receivables(): MorphMany
    {
        return $this->morphMany(Receivable::class, 'document');
    }
}
