<?php

namespace App\Models\Concerns\DeliveryReceiptEmail;

use App\Models\Delivery;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesDeliveryReceiptEmailRelationships
{
    /**
     * Load the delivery relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }
}
