<?php

namespace App\Models\Concerns\BankSlipUpdateHistory;

use App\Models\BankSlip;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesBankSlipUpdateHistoryRelationships
{
    /**
     * Load the bank slip relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bankSlip(): BelongsTo
    {
        return $this->belongsTo(BankSlip::class);
    }
}
