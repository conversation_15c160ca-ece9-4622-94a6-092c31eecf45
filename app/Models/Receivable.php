<?php

namespace App\Models;

use App\Models\Concerns\Receivable\HandlesReceivableAttributes;
use App\Models\Concerns\Receivable\HandlesReceivableRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Receivable model.
 *
 * @package App\Models
 * @property int $id
 * @property string $omie_id
 * @property int $customer_id
 * @property int $document_id
 * @property string $document_type
 * @property int $sequence
 * @property float $original_amount
 * @property float $pis_wht_amount
 * @property float $cofins_wht_amount
 * @property float $csll_wht_amount
 * @property float $irrf_wht_amount
 * @property float $inss_wht_amount
 * @property float $iss_wht_amount
 * @property float $addition_amount
 * @property float $discount_amount
 * @property float $updated_amount
 * @property string $status
 * @property bool $email_sent
 * @property int $collection_count
 * @property \Carbon\Carbon $issued_at
 * @property \Carbon\Carbon $expires_at
 * @property \Carbon\Carbon $settled_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 * @property string $friendly_updated_amount
 * @property \App\Models\Customer $customer
 * @property \App\Models\Income $document
 * @property \Illuminate\Support\Collection|\App\Models\BankSlip[] $bankSlips
 * @property \Illuminate\Support\Collection|\App\Models\ReceivableSettlement[] $receivableSettlements
 * @property \Illuminate\Support\Collection|\App\Models\ReceivableEmail[] $receivableEmails
 * @property-read int|null $bank_slips_count
 * @property-read int|null $receivable_emails_count
 * @property-read int|null $receivable_settlements_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Receivable withoutTrashed()
 * @mixin \Eloquent
 */
class Receivable extends Model
{
    use HandlesReceivableAttributes;
    use HandlesReceivableRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'omie_id',
        'customer_id',
        'document_id',
        'document_type',
        'sequence',
        'original_amount',
        'pis_wht_amount',
        'cofins_wht_amount',
        'csll_wht_amount',
        'irrf_wht_amount',
        'inss_wht_amount',
        'iss_wht_amount',
        'addition_amount',
        'discount_amount',
        'updated_amount',
        'status',
        'email_sent',
        'collection_count',
        'issued_at',
        'expires_at',
        'settled_at',
        'cancelled_at',
    ];
}
