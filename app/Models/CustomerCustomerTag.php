<?php

namespace App\Models;

use App\Models\Concerns\CustomerCustomerTag\HandlesCustomerCustomerTagRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer customer tag model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $customer_tag_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\CustomerTag $customerTag
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerCustomerTag newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerCustomerTag newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerCustomerTag query()
 * @mixin \Eloquent
 */
class CustomerCustomerTag extends Model
{
    use HandlesCustomerCustomerTagRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'customer_tag_id',
    ];
}
