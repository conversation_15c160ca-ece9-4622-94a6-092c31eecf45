<?php

namespace App\Models;

use App\Models\Concerns\IncomeCollection\HandlesIncomeCollectionRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Income collection model.
 *
 * @package App\Models
 * @property int $id
 * @property int $income_id
 * @property int $collection_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Income $income
 * @property \App\Models\Collection $collection
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeCollection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeCollection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IncomeCollection query()
 * @mixin \Eloquent
 */
class IncomeCollection extends Model
{
    use HandlesIncomeCollectionRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'income_id',
        'collection_id',
    ];
}
