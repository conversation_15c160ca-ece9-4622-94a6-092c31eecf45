<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Tecnospeed PlugBoleto integration log.
 *
 * @package App\Models
 * @property int $id
 * @property int $integratable_id
 * @property string $integratable_type
 * @property array $response
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Receivable $integratable
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugBoletoIntegrationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugBoletoIntegrationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugBoletoIntegrationLog query()
 * @mixin \Eloquent
 */
class TecnospeedPlugBoletoIntegrationLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'integratable_id',
        'integratable_type',
        'response',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'response' => 'array',
    ];
}
