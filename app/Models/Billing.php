<?php

namespace App\Models;

use App\Models\Concerns\Billing\HandlesBillingRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Billing model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $contract_id
 * @property float $original_amount
 * @property float $pis_wht_amount
 * @property float $cofins_wht_amount
 * @property float $csll_wht_amount
 * @property float $irrf_wht_amount
 * @property float $inss_wht_amount
 * @property float $iss_wht_amount
 * @property float $addition_amount
 * @property float $discount_amount
 * @property float $updated_amount
 * @property \Carbon\Carbon $billed_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\Contract $contract
 * @property \Illuminate\Support\Collection|\App\Models\BillingItem[] $billingItems
 * @property-read int|null $billing_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Receivable> $receivables
 * @property-read int|null $receivables_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Billing newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Billing newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Billing query()
 * @mixin \Eloquent
 */
class Billing extends Model
{
    use HandlesBillingRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'contract_id',
        'original_amount',
        'pis_wht_amount',
        'cofins_wht_amount',
        'csll_wht_amount',
        'irrf_wht_amount',
        'inss_wht_amount',
        'iss_wht_amount',
        'addition_amount',
        'discount_amount',
        'updated_amount',
        'billed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'original_amount' => 'float',
        'pis_wht_amount' => 'float',
        'cofins_wht_amount' => 'float',
        'csll_wht_amount' => 'float',
        'irrf_wht_amount' => 'float',
        'inss_wht_amount' => 'float',
        'iss_wht_amount' => 'float',
        'addition_amount' => 'float',
        'discount_amount' => 'float',
        'updated_amount' => 'float'
    ];
}
