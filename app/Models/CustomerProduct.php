<?php

namespace App\Models;

use App\Models\Concerns\CustomerProduct\HandlesCustomerProductAttributes;
use App\Models\Concerns\CustomerProduct\HandlesCustomerProductRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Customer product model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  int $product_id
 * @property  int $print_product_id
 * @property  float $quantity
 * @property  float $unit_amount
 * @property  float $total_amount
 * @property  bool $visible_in_collections
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Product $product
 */
class CustomerProduct extends Model
{
    use HandlesCustomerProductAttributes;
    use HandlesCustomerProductRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'product_id',
        'print_product_id',
        'quantity',
        'unit_amount',
        'total_amount',
        'visible_in_collections',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'float',
        'unit_amount' => 'float',
        'total_amount' => 'float',
    ];
}
