<?php

namespace App\Models;

use App\Models\Concerns\IntegrationLog\HandlesIntegrationLogRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Integration log model.
 *
 * @package App\Models
 * @property int $id
 * @property int $integration_id
 * @property string $integration_type
 * @property int $entity_id
 * @property string $entity_type
 * @property string $data_flow
 * @property string $endpoint
 * @property array $data
 * @property string $message
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $entity
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $integration
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IntegrationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IntegrationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\IntegrationLog query()
 * @mixin \Eloquent
 */
class IntegrationLog extends Model
{
    use HandlesIntegrationLogRelationships;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'integration_id',
        'integration_type',
        'entity_id',
        'entity_type',
        'data_flow',
        'endpoint',
        'data',
        'message'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'data' => 'array'
    ];
}
