<?php

namespace App\Models;

use App\Models\Concerns\FinanceParameter\HandlesFinanceParameterRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Finance parameter model.
 *
 * @package App\Models
 * @property int $id
 * @property int $default_cash_payment_method_id
 * @property int $default_bank_slip_payment_method_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\PaymentMethod $defaultCashPaymentMethod
 * @property \App\Models\PaymentMethod $defaultBankSlipPaymentMethod
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\FinanceParameter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\FinanceParameter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\FinanceParameter query()
 * @mixin \Eloquent
 */
class FinanceParameter extends Model
{
    use HandlesFinanceParameterRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'default_cash_payment_method_id',
        'default_bank_slip_payment_method_id',
    ];
}
