<?php

namespace App\Models;

use App\Models\Concerns\StockLocationProduct\HandlesStockLocationProductRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Stock location product model.
 *
 * @package App\Models
 * @property int $id
 * @property int $stock_location_id
 * @property int $product_id
 * @property float $initial_quantity
 * @property float $current_quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\StockLocation $stockLocation
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocationProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocationProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockLocationProduct query()
 * @mixin \Eloquent
 */
class StockLocationProduct extends Model
{
    use HandlesStockLocationProductRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'stock_location_id',
        'product_id',
        'initial_quantity',
        'current_quantity'
    ];
}
