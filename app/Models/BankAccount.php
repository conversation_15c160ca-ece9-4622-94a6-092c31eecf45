<?php

namespace App\Models;

use App\Models\Concerns\BankAccount\HandlesBankAccountRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Bank account model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $bank_id
 * @property  string $number
 * @property  string $digit
 * @property  string $branch_number
 * @property  string $branch_digit
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property \App\Models\Bank $bank
 * @property \Illuminate\Support\Collection|\App\Models\BankAccountWallet[] $bankAccountWallets
 */
class BankAccount extends Model
{
    use HandlesBankAccountRelationships;
    use HasFactory;

    protected $fillable = [
        'bank_id',
        'number',
        'digit',
        'branch_number',
        'branch_digit',
        'active'
    ];
}
