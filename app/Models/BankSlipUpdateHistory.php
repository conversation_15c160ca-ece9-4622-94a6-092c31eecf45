<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Bank slip update history model.
 *
 * @package App\Models
 * @property int $id
 * @property int $bank_slip_id
 * @property string $protocol
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\BankSlip $bankSlip
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlipUpdateHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlipUpdateHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BankSlipUpdateHistory query()
 * @mixin \Eloquent
 */
class BankSlipUpdateHistory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'bank_slip_id',
        'protocol',
    ];
}
