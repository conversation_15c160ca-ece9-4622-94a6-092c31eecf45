<?php

namespace App\Models;

use App\Actions\Core\CalculateAddressLatitudeLongitude;
use App\Models\Concerns\Customer\HandlesCustomerAttributes;
use App\Models\Concerns\Customer\HandlesCustomerRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Customer model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $omie_id
 * @property  int $salesman_id
 * @property  int $customer_group_id
 * @property  int $payment_recovery_setting_id
 * @property  int $index_id
 * @property  int $default_bank_account_wallet_id
 * @property  string $name
 * @property  string $trading_name
 * @property  string $tax_id_number
 * @property  bool $exempt_from_state_registration
 * @property  string $state_registration
 * @property  string $city_registration
 * @property  string $email
 * @property  string $billing_email
 * @property  string $billing_phone
 * @property  string $operation_email
 * @property  string $phone_1
 * @property  string $in_charge_person_1
 * @property  string $phone_2
 * @property  string $in_charge_person_2
 * @property  string $address_zipcode
 * @property  string $address_address
 * @property  string $address_number
 * @property  string $address_additional_info
 * @property  string $address_district
 * @property  int $address_city_id
 * @property  int $address_state_id
 * @property  string $latitude
 * @property  string $longitude
 * @property  string $delivery_address_zipcode
 * @property  string $delivery_address_address
 * @property  string $delivery_address_number
 * @property  string $delivery_address_additional_info
 * @property  string $delivery_address_district
 * @property  int $delivery_address_city_id
 * @property  int $delivery_address_state_id
 * @property  string $specific_latitude
 * @property  string $specific_longitude
 * @property  string $preferred_charging_method
 * @property  int $previous_collection_count
 * @property  float $minimum_billing_amount
 * @property  int $default_due_day
 * @property  string $default_billing_period
 * @property  string $default_tax_condition
 * @property  string $default_delivery_model
 * @property  float $stock_safety_percentage
 * @property  int $payment_recovery_day_count
 * @property  bool $active
 * @property  \Carbon\Carbon $service_started_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_tax_id_number
 * @property  string $friendly_address_zipcode
 * @property  string $long_address
 *
 * @property  \App\Models\Salesman $salesman
 * @property  \App\Models\CustomerGroup $customerGroup
 * @property  \App\Models\PaymentRecoverySetting $paymentRecoverySetting
 * @property  \App\Models\Index $index
 * @property  \App\Models\BankAccountWallet $defaultBankAccountWallet
 * @property  \App\Models\City $city
 * @property  \App\Models\State $state
 * @property  \App\Models\City $deliveryCity
 * @property  \App\Models\State $deliveryState
 * @property  \App\Models\Delivery|null $lastDelivery
 *
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerHistory[] $customerHistories
 * @property  \Illuminate\Support\Collection|\App\Models\Delivery[] $deliveries
 * @property  \Illuminate\Support\Collection|\App\Models\Collection[] $collections
 * @property  \Illuminate\Support\Collection|\App\Models\StockLocation[] $stockLocations
 * @property  \Illuminate\Support\Collection|\App\Models\LendingStockAdjustment[] $lendingStockAdjustments
 * @property  \Illuminate\Support\Collection|\App\Models\Billing[] $billings
 * @property  \Illuminate\Support\Collection|\App\Models\IntegrationLog[] $integrationLogs
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerPaymentRecoveryLog[] $customerPaymentRecoveryLogs
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerCustomerTag[] $customerCustomerTags
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerBasicStockStats[] $customerBasicStockStats
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerCoupon[] $customerCoupons
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerProduct[] $customerProducts
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerProductsReadjustment[] $customerProductsReadjustments
 */
class Customer extends Model
{
    use HandlesCustomerAttributes;
    use HandlesCustomerRelationships;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'omie_id',
        'salesman_id',
        'customer_group_id',
        'payment_recovery_setting_id',
        'index_id',
        'default_bank_account_wallet_id',
        'name',
        'trading_name',
        'tax_id_number',
        'exempt_from_state_registration',
        'state_registration',
        'city_registration',
        'email',
        'billing_email',
        'billing_phone',
        'operation_email',
        'phone_1',
        'in_charge_person_1',
        'phone_2',
        'in_charge_person_2',
        'address_zipcode',
        'address_address',
        'address_number',
        'address_additional_info',
        'address_district',
        'address_city_id',
        'address_state_id',
        'latitude',
        'longitude',
        'delivery_address_zipcode',
        'delivery_address_address',
        'delivery_address_number',
        'delivery_address_additional_info',
        'delivery_address_district',
        'delivery_address_city_id',
        'delivery_address_state_id',
        'specific_latitude',
        'specific_longitude',
        'preferred_charging_method',
        'previous_collection_count',
        'minimum_billing_amount',
        'default_due_day',
        'default_billing_period',
        'default_tax_condition',
        'default_delivery_model',
        'stock_safety_percentage',
        'payment_recovery_day_count',
        'active',
        'service_started_at',
    ];

    protected $appends = [
        'long_address',
        'long_delivery_address',
        'friendly_address_zipcode',
        'friendly_delivery_address_zipcode',
    ];

    protected $casts = [
        'payment_recovery_day_count' => 'int',
    ];

    protected static function booted(): void
    {
        static::saving(function (self $customer) {
            if (is_null($customer->state_registration)) {
                $customer->state_registration = 'ISENTO';
            }

            if ($customer->previous_collection_count < 0) {
                $customer->previous_collection_count = 0;
            }

            $latitudeLongitude = CalculateAddressLatitudeLongitude::run(
                $customer->delivery_address_address ?? $customer->address_address,
                $customer->delivery_address_number ?? $customer->address_number,
                $customer->delivery_address_district ?? $customer->address_district,
                $customer->deliveryCity?->name ?? $customer->city->name,
                $customer->deliveryState?->abbreviation ?? $customer->state->abbreviation,
            );

            $customer->latitude = $latitudeLongitude['latitude'];
            $customer->longitude = $latitudeLongitude['longitude'];
        });
    }
}
