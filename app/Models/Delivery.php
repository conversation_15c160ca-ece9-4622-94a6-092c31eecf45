<?php

namespace App\Models;

use App\Models\Concerns\Delivery\HandlesDeliveryRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Delivery model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  string $additional_info
 * @property  \Carbon\Carbon $delivered_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\DeliveryBatchDelivery $deliveryBatchDelivery
 *
 * @property  \Illuminate\Support\Collection|\App\Models\DeliveryItem[] $deliveryItems
 * @property  \Illuminate\Support\Collection|\App\Models\DeliveryReceiptEmail[] $deliveryReceiptEmails
 */
class Delivery extends Model
{
    use HandlesDeliveryRelationships;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'customer_id',
        'additional_info',
        'delivered_at'
    ];
}
