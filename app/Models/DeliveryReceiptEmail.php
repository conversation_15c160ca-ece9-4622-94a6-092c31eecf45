<?php

namespace App\Models;

use App\Models\Concerns\DeliveryReceiptEmail\HandlesDeliveryReceiptEmailRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Delivery receipt email model.
 *
 * @package App\Models
 * @property int $id
 * @property int $delivery_id
 * @property array $to_emails
 * @property string $message
 * @property \Carbon\Carbon $email_sent_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Delivery $delivery
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryReceiptEmail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryReceiptEmail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliveryReceiptEmail query()
 * @mixin \Eloquent
 */
class DeliveryReceiptEmail extends Model
{
    use HandlesDeliveryReceiptEmailRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'delivery_id',
        'to_emails',
        'message',
        'email_sent_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'to_emails' => 'array'
    ];
}
