<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Customer products readjustment item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_products_readjustment_id
 * @property  int $customer_product_id
 * @property  int $index_id
 * @property  float $old_amount
 * @property  float $new_amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\CustomerProductsReadjustment $customerProductsReadjustment
 * @property  \App\Models\CustomerProduct $customerProduct
 * @property  \App\Models\Index $index
 */
class CustomerProductsReadjustmentItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_products_readjustment_id',
        'customer_product_id',
        'index_id',
        'old_amount',
        'new_amount',
    ];

    public function customerProductsReadjustment(): BelongsTo
    {
        return $this->belongsTo(CustomerProductsReadjustment::class);
    }

    public function customerProduct(): BelongsTo
    {
        return $this->belongsTo(CustomerProduct::class);
    }

    public function index(): BelongsTo
    {
        return $this->belongsTo(Index::class);
    }
}
