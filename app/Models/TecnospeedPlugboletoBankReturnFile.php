<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Tecnospeed PlugBoleto bank return file model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $path
 * @property string $protocol
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoBankReturnFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoBankReturnFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoBankReturnFile query()
 * @mixin \Eloquent
 */
class TecnospeedPlugboletoBankReturnFile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'path',
        'protocol',
    ];
}
