<?php

namespace App\Models;

use App\Models\Concerns\CustomerContactType\HandlesCustomerContactTypeRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer contact type model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_contact_id
 * @property  string $type
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\CustomerContact $customerContact
 */
class CustomerContactType extends Model
{
    use HandlesCustomerContactTypeRelationships;

    protected $fillable = [
        'customer_contact_id',
        'type'
    ];
}
