<?php

namespace App\Models;

use App\Core\Models\Interfaces\HandlesSearchableSelectData;
use App\Models\Concerns\Product\HandlesProductAttributes;
use App\Models\Concerns\Product\HandlesProductRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * Product model.
 *
 * @package App\Models
 * @property int $id
 * @property int $subcategory_id
 * @property string $code
 * @property string $name
 * @property string $description
 * @property float $gross_weight
 * @property float $net_weight
 * @property float $default_price
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_default_price
 *
 * @property \App\Models\Subcategory $subcategory
 *
 * @property \Illuminate\Support\Collection|\App\Models\StockLocationProduct[] $stockLocationProducts
 */
class Product extends Model implements HandlesSearchableSelectData
{
    use HandlesProductAttributes;
    use HandlesProductRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'subcategory_id',
        'code',
        'name',
        'description',
        'gross_weight',
        'net_weight',
        'default_price',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'gross_weight' => 'float',
        'net_weight' => 'float',
        'default_price' => 'float',
    ];

    /**
     * @inheritDoc
     */
    public static function pluckForSearchableSelect(?string $search): Collection
    {
        return self::query()
            ->where('code', $search)
            ->orWhere('name', 'like', "%$search%")
            ->pluck('name', 'id');
    }
}
