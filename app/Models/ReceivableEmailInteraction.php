<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Receivable email interaction model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $receivable_email_id
 * @property  string $event
 * @property  string $recipient
 * @property  int $timestamp
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ReceivableEmail $receivableEmail
 */
class ReceivableEmailInteraction extends Model
{
    protected $fillable = [
        'receivable_email_id',
        'event',
        'recipient',
        'timestamp',
    ];
}
