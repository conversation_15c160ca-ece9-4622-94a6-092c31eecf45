<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Index revision model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $index_id
 * @property  float $old_amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_old_amount
 * @property  string $friendly_created_at
 *
 * @property  \App\Models\Index $index
 */
class IndexRevision extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'index_id',
        'old_amount',
    ];

    /**
     * Mutator for the "old amount" attribute.
     *
     * @return void
     */
    public function setOldAmountAttribute(mixed $value)
    {
        $this->attributes['old_amount'] = unmask_percentage($value);
    }

    /**
     * Accessor for the "friendly old amount" attribute.
     *
     * @return string
     */
    public function getFriendlyOldAmountAttribute(): string
    {
        return mask_percentage($this->old_amount);
    }

    /**
     * Accessor for the "friendly created at" attribute.
     *
     * @return string
     */
    public function getFriendlyCreatedAtAttribute(): string
    {
        return format_date($this->created_at);
    }

    /**
     * Load the index relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function index(): BelongsTo
    {
        return $this->belongsTo(Index::class);
    }
}
