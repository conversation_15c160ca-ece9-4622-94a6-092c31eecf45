<?php

namespace App\Models;

use App\Models\Concerns\LendingStockAdjustmentItem\HandlesLendingStockAdjustmentItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Lending stock adjustment item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $lending_stock_adjustment_id
 * @property int $product_id
 * @property float $old_stock_quantity
 * @property float $collected_quantity
 * @property float $delivered_quantity
 * @property float $out_of_movement_quantity
 * @property float $adjustment_quantity
 * @property float $current_stock_quantity_before_update
 * @property float $current_stock_quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\LendingStockAdjustment $lendingStockAdjustment
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustmentItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustmentItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustmentItem query()
 * @mixin \Eloquent
 */
class LendingStockAdjustmentItem extends Model
{
    use HandlesLendingStockAdjustmentItemRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lending_stock_adjustment_id',
        'product_id',
        'old_stock_quantity',
        'collected_quantity',
        'delivered_quantity',
        'out_of_movement_quantity',
        'adjustment_quantity',
        'current_stock_quantity_before_update',
        'current_stock_quantity'
    ];
}
