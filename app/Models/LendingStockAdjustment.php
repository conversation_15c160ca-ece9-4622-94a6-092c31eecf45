<?php

namespace App\Models;

use App\Models\Concerns\LendingStockAdjustment\HandlesLendingStockAdjustmentRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Lending stock adjustment model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property string $additional_info
 * @property \Carbon\Carbon $adjusted_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \Illuminate\Support\Collection|\App\Models\LendingStockAdjustmentItem[] $lendingStockAdjustmentItems
 * @property \Illuminate\Support\Collection|\App\Models\CustomerHistory[] $customerHistories
 * @property-read int|null $customer_histories_count
 * @property-read int|null $lending_stock_adjustment_items_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\LendingStockAdjustment query()
 * @mixin \Eloquent
 */
class LendingStockAdjustment extends Model
{
    use HandlesLendingStockAdjustmentRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'additional_info',
        'adjusted_at',
    ];
}
