<?php

namespace App\Models;

use App\Models\Concerns\BankAccountWallet\HandlesBankAccountWalletAttributes;
use App\Models\Concerns\BankAccountWallet\HandlesBankAccountWalletRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Bank account wallet.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $erp_flex_id
 * @property  int $omie_id
 * @property  int $bank_account_id
 * @property  string $number
 * @property  string $drawee_tax_id_number
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $full_number
 *
 * @property  \App\Models\BankAccount $bankAccount
 */
class BankAccountWallet extends Model
{
    use HandlesBankAccountWalletAttributes;
    use HandlesBankAccountWalletRelationships;
    use HasFactory;

    protected $fillable = [
        'omie_id',
        'bank_account_id',
        'number',
        'drawee_tax_id_number',
        'active',
    ];
}
