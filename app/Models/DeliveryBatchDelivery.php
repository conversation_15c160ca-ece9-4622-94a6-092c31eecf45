<?php

namespace App\Models;

use App\Models\Concerns\DeliveryBatchDelivery\HandlesDeliveryBatchDeliveryAttributes;
use App\Models\Concerns\DeliveryBatchDelivery\HandlesDeliveryBatchDeliveryRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Delivery batch delivery model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $delivery_batch_id
 * @property  int $customer_id
 * @property  int $collection_id
 * @property  \Carbon\Carbon $delivery_reference_date
 * @property  int $delivery_id
 * @property  string $itinerary
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_delivery_reference_date
 *
 * @property  \App\Models\DeliveryBatch $deliveryBatch
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Delivery $delivery
 */
class DeliveryBatchDelivery extends Model
{
    use HandlesDeliveryBatchDeliveryAttributes;
    use HandlesDeliveryBatchDeliveryRelationships;
    use HasFactory;

    protected $fillable = [
        'delivery_batch_id',
        'customer_id',
        'collection_id',
        'delivery_reference_date',
        'delivery_id',
        'itinerary',
    ];
}
