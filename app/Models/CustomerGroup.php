<?php

namespace App\Models;

use App\Models\Concerns\CustomerGroup\HandlesCustomerGroupRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer group model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Illuminate\Support\Collection|\App\Models\Customer[] $customers
 * @property-read int|null $customers_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CustomerGroup query()
 * @mixin \Eloquent
 */
class CustomerGroup extends Model
{
    use HandlesCustomerGroupRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];
}
