<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Payment recovery setting model.
 *
 * @package App\Models
 * @property int $id
 * @property int $overdue_day_count_from
 * @property int $overdue_day_count_to
 * @property string $email_template
 * @property string $whatsapp_template
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentRecoverySetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentRecoverySetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PaymentRecoverySetting query()
 * @mixin \Eloquent
 */
class PaymentRecoverySetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'overdue_day_count_from',
        'overdue_day_count_to',
        'email_template',
        'whatsapp_template',
    ];
}
