<?php

namespace App\Models;

use App\Models\Concerns\Income\HandlesIncomeRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Income model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property string $document_number
 * @property float $amount
 * @property string $additional_info
 * @property \Carbon\Carbon $issued_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 * @property \App\Models\Customer $customer
 * @property \Illuminate\Support\Collection|\App\Models\IncomeItem[] $incomeItems
 * @property \Illuminate\Support\Collection|\App\Models\IncomeCollection[] $incomeCollections
 * @property \Illuminate\Support\Collection|\App\Models\IncomeCustomerCoupon[] $incomeCustomerCoupons
 * @property \Illuminate\Support\Collection|\App\Models\Receivable[] $receivables
 * @property-read int|null $income_collections_count
 * @property-read int|null $income_items_count
 * @property-read int|null $receivables_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Income withoutTrashed()
 * @mixin \Eloquent
 */
class Income extends Model
{
    use HandlesIncomeRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'document_number',
        'amount',
        'additional_info',
        'issued_at',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'float',
    ];
}
