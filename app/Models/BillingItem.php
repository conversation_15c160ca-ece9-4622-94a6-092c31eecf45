<?php

namespace App\Models;

use App\Models\Concerns\BillingItem\HandlesBillingItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Billing item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $billing_id
 * @property int $document_id
 * @property string $document_type
 * @property float $quantity
 * @property float $unit_amount
 * @property float $total_amount
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Billing $billing
 * @property \App\Models\Collection $document
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BillingItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BillingItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BillingItem query()
 * @mixin \Eloquent
 */
class BillingItem extends Model
{
    use HandlesBillingItemRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'billing_id',
        'document_id',
        'document_type',
        'quantity',
        'unit_amount',
        'total_amount'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'float',
        'unit_amount' => 'float',
        'total_amount' => 'float'
    ];
}
