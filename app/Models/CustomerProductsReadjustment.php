<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Customer products readjustment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $customer_id
 * @property  int $operator_id
 * @property  int $index_id
 * @property  int $batch_no
 * @property  int $referring_month
 * @property  int $referring_year
 * @property  float $old_amount
 * @property  float $new_amount
 * @property  \Carbon\Carbon $old_ended_at
 * @property  \Carbon\Carbon $new_ended_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_old_amount
 * @property  string $friendly_new_amount
 * @property  string $friendly_created_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Operator $operator
 * @property  \App\Models\Index $index
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerProductsReadjustmentItem[] $customerProductsReadjustmentItems
 */
class CustomerProductsReadjustment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'operator_id',
        'index_id',
        'batch_no',
        'referring_month',
        'referring_year',
        'old_amount',
        'new_amount',
        'old_ended_at',
        'new_ended_at',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function customerProductsReadjustmentItems(): HasMany
    {
        return $this->hasMany(CustomerProductsReadjustmentItem::class);
    }
}
