<?php

namespace App\Models;

use App\Models\Concerns\StockMovementSummary\HandlesStockMovementSummaryAttributes;
use App\Models\Concerns\StockMovementSummary\HandlesStockMovementSummaryRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Stock movement summary model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $product_id
 * @property \Carbon\Carbon $movement_date
 * @property float $previous_stock_quantity
 * @property float $collected_quantity
 * @property float $delivered_quantity
 * @property float $out_of_movement_quantity
 * @property float $adjustment_quantity
 * @property float $stock_quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\Product $product
 * @property-read string $friendly_movement_date
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockMovementSummary newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockMovementSummary newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockMovementSummary query()
 * @mixin \Eloquent
 */
class StockMovementSummary extends Model
{
    use HandlesStockMovementSummaryAttributes;
    use HandlesStockMovementSummaryRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'product_id',
        'movement_date',
        'previous_stock_quantity',
        'collected_quantity',
        'delivered_quantity',
        'out_of_movement_quantity',
        'adjustment_quantity',
        'stock_quantity',
    ];
}
