<?php

namespace App\Models;

use App\Models\Concerns\Bank\HandlesBankRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Bank model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $code
 * @property  string $name
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\BankAccount[] $bankAccounts
 */
class Bank extends Model
{
    use HandlesBankRelationships;
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'active'
    ];
}
