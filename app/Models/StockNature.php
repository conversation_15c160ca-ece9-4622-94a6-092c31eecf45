<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Stock nature model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $location_type
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockNature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockNature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StockNature query()
 * @mixin \Eloquent
 */
class StockNature extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'location_type'
    ];
}
