<?php

namespace App\Models;

use App\Core\Models\Interfaces\HandlesSearchableSelectData;
use App\Models\Concerns\Salesman\HandlesSalesmanRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * Salesman model.
 *
 * @package App\Models
 * @property int $id
 * @property int $operator_id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Operator $operator
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Salesman newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Salesman newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Salesman query()
 * @mixin \Eloquent
 */
class Salesman extends Model implements HandlesSearchableSelectData
{
    use HandlesSalesmanRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'operator_id',
        'name'
    ];

    /**
     * @inheritDoc
     */
    public static function pluckForSearchableSelect(?string $search): Collection
    {
        return self::query()
            ->where('name', 'like', "%$search%")
            ->orderBy('name')
            ->pluck('name', 'id');
    }
}
