<?php

namespace App\Models;

/**
 * Permission model.
 *
 * @package App\Models
 */
class Permission extends \Spatie\Permission\Models\Permission
{
    public const GET_ROLES = 'get_roles';
    public const CREATE_ROLES = 'create_roles';
    public const UPDATE_ROLES = 'update_roles';
    public const DELETE_ROLES = 'delete_roles';

    public const GET_OPERATORS = 'get_operators';
    public const CREATE_OPERATORS = 'create_operators';
    public const UPDATE_OPERATORS = 'update_operators';
    public const DELETE_OPERATORS = 'delete_operators';

    public const GET_BANKS = 'get_banks';
    public const CREATE_BANKS = 'create_banks';
    public const UPDATE_BANKS = 'update_banks';
    public const DELETE_BANKS = 'delete_banks';

    public const GET_BANK_SLIPS = 'get_bank_slips';
    public const CREATE_BANK_SLIPS = 'create_bank_slips';
    public const UPDATE_BANK_SLIPS = 'update_bank_slips';
    public const DELETE_BANK_SLIPS = 'delete_bank_slips';

    public const GET_CATEGORIES = 'get_categories';
    public const CREATE_CATEGORIES = 'create_categories';
    public const UPDATE_CATEGORIES = 'update_categories';
    public const DELETE_CATEGORIES = 'delete_categories';

    public const GET_COLLECTIONS = 'get_collections';
    public const CREATE_COLLECTIONS = 'create_collections';
    public const UPDATE_COLLECTIONS = 'update_collections';
    public const DELETE_COLLECTIONS = 'delete_collections';

    public const GET_CONTRACTS = 'get_contracts';
    public const CREATE_CONTRACTS = 'create_contracts';
    public const UPDATE_CONTRACTS = 'update_contracts';
    public const DELETE_CONTRACTS = 'delete_contracts';

    public const GET_CUSTOMERS = 'get_customers';
    public const CREATE_CUSTOMERS = 'create_customers';
    public const UPDATE_CUSTOMERS = 'update_customers';
    public const DELETE_CUSTOMERS = 'delete_customers';

    public const GET_CUSTOMER_TAGS = 'get_customer_tags';
    public const CREATE_CUSTOMER_TAGS = 'create_customer_tags';
    public const UPDATE_CUSTOMER_TAGS = 'update_customer_tags';
    public const DELETE_CUSTOMER_TAGS = 'delete_customer_tags';

    public const GET_CUSTOMER_GROUPS = 'get_customer_groups';
    public const CREATE_CUSTOMER_GROUPS = 'create_customer_groups';
    public const UPDATE_CUSTOMER_GROUPS = 'update_customer_groups';
    public const DELETE_CUSTOMER_GROUPS = 'delete_customer_groups';

    public const GET_DELIVERIES = 'get_deliveries';
    public const CREATE_DELIVERIES = 'create_deliveries';
    public const UPDATE_DELIVERIES = 'update_deliveries';
    public const DELETE_DELIVERIES = 'delete_deliveries';

    public const GET_DELIVERY_ADJUSTMENTS = 'get_delivery_adjustments';
    public const CREATE_DELIVERY_ADJUSTMENTS = 'create_delivery_adjustments';
    public const UPDATE_DELIVERY_ADJUSTMENTS = 'update_delivery_adjustments';
    public const DELETE_DELIVERY_ADJUSTMENTS = 'delete_delivery_adjustments';

    public const CREATE_DELIVERY_BATCHES = 'create_delivery_batches';

    public const GET_LENDING_STOCK_ADJUSTMENTS = 'get_lending_stock_adjustments';
    public const CREATE_LENDING_STOCK_ADJUSTMENTS = 'create_lending_stock_adjustments';
    public const UPDATE_LENDING_STOCK_ADJUSTMENTS = 'update_lending_stock_adjustments';
    public const DELETE_LENDING_STOCK_ADJUSTMENTS = 'delete_lending_stock_adjustments';

    public const GET_PAYMENT_METHODS = 'get_payment_methods';
    public const CREATE_PAYMENT_METHODS = 'create_payment_methods';
    public const UPDATE_PAYMENT_METHODS = 'update_payment_methods';
    public const DELETE_PAYMENT_METHODS = 'delete_payment_methods';

    public const GET_PRODUCTS = 'get_products';
    public const CREATE_PRODUCTS = 'create_products';
    public const UPDATE_PRODUCTS = 'update_products';
    public const DELETE_PRODUCTS = 'delete_products';

    public const GET_RECEIVABLES = 'get_receivables';
    public const CREATE_RECEIVABLES = 'create_receivables';
    public const UPDATE_RECEIVABLES = 'update_receivables';
    public const DELETE_RECEIVABLES = 'delete_receivables';

    public const GET_STOCK_LOCATIONS = 'get_stock_locations';
    public const CREATE_STOCK_LOCATIONS = 'create_stock_locations';
    public const UPDATE_STOCK_LOCATIONS = 'update_stock_locations';
    public const DELETE_STOCK_LOCATIONS = 'delete_stock_locations';

    public const GET_STOCK_NATURES = 'get_stock_natures';
    public const CREATE_STOCK_NATURES = 'create_stock_natures';
    public const UPDATE_STOCK_NATURES = 'update_stock_natures';
    public const DELETE_STOCK_NATURES = 'delete_stock_natures';

    public const GET_SUPPLIERS = 'get_suppliers';
    public const CREATE_SUPPLIERS = 'create_suppliers';
    public const UPDATE_SUPPLIERS = 'update_suppliers';
    public const DELETE_SUPPLIERS = 'delete_suppliers';

    public const GET_TECNOSPEED_PLUGBOLETO_BANK_RETURN_FILES = 'get_tecnospeed_plugboleto_bank_return_files';
    public const CREATE_TECNOSPEED_PLUGBOLETO_BANK_RETURN_FILES = 'create_tecnospeed_plugboleto_bank_return_files';
    public const UPDATE_TECNOSPEED_PLUGBOLETO_BANK_RETURN_FILES = 'update_tecnospeed_plugboleto_bank_return_files';
    public const DELETE_TECNOSPEED_PLUGBOLETO_BANK_RETURN_FILES = 'delete_tecnospeed_plugboleto_bank_return_files';

    public const GET_PAYMENT_RECOVERY_SETTINGS = 'get_payment_recovery_settings';
    public const CREATE_PAYMENT_RECOVERY_SETTINGS = 'create_payment_recovery_settings';
    public const UPDATE_PAYMENT_RECOVERY_SETTINGS = 'update_payment_recovery_settings';
    public const DELETE_PAYMENT_RECOVERY_SETTINGS = 'delete_payment_recovery_settings';

    /**
     * Translate a given permission.
     *
     * @param string $permission
     * @return array|\Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Translation\Translator|string
     */
    public static function translate(string $permission)
    {
        return __('permissions.' . $permission);
    }

    /**
     * Get all available permissions.
     *
     * @return array
     */
    public static function getAvailablePermissions(): array
    {
        $permissionReflectionClass = new \ReflectionClass(self::class);

        return array_filter(
            array_values($permissionReflectionClass->getConstants()),
            fn ($item) => !in_array($item, ['created_at', 'updated_at'])
        );
    }

    public static function getMapped(): array
    {
        return [
            'roles' => [
                'permission_name' => 'Perfis',
                self::GET_ROLES => 'Listar',
                self::CREATE_ROLES => 'Criar',
                self::UPDATE_ROLES => 'Atualizar',
                self::DELETE_ROLES => 'Excluir',
            ],

            'operators' => [
                'permission_name' => 'Operadores',
                self::GET_OPERATORS => 'Listar',
                self::CREATE_OPERATORS => 'Criar',
                self::UPDATE_OPERATORS => 'Atualizar',
                self::DELETE_OPERATORS => 'Excluir',
            ],

            'banks' => [
                'permission_name' => 'Bancos',
                self::GET_BANKS => 'Listar',
                self::CREATE_BANKS => 'Criar',
                self::UPDATE_BANKS => 'Atualizar',
                self::DELETE_BANKS => 'Excluir',
            ],

            'bank_slips' => [
                'permission_name' => 'Boletos',
                self::GET_BANK_SLIPS => 'Listar',
                self::CREATE_BANK_SLIPS => 'Criar',
                self::UPDATE_BANK_SLIPS => 'Atualizar',
                self::DELETE_BANK_SLIPS => 'Excluir',
            ],

            'categories' => [
                'permission_name' => 'Categorias',
                self::GET_CATEGORIES => 'Listar',
                self::CREATE_CATEGORIES => 'Criar',
                self::UPDATE_CATEGORIES => 'Atualizar',
                self::DELETE_CATEGORIES => 'Excluir',
            ],

            'collections' => [
                'permission_name' => 'Coletas',
                self::GET_COLLECTIONS => 'Listar',
                self::CREATE_COLLECTIONS => 'Criar',
                self::UPDATE_COLLECTIONS => 'Atualizar',
                self::DELETE_COLLECTIONS => 'Excluir',
            ],

            'contracts' => [
                'permission_name' => 'Contratos',
                self::GET_CONTRACTS => 'Listar',
                self::CREATE_CONTRACTS => 'Criar',
                self::UPDATE_CONTRACTS => 'Atualizar',
                self::DELETE_CONTRACTS => 'Excluir',
            ],

            'customers' => [
                'permission_name' => 'Clientes',
                self::GET_CUSTOMERS => 'Listar',
                self::CREATE_CUSTOMERS => 'Criar',
                self::UPDATE_CUSTOMERS => 'Atualizar',
                self::DELETE_CUSTOMERS => 'Excluir',
            ],

            'customer_tags' => [
                'permission_name' => 'Tags de clientes',
                self::GET_CUSTOMER_TAGS => 'Listar',
                self::CREATE_CUSTOMER_TAGS => 'Criar',
                self::UPDATE_CUSTOMER_TAGS => 'Atualizar',
                self::DELETE_CUSTOMER_TAGS => 'Excluir',
            ],

            'customer_groups' => [
                'permission_name' => 'Grupos de clientes',
                self::GET_CUSTOMER_GROUPS => 'Listar',
                self::CREATE_CUSTOMER_GROUPS => 'Criar',
                self::UPDATE_CUSTOMER_GROUPS => 'Atualizar',
                self::DELETE_CUSTOMER_GROUPS => 'Excluir',
            ],

            'deliveries' => [
                'permission_name' => 'Entregas',
                self::GET_DELIVERIES => 'Listar',
                self::CREATE_DELIVERIES => 'Criar',
                self::UPDATE_DELIVERIES => 'Atualizar',
                self::DELETE_DELIVERIES => 'Excluir',
            ],

            'delivery_batches' => [
                'permission_name' => 'Entregas em lote',
                self::CREATE_DELIVERY_BATCHES => 'Criar',
            ],

            'lending_stock_adjustments' => [
                'permission_name' => 'Ajustes de estoque',
                self::GET_LENDING_STOCK_ADJUSTMENTS => 'Listar',
                self::CREATE_LENDING_STOCK_ADJUSTMENTS => 'Criar',
                self::UPDATE_LENDING_STOCK_ADJUSTMENTS => 'Atualizar',
                self::DELETE_LENDING_STOCK_ADJUSTMENTS => 'Excluir',
            ],

            'payment_methods' => [
                'permission_name' => 'Formas de pagamento',
                self::GET_PAYMENT_METHODS => 'Listar',
                self::CREATE_PAYMENT_METHODS => 'Criar',
                self::UPDATE_PAYMENT_METHODS => 'Atualizar',
                self::DELETE_PAYMENT_METHODS => 'Excluir',
            ],

            'products' => [
                'permission_name' => 'Produtos',
                self::GET_PRODUCTS => 'Listar',
                self::CREATE_PRODUCTS => 'Criar',
                self::UPDATE_PRODUCTS => 'Atualizar',
                self::DELETE_PRODUCTS => 'Excluir',
            ],

            'receivables' => [
                'permission_name' => 'Faturas',
                self::GET_RECEIVABLES => 'Listar',
                self::CREATE_RECEIVABLES => 'Criar',
                self::UPDATE_RECEIVABLES => 'Atualizar',
                self::DELETE_RECEIVABLES => 'Excluir',
            ],

            'stock_locations' => [
                'permission_name' => 'Localizações de estoque',
                self::GET_STOCK_LOCATIONS => 'Listar',
                self::CREATE_STOCK_LOCATIONS => 'Criar',
                self::UPDATE_STOCK_LOCATIONS => 'Atualizar',
                self::DELETE_STOCK_LOCATIONS => 'Excluir',
            ],

            'stock_natures' => [
                'permission_name' => 'Naturezas de estoque',
                self::GET_STOCK_NATURES => 'Listar',
                self::CREATE_STOCK_NATURES => 'Criar',
                self::UPDATE_STOCK_NATURES => 'Atualizar',
                self::DELETE_STOCK_NATURES => 'Excluir',
            ],

            'suppliers' => [
                'permission_name' => 'Fornecedores',
                self::GET_SUPPLIERS => 'Listar',
                self::CREATE_SUPPLIERS => 'Criar',
                self::UPDATE_SUPPLIERS => 'Atualizar',
                self::DELETE_SUPPLIERS => 'Excluir',
            ],
        ];
    }
}
