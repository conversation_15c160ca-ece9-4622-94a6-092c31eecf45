<?php

namespace App\Models;

use App\Models\Concerns\DeliveryAdjustment\HandlesDeliveryAdjustmentRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Delivery adjustment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $delivery_id
 * @property  int $customer_id
 * @property  int $product_id
 * @property  float $desired_withdraw_quantity
 * @property  float $max_withdraw_percentage
 * @property  int $parent_delivery_adjustment_id
 * @property  string $additional_info
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Product $product
 * @property  \App\Models\DeliveryAdjustment $parentDeliveryAdjustment
 */
class DeliveryAdjustment extends Model
{
    use HandlesDeliveryAdjustmentRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'delivery_id',
        'customer_id',
        'product_id',
        'desired_withdraw_quantity',
        'max_withdraw_percentage',
        'parent_delivery_adjustment_id',
        'additional_info',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'delivery_id' => 'int',
        'customer_id' => 'int',
        'product_id' => 'int',
        'desired_withdraw_quantity' => 'float',
        'max_withdraw_percentage' => 'float',
        'parent_delivery_adjustment_id' => 'int',
    ];
}
