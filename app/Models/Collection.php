<?php

namespace App\Models;

use App\Models\Concerns\Collection\HandlesCollectionRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Collection model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $contract_id
 * @property string $additional_info
 * @property bool $email_sent
 * @property \Carbon\Carbon $collected_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\Contract $contract
 * @property \Illuminate\Support\Collection|\App\Models\CollectionItem[] $collectionItems
 * @property \Illuminate\Support\Collection|\App\Models\CustomerHistory[] $customerHistories
 * @property \Illuminate\Support\Collection|\App\Models\CollectionReceiptEmail[] $collectionReceiptEmails
 * @property \Illuminate\Support\Collection|\App\Models\IncomeCollection[] $incomeCollections
 * @property-read \App\Models\BillingItem|null $billingItem
 * @property-read int|null $collection_items_count
 * @property-read int|null $collection_receipt_emails_count
 * @property-read int|null $customer_histories_count
 * @property-read int|null $income_collections_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Collection withoutTrashed()
 * @mixin \Eloquent
 */
class Collection extends Model
{
    use HandlesCollectionRelationships;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'contract_id',
        'additional_info',
        'email_sent',
        'collected_at'
    ];
}
