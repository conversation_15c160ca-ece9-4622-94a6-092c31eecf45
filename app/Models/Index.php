<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Index model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  float $amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_amount
 *
 * @property  \Illuminate\Support\Collection|\App\Models\IndexRevision[] $indexRevisions
 */
class Index extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'amount',
    ];

    /**
     * Mutator for the "amount" attribute.
     *
     * @return void
     */
    public function setAmountAttribute(mixed $value)
    {
        $this->attributes['amount'] = unmask_percentage($value);
    }

    /**
     * Accessor for the "amount" attribute.
     *
     * @return string
     */
    public function getFriendlyAmountAttribute(): string
    {
        return mask_percentage($this->amount);
    }
}
