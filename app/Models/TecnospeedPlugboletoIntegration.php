<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Tecnospeed PlugBoleto integration model.
 *
 * @package App\Models
 * @property int $id
 * @property string $receiver_tax_id_number
 * @property bool $active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoIntegration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoIntegration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TecnospeedPlugboletoIntegration query()
 * @mixin \Eloquent
 */
class TecnospeedPlugboletoIntegration extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'receiver_tax_id_number',
        'active'
    ];
}
