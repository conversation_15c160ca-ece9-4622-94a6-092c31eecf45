<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Subcategory
 *
 * @property-read \App\Models\Category|null $category
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Subcategory withoutTrashed()
 * @mixin \Eloquent
 */
class Subcategory extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'name'
    ];

    /**
     * Load the category relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
