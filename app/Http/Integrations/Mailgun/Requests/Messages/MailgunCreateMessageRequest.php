<?php

namespace App\Http\Integrations\Mailgun\Requests\Messages;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasFormParams;

class MailgunCreateMessageRequest extends SaloonRequest
{
    use HasFormParams;

    protected ?string $method = Saloon::POST;

    public function __construct(
        protected string $domain,
        protected string $from,
        protected string $to,
        protected string $subject,
        protected string $html,
        protected ?string $cc = null,
        protected ?string $bcc = null,
    ) {}

    public function defineEndpoint(): string
    {
        return "/$this->domain/messages";
    }

    public function defaultData(): array
    {
        return array_filter([
            'from' => $this->from,
            'to' => $this->to,
            'subject' => $this->subject,
            'html' => $this->html,
            'cc' => $this->cc,
            'bcc' => $this->bcc,
        ]);
    }
}
