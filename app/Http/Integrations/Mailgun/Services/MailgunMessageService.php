<?php

namespace App\Http\Integrations\Mailgun\Services;

use App\Http\Integrations\Mailgun\Requests\Messages\MailgunCreateMessageRequest;

class MailgunMessageService extends MailgunBaseService
{
    public function create(
        string $domain,
        string $from,
        string $to,
        string $subject,
        string $html,
        ?string $cc = null,
        ?string $bcc = null,
    ): ?string {
        $request = new MailgunCreateMessageRequest($domain, $from, $to, $subject, $html, $cc, $bcc);
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body->id ?? null;
    }
}
