<?php

namespace App\Http\Integrations\Itts\Requests\Activity;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;

class IttsGetActivitiesRequest extends SaloonRequest
{
    protected ?string $method = Saloon::GET;

    public function __construct(protected ?string $status = null) {}

    public function defineEndpoint(): string
    {
        return $this->status
            ? "/modules/iris/activities?status={$this->status}"
            : '/modules/iris/activities';
    }
}
