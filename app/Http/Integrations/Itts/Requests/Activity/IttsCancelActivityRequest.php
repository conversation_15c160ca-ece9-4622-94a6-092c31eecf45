<?php

namespace App\Http\Integrations\Itts\Requests\Activity;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;

class IttsCancelActivityRequest extends SaloonRequest
{
    protected ?string $method = Saloon::POST;

    public function __construct(protected string $activityId) {}

    public function defineEndpoint(): string
    {
        return "/modules/iris/activities/{$this->activityId}/cancel";
    }
}
