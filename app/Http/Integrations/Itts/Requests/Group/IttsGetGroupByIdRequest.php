<?php

namespace App\Http\Integrations\Itts\Requests\Group;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;

class IttsGetGroupByIdRequest extends SaloonRequest
{
    protected ?string $method = Saloon::GET;

    public function __construct(protected string $groupId) {}

    public function defineEndpoint(): string
    {
        return "/modules/iris/groups/{$this->groupId}";
    }
}
