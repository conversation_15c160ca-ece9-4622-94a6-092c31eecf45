<?php

namespace App\Http\Integrations\Itts\Requests\ActivityTemplate;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;

class IttsGetActivityTemplatesRequest extends SaloonRequest
{
    protected ?string $method = Saloon::GET;

    public function __construct() {}

    public function defineEndpoint(): string
    {
        return '/modules/iris/activitytemplates';
    }
}
