<?php

namespace App\Http\Integrations\Itts\Requests;

use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Http\SaloonRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasFormParams;

class IttsRegisterRequest extends SaloonRequest
{
    use HasFormParams;

    protected ?string $method = Saloon::POST;

    public function __construct(
        protected string $username,
        protected string $password,
    ) {}

    public function defineEndpoint(): string
    {
        return '/register';
    }

    public function defaultData(): array
    {
        return [
            'username' => $this->username,
            'password' => $this->password,
            'grant_type' => 'password',
        ];
    }
}
