<?php

namespace App\Http\Integrations\Itts\Services;

use App\Http\Integrations\Itts\Requests\Activity\IttsCancelActivityRequest;
use App\Http\Integrations\Itts\Requests\Activity\IttsGetActivitiesRequest;

class IttsActivityService extends IttsBaseService
{
    public function get(?string $status = null): mixed
    {
        $request = new IttsGetActivitiesRequest($status);
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }

    public function cancel(string $activityId): mixed
    {
        $request = new IttsCancelActivityRequest($activityId);
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }
}
