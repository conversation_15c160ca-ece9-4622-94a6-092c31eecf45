<?php

namespace App\Http\Integrations\Itts\Services;

use App\Http\Integrations\Itts\Requests\ActivityTemplate\IttsGetActivityTemplatesRequest;

class IttsActivityTemplateService extends IttsBaseService
{
    public function get(): mixed
    {
        $request = new IttsGetActivityTemplatesRequest();
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }
}
