<?php

namespace App\Http\Integrations\Itts\Services;

use App\Http\Integrations\Itts\Requests\Group\IttsGetGroupByIdRequest;
use App\Http\Integrations\Itts\Requests\Group\IttsGetGroupsRequest;

class IttsGroupService extends IttsBaseService
{
    public function get(): mixed
    {
        $request = new IttsGetGroupsRequest();
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }

    public function getById(string $groupId): mixed
    {
        $request = new IttsGetGroupByIdRequest($groupId);
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }
}
