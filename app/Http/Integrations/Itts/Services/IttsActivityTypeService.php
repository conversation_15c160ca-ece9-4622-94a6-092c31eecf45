<?php

namespace App\Http\Integrations\Itts\Services;

use App\Http\Integrations\Itts\Requests\ActivityType\IttsGetActivityTypesRequest;

class IttsActivityTypeService extends IttsBaseService
{
    public function get(): mixed
    {
        $request = new IttsGetActivityTypesRequest();
        $response = $this->connector->send($request);
        $body = json_decode($response->body());

        return $body;
    }
}
