<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Services;

class TecnospeedPlugBoletoBaseService
{
    /**
     * Create a new instance.
     *
     * @param  string|null $draweeTaxIdNumber
     */
    public function __construct(
        protected ?string $draweeTaxIdNumber = null
    ) {
    }

    /**
     * Build an instance.
     *
     * @param  string|null $draweeTaxIdNumber
     * @return static
     */
    public static function make(?string $draweeTaxIdNumber = null): static
    {
        return new static($draweeTaxIdNumber);
    }
}
