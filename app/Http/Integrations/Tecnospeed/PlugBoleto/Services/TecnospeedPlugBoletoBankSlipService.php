<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Services;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip\TecnospeedPlugBoletoCreateBankSlipBatchRequest;
use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip\TecnospeedPlugBoletoGetBankSlipRequest;
use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip\TecnospeedPlugBoletoSettleBankSlipBatchRequest;
use App\Http\Integrations\Tecnospeed\PlugBoleto\TecnospeedPlugBoletoConnector;
use App\Http\Integrations\Tecnospeed\PlugBoleto\Transformers\TecnospeedPlugBoletoBankSlipTransformer;
use App\Models\BankAccountWallet;

class TecnospeedPlugBoletoBankSlipService extends TecnospeedPlugBoletoBaseService
{
    /**
     * Get a bank slip by its ID.
     *
     * @param  string|null $tecnospeedId
     * @return mixed
     */
    public function getById(?string $tecnospeedId): mixed
    {
        if (!$tecnospeedId) {
            return null;
        }

        return json_decode(
            (new TecnospeedPlugBoletoConnector($this->draweeTaxIdNumber))
                ->request(new TecnospeedPlugBoletoGetBankSlipRequest($tecnospeedId))
                ->send()
        );
    }

    /**
     * Get the bank slip's PDF url.
     *
     * @param  string|null $tecnospeedId
     * @return string
     */
    public function getPdfUrl(?string $tecnospeedId): string
    {
        if (is_null($tecnospeedId)) {
            return '';
        }

        $tecnospeedBankSlip = $this->getById($tecnospeedId);

        return $tecnospeedBankSlip->_dados[0]?->UrlBoleto ?? '';
    }

    /**
     * Create a bank slip batch.
     *
     * @param  \App\Models\BankSlip[] $bankSlips
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @return mixed
     */
    public function createBatch(array $bankSlips, BankAccountWallet $bankAccountWallet): mixed
    {
        $data = [];

        foreach ($bankSlips as $bankSlip) {
            $data[] = TecnospeedPlugBoletoBankSlipTransformer::make($bankSlip, $bankAccountWallet)->transform();
        }

        return json_decode(
            (new TecnospeedPlugBoletoConnector($bankAccountWallet->drawee_tax_id_number))
                ->request(new TecnospeedPlugBoletoCreateBankSlipBatchRequest($data))
                ->send()
        );
    }

    /**
     * Settle a bank slip batch.
     *
     * @param  array $bankSlipTecnospeedIds
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @return mixed
     */
    public function settleBatch(array $bankSlipTecnospeedIds, BankAccountWallet $bankAccountWallet): mixed
    {
        return json_decode(
            (new TecnospeedPlugBoletoConnector($bankAccountWallet->drawee_tax_id_number))
                ->request(new TecnospeedPlugBoletoSettleBankSlipBatchRequest($bankSlipTecnospeedIds))
                ->send()
        );
    }
}
