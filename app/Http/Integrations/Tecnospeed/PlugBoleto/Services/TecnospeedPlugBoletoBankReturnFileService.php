<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Services;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankReturnFile\TecnospeedPlugBoletoSendBankReturnFileRequest;
use App\Http\Integrations\Tecnospeed\PlugBoleto\TecnospeedPlugBoletoConnector;
use App\Models\BankAccountWallet;
use Illuminate\Support\Facades\Storage;

class TecnospeedPlugBoletoBankReturnFileService extends TecnospeedPlugBoletoBaseService
{
    /**
     * Send the return file to Tecnospeed.
     *
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @param  string $name
     * @param  string $filepath
     * @return mixed
     */
    public function send(BankAccountWallet $bankAccountWallet, string $name, string $filepath): mixed
    {
        $file = Storage::disk('digitalocean')->get($filepath);

        $data = [
            'arquivo' => base64_encode($file),
            'nome' => $name
        ];

        return json_decode(
            (new TecnospeedPlugBoletoConnector($bankAccountWallet->drawee_tax_id_number))
                ->request(new TecnospeedPlugBoletoSendBankReturnFileRequest($data))
                ->send()
        );
    }
}
