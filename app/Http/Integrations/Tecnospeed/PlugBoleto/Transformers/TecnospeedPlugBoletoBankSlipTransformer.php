<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Transformers;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Transformers\Interfaces\ConvertsModelToPlugBoletoArray;
use App\Models\BankAccountWallet;
use App\Models\BankSlip;

class TecnospeedPlugBoletoBankSlipTransformer implements ConvertsModelToPlugBoletoArray
{
    /**
     * Create a new instance.
     *
     * @param  \App\Models\BankSlip $bankSlip
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     */
    public function __construct(
        protected BankSlip $bankSlip,
        protected BankAccountWallet $bankAccountWallet
    ) {
    }

    /**
     * Build an instance.
     *
     * @param  \App\Models\BankSlip $bankSlip
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @return static
     */
    public static function make(BankSlip $bankSlip, BankAccountWallet $bankAccountWallet): static
    {
        return new static($bankSlip, $bankAccountWallet);
    }

    /**
     * @inheritDoc
     */
    public function transform(): array
    {
        $data = [
            // "Sacado" (customer) - responsible for the bank slip's payment
            'SacadoCPFCNPJ' => $this->bankSlip->customer->tax_id_number,
            'SacadoNome' => $this->bankSlip->customer->name,
            'SacadoEnderecoLogradouro' => $this->bankSlip->customer->address_address,
            'SacadoEnderecoNumero' => $this->bankSlip->customer->address_number,
            'SacadoEnderecoBairro' => $this->bankSlip->customer->address_district,
            'SacadoEnderecoCep' => $this->bankSlip->customer->address_zipcode,
            'SacadoEnderecoCidade' => $this->bankSlip->customer->city->name,
            'SacadoEnderecoComplemento' => $this->bankSlip->customer->address_additional_info,
            'SacadoEnderecoPais' => 'Brasil',
            'SacadoEnderecoUf' => $this->bankSlip->customer->state->abbreviation,
            'SacadoEmail' => $this->bankSlip->customer->email,
            'SacadoTelefone' => get_numbers($this->bankSlip->customer->phone_1),

            // "Cedente" (receiver) - responsible for the bank slip's issuance
            'CedenteContaCodigoBanco' => str_pad($this->bankAccountWallet->bankAccount->bank->code, 3, '0', STR_PAD_LEFT),
            'CedenteContaNumero' => $this->bankAccountWallet->bankAccount->number,
            'CedenteContaNumeroDV' => $this->bankAccountWallet->bankAccount->digit,
            'CedenteConvenioNumero' => $this->bankAccountWallet->number,

            // Basic inclusion fields
            'TituloValor' => number_format($this->bankSlip->amount, 2, ',', ''),
            'TituloNumeroDocumento' => $this->bankSlip->receivable->document->document_number,
            'TituloDataEmissao' => format_date(now()),
            'TituloDataVencimento' => format_date($this->bankSlip->expires_at),
            'TituloAceite' => 'N',
            'TituloDocEspecie' => '01',

            // Interest fields
            'TituloCodigoJuros' => '2',
            'TituloDataJuros' => format_date(carbon($this->bankSlip->expires_at)->addDay()),
            'TituloValorJuros' => '1,00',

            // Fine fields
            'TituloCodigoMulta' => '2',
            'TituloDataMulta' => format_date(carbon($this->bankSlip->expires_at)->addDay()),
            'TituloValorMultaTaxa' => '2,00',

            // Protest fields
            'TituloCodProtesto' => '3',

            // Automatic settlement
            'TituloCodBaixaDevolucao' => '2',

            // Other fields
            'TituloCategoria' => '2',
            'TituloMensagem01' => 'Após o vencimento, cobrar multa de 2% após 1 dia útil e juros de 1% ao mês.',
            'TituloMensagem02' => 'BOLETO REFERENTE À FATURA #' . $this->bankSlip->receivable->document->document_number,
        ];

        if (str_pad($this->bankAccountWallet->bankAccount->bank->code, 3, '0', STR_PAD_LEFT) !== '077') {
            $data['TituloNossoNumero'] = $this->bankSlip->id;
        }

        return $data;
    }
}
