<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\TecnospeedPlugBoletoBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class TecnospeedPlugBoletoCreateBankSlipBatchRequest extends TecnospeedPlugBoletoBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  array $data
     */
    public function __construct(protected array $data)
    {
        $this->data = $data;
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/boletos/lote';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->data;
    }
}
