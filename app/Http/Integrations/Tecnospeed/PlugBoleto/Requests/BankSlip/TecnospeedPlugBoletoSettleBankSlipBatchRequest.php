<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\TecnospeedPlugBoletoBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class TecnospeedPlugBoletoSettleBankSlipBatchRequest extends TecnospeedPlugBoletoBasePostRequest
{
    use HasJsonBody;

    /**
     * Create a new instance.
     *
     * @param  array $tecnospeedIds
     */
    public function __construct(protected array $tecnospeedIds)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/boletos/baixa/lote';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->tecnospeedIds;
    }
}
