<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankSlip;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\TecnospeedPlugBoletoBaseGetRequest;

class TecnospeedPlugBoletoGetBankSlipRequest extends TecnospeedPlugBoletoBaseGetRequest
{
    /**
     * Create a new instance.
     *
     * @param  string tecnospeedId
     */
    public function __construct(protected string $tecnospeedId)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/boletos?idintegracao=' . $this->tecnospeedId;
    }
}
