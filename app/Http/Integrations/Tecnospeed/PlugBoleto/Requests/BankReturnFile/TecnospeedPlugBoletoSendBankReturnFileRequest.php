<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\BankReturnFile;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Requests\TecnospeedPlugBoletoBasePostRequest;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class TecnospeedPlugBoletoSendBankReturnFileRequest extends TecnospeedPlugBoletoBasePostRequest
{
    use Has<PERSON>sonBody;

    /**
     * Create a new instance.
     *
     * @param  array $data
     */
    public function __construct(protected array $data)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/retornos';
    }

    /**
     * @inheritDoc
     */
    public function defaultData(): array
    {
        return $this->data;
    }
}
