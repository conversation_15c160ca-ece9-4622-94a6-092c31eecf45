<?php

namespace App\Http\Integrations\Tecnospeed\PlugBoleto;

use Sammyjo20\Saloon\Http\SaloonConnector;
use Sammyjo20\Saloon\Traits\Plugins\AcceptsJson;

class TecnospeedPlugBoletoConnector extends SaloonConnector
{
    use AcceptsJson;

    /**
     * Create a new instance.
     *
     * @param  string|null $draweeTaxIdNumber
     */
    public function __construct(protected ?string $draweeTaxIdNumber = null)
    {
    }

    /**
     * The Base URL of the API.
     *
     * @return string
     */
    public function defineBaseUrl(): string
    {
        return app()->isProduction()
            ? config('tecnospeed.plugboleto.api.production.url')
            : config('tecnospeed.plugboleto.api.local.url');
    }

    /**
     * The headers that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'cnpj-sh' => config('tecnospeed.plugboleto.software_house_tax_id_number'),
            'token-sh' => config('tecnospeed.plugboleto.software_house_token'),
            'cnpj-cedente' => $this->draweeTaxIdNumber
        ];
    }

    /**
     * The config options that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultConfig(): array
    {
        return [];
    }
}
