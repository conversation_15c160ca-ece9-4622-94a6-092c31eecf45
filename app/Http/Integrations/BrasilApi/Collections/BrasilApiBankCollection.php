<?php

namespace App\Http\Integrations\BrasilApi\Collections;

use App\Http\Integrations\BrasilApi\Requests\Bank\BrasilApiGetBanksRequest;
use Sammyjo20\Saloon\Http\SaloonResponse;

class BrasilApiBankCollection extends BrasilApiBaseCollection
{
    /**
     * Get all banks.
     *
     * @return \Sammyjo20\Saloon\Http\SaloonResponse
     */
    public function get(): SaloonResponse
    {
        return $this->connector
            ->request(new BrasilApiGetBanksRequest())
            ->send();
    }
}
