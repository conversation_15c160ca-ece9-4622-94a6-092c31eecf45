<?php

namespace App\Http\Integrations\BrasilApi\Services;

use App\Http\Integrations\BrasilApi\Collections\BrasilApiBankCollection;

class BrasilApiBankService
{
    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\BrasilApi\Collections\BrasilApiBankCollection $brasilApiBankCollection
     */
    public function __construct(
        protected BrasilApiBankCollection $brasilApiBankCollection = new BrasilApiBankCollection()
    ) {
    }

    /**
     * Get all banks.
     *
     * @return mixed
     */
    public function get(): mixed
    {
        return json_decode(
            $this->brasilApiBankCollection->get()
        );
    }
}
