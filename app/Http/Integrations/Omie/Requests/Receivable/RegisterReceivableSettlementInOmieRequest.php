<?php

namespace App\Http\Integrations\Omie\Requests\Receivable;

use App\Http\Integrations\Omie\DataTransferObjects\OmieRegisterReceivableSettlementDto;
use App\Http\Integrations\Omie\Requests\OmieRequest;
use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class RegisterReceivableSettlementInOmieRequest extends OmieRequest
{
    use HasJsonBody;

    /**
     * The HTTP verb the request will use.
     *
     * @var string|null
     */
    protected ?string $method = Saloon::POST;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieDeleteReceivableDto $omieRegisterReceivableSettlementDto
     */
    public function __construct(protected OmieRegisterReceivableSettlementDto $omieRegisterReceivableSettlementDto) {}

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/financas/contareceber/';
    }

    /** @inheritDoc */
    public function defaultData(): array
    {
        return array_merge($this->buildBaseBody(), [
            'call' => 'LancarRecebimento',
            'param' => [$this->omieRegisterReceivableSettlementDto->toArray()]
        ]);
    }
}
