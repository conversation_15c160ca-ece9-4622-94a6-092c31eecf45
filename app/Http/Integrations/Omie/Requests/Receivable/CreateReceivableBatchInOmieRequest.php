<?php

namespace App\Http\Integrations\Omie\Requests\Receivable;

use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableBatchDto;
use App\Http\Integrations\Omie\Requests\OmieRequest;
use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class CreateReceivableBatchInOmieRequest extends OmieRequest
{
    use HasJsonBody;

    /**
     * The HTTP verb the request will use.
     *
     * @var string|null
     */
    protected ?string $method = Saloon::POST;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableBatchDto $omieCreateReceivableBatchDto
     */
    public function __construct(protected OmieCreateReceivableBatchDto $omieCreateReceivableBatchDto) {}

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/financas/contareceber/';
    }

    /** @inheritDoc */
    public function defaultData(): array
    {
        return array_merge($this->buildBaseBody(), [
            'call' => 'IncluirContaReceberPorLote',
            'param' => [$this->omieCreateReceivableBatchDto->toArray()],
        ]);
    }
}
