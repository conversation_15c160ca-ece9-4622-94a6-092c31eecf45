<?php

namespace App\Http\Integrations\Omie\Requests\Receivable;

use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableDto;
use App\Http\Integrations\Omie\Requests\OmieRequest;
use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class CreateReceivableInOmieRequest extends OmieRequest
{
    use HasJsonBody;

    /**
     * The HTTP verb the request will use.
     *
     * @var string|null
     */
    protected ?string $method = Saloon::POST;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableDto $omieCreateReceivableDto
     */
    public function __construct(protected OmieCreateReceivableDto $omieCreateReceivableDto) {}

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/financas/contareceber/';
    }

    /** @inheritDoc */
    public function defaultData(): array
    {
        return array_merge($this->buildBaseBody(), [
            'call' => 'IncluirContaReceber',
            'param' => [$this->omieCreateReceivableDto->toArray()],
        ]);
    }
}
