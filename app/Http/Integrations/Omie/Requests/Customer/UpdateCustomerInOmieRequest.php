<?php

namespace App\Http\Integrations\Omie\Requests\Customer;

use App\Http\Integrations\Omie\DataTransferObjects\OmieUpdateCustomerDto;
use App\Http\Integrations\Omie\Requests\OmieRequest;
use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class UpdateCustomerInOmieRequest extends OmieRequest
{
    use HasJsonBody;

    /**
     * The HTTP verb the request will use.
     *
     * @var string|null
     */
    protected ?string $method = Saloon::POST;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieUpdateCustomerDto $omieUpdateCustomerDto
     */
    public function __construct(protected OmieUpdateCustomerDto $omieUpdateCustomerDto) {}

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/geral/clientes/';
    }

    /** @inheritDoc */
    public function defaultData(): array
    {
        return array_merge($this->buildBaseBody(), [
            'call' => 'AlterarCliente',
            'param' => [$this->omieUpdateCustomerDto->toArray()],
        ]);
    }
}
