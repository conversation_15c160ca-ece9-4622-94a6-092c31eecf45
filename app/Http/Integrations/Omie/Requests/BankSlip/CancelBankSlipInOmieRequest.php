<?php

namespace App\Http\Integrations\Omie\Requests\BankSlip;

use App\Http\Integrations\Omie\DataTransferObjects\OmieCancelBankSlipDto;
use App\Http\Integrations\Omie\Requests\OmieRequest;
use Sammyjo20\Saloon\Constants\Saloon;
use Sammyjo20\Saloon\Traits\Plugins\HasJsonBody;

class CancelBankSlipInOmieRequest extends OmieRequest
{
    use HasJsonBody;

    /**
     * The HTTP verb the request will use.
     *
     * @var string|null
     */
    protected ?string $method = Saloon::POST;

    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieCancelBankSlipDto $omieCancelBankSlipDto
     */
    public function __construct(protected OmieCancelBankSlipDto $omieCancelBankSlipDto)
    {
    }

    /**
     * The endpoint of the request.
     *
     * @return string
     */
    public function defineEndpoint(): string
    {
        return '/v1/financas/contareceberboleto/';
    }

    public function defaultData(): array
    {
        return array_merge($this->buildBaseBody(), [
            'call' => 'CancelarBoleto',
            'param' => [$this->omieCancelBankSlipDto],
        ]);
    }
}
