<?php

namespace App\Http\Integrations\Omie\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateCustomerDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieDeleteCustomerDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieUpdateCustomerDto;
use App\Http\Integrations\Omie\OmieConnector;
use App\Http\Integrations\Omie\Requests\Customer\CreateCustomerInOmieRequest;
use App\Http\Integrations\Omie\Requests\Customer\DeleteCustomerFromOmieRequest;
use App\Http\Integrations\Omie\Requests\Customer\UpdateCustomerInOmieRequest;
use App\Models\Customer;

class OmieCustomerService extends OmieBaseService
{
    public function create(Customer $customer, OmieCreateCustomerDto $omieCreateCustomerDto): mixed
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new CreateCustomerInOmieRequest($omieCreateCustomerDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $customer,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body->codigo_cliente_omie;
    }

    public function update(Customer $customer, OmieUpdateCustomerDto $omieUpdateCustomerDto): mixed
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new UpdateCustomerInOmieRequest($omieUpdateCustomerDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $customer,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function delete(Customer $customer, OmieDeleteCustomerDto $omieDeleteCustomerDto): mixed
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new DeleteCustomerFromOmieRequest($omieDeleteCustomerDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $customer,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }
}
