<?php

namespace App\Http\Integrations\Omie\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCancelBankSlipDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateBankSlipDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieExtendBankSlipDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\OmieConnector;
use App\Http\Integrations\Omie\Requests\BankSlip\CancelBankSlipInOmieRequest;
use App\Http\Integrations\Omie\Requests\BankSlip\CreateBankSlipInOmieRequest;
use App\Http\Integrations\Omie\Requests\BankSlip\ExtendBankSlipInOmieRequest;
use App\Http\Integrations\Omie\Requests\BankSlip\GetBankSlipFromOmieRequest;
use App\Models\Receivable;

class OmieBankSlipService extends OmieBaseService
{
    public function get(Receivable $receivable, OmieGetBankSlipDto $omieGetBankSlipDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new GetBankSlipFromOmieRequest($omieGetBankSlipDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function create(Receivable $receivable, OmieCreateBankSlipDto $omieCreateBankSlipDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new CreateBankSlipInOmieRequest($omieCreateBankSlipDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function extend(Receivable $receivable, OmieExtendBankSlipDto $omieExtendBankSlipDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new ExtendBankSlipInOmieRequest($omieExtendBankSlipDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function cancel(Receivable $receivable, OmieCancelBankSlipDto $omieCancelBankSlipDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new CancelBankSlipInOmieRequest($omieCancelBankSlipDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }
}
