<?php

namespace App\Http\Integrations\Omie\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableBatchDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieDeleteReceivableDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieRegisterReceivableSettlementDto;
use App\Http\Integrations\Omie\OmieConnector;
use App\Http\Integrations\Omie\Requests\Receivable\CreateReceivableBatchInOmieRequest;
use App\Http\Integrations\Omie\Requests\Receivable\CreateReceivableInOmieRequest;
use App\Http\Integrations\Omie\Requests\Receivable\DeleteReceivableInOmieRequest;
use App\Http\Integrations\Omie\Requests\Receivable\RegisterReceivableSettlementInOmieRequest;
use App\Models\OmieReceivableBatch;
use App\Models\Receivable;

class OmieReceivableService extends OmieBaseService
{
    public function create(Receivable $receivable, OmieCreateReceivableDto $omieCreateReceivableDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new CreateReceivableInOmieRequest($omieCreateReceivableDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function createBatch(OmieReceivableBatch $omieReceivableBatch, OmieCreateReceivableBatchDto $omieCreateReceivableBatchDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new CreateReceivableBatchInOmieRequest($omieCreateReceivableBatchDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $omieReceivableBatch,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function registerSettlement(Receivable $receivable, OmieRegisterReceivableSettlementDto $omieRegisterReceivableSettlementDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new RegisterReceivableSettlementInOmieRequest($omieRegisterReceivableSettlementDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }

    public function delete(Receivable $receivable, OmieDeleteReceivableDto $omieDeleteReceivableDto)
    {
        $connector = new OmieConnector($this->appKey, $this->appSecret);

        $request = new DeleteReceivableInOmieRequest($omieDeleteReceivableDto);

        $request->setConnector($connector);

        $response = $connector->send($request);

        CreateApiRequestLog::run(
            model: $receivable,
            response: $response,
            success: $response->successful(),
            requestBody: $request->defaultData()
        );

        $body = json_decode($response);

        return $body;
    }
}
