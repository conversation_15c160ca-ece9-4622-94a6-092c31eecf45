<?php

namespace App\Http\Integrations\Omie;

use Sammyjo20\Saloon\Http\SaloonConnector;
use Sammyjo20\Saloon\Traits\Plugins\AcceptsJson;

class OmieConnector extends SaloonConnector
{
    use AcceptsJson;

    /**
     * Create a new instance.
     *
     * @param  string|null $appKey
     * @param  string|null $appSecret
     */
    public function __construct(
        protected ?string $appKey = null,
        protected ?string $appSecret = null
    ) {
        if (!$appKey || !$appSecret) {
            $apiCredentials = get_omie_api_credentials();

            $this->appKey = $apiCredentials['app_key'];
            $this->appSecret = $apiCredentials['app_secret'];
        }
    }

    /**
     * The Base URL of the API.
     *
     * @return string
     */
    public function defineBaseUrl(): string
    {
        return config('omie.api.url');
    }

    /**
     * The headers that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * The config options that will be applied to every request.
     *
     * @return string[]
     */
    public function defaultConfig(): array
    {
        return [];
    }

    public function getAppKey(): string
    {
        return $this->appKey;
    }

    public function getAppSecret(): string
    {
        return $this->appSecret;
    }
}
