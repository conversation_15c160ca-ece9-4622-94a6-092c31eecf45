<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieRegisterReceivableSettlementDto
{
    public function __construct(
        public string $codigo_lancamento,
        public int $codigo_conta_corrente,
        public float $valor,
        public string $data,
        public string $observacao = '',
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'codigo_lancamento' => $this->codigo_lancamento,
            'codigo_conta_corrente' => $this->codigo_conta_corrente,
            'valor' => $this->valor,
            'data' => $this->data,
            'observacao' => $this->observacao,
            'conciliar_documento' => 'N',
        ]);
    }
}
