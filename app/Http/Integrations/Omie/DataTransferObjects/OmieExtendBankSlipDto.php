<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

use Carbon\Carbon;

class OmieExtendBankSlipDto
{
    public function __construct(
        public string $nCodTitulo,
        public Carbon $dDtVenc,
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'nCodTitulo' => $this->nCodTitulo,
            'dDtVenc' => $this->dDtVenc->format('d/m/Y'),
        ]);
    }
}
