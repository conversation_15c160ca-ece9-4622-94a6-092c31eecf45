<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieDeleteCustomerDto
{
    public function __construct(
        public int $codigo_cliente_omie,
        public string $codigo_cliente_integracao,
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'codigo_cliente_omie' => $this->codigo_cliente_omie,
            'codigo_cliente_integracao' => $this->codigo_cliente_integracao,
        ]);
    }
}
