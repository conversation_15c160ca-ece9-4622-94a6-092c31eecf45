<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieCreateReceivableDto
{
    public function __construct(
        public string $codigo_lancamento_integracao,
        public int $codigo_cliente_fornecedor,
        public string $data_vencimento,
        public string $data_previsao,
        public float $valor_documento,
        public int $id_conta_corrente,
        public string $numero_documento,
        public string $data_emissao,
        public string $codigo_categoria = '1.01.02' // mapear centro de custo/categoria
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'codigo_lancamento_integracao' => $this->codigo_lancamento_integracao,
            'codigo_cliente_fornecedor' => $this->codigo_cliente_fornecedor,
            'data_vencimento' => $this->data_vencimento,
            'data_previsao' => $this->data_previsao,
            'valor_documento' => $this->valor_documento,
            'id_conta_corrente' => $this->id_conta_corrente,
            'numero_documento' => $this->numero_documento,
            'data_emissao' => $this->data_emissao,
            'codigo_categoria' => $this->codigo_categoria,
        ]);
    }
}
