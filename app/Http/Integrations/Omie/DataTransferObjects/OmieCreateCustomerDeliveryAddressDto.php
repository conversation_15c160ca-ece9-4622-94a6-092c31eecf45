<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieCreateCustomerDeliveryAddressDto
{
    public function __construct(
        public string $entRazaoSocial,
        public string $entCnpjCpf,
        public string $entEndereco,
        public string $entNumero,
        public string $entCEP,
        public string $entEstado,
        public string $entCidade,
        public ?string $entBairro = null,
        public string $entSepararEndereco = 'N',
        public ?string $entComplemento = null,
        public ?string $entTelefone = null,
        public ?string $entIE = null,
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'entRazaoSocial' => $this->entRazaoSocial,
            'entCnpjCpf' => $this->entCnpjCpf,
            'entEndereco' => $this->entEndereco,
            'entNumero' => $this->entNumero,
            'entComplemento' => $this->entComplemento,
            'entBairro' => $this->entBairro,
            'entCEP' => $this->entCEP,
            'entEstado' => $this->entEstado,
            'entCidade' => $this->entCidade,
            'entSepararEndereco' => $this->entSepararEndereco,
            'entTelefone' => $this->entTelefone,
            'entIE' => $this->entIE,
        ]);
    }
}
