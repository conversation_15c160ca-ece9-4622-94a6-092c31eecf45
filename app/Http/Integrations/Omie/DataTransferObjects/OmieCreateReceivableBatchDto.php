<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieCreateReceivableBatchDto
{
    /**
     * Create a new instance.
     *
     * @param  \App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableDto[] $conta_receber_cadastro
     * @param  int $lote
     */
    public function __construct(
        public array $conta_receber_cadastro,
        public int $lote,
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'conta_receber_cadastro' => $this->conta_receber_cadastro,
            'lote' => $this->lote,
        ]);
    }
}
