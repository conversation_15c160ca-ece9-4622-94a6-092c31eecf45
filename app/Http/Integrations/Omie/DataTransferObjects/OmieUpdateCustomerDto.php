<?php

namespace App\Http\Integrations\Omie\DataTransferObjects;

class OmieUpdateCustomerDto
{
    public function __construct(
        public int $codigo_cliente_omie,
        public string $email,
        public string $razao_social,
        public string $nome_fantasia,
        public string $cnpj_cpf,
        public string $telefone1_ddd,
        public string $telefone1_numero,
        public string $contato,
        public string $endereco,
        public string $endereco_numero,
        public string $bairro,
        public string $cidade_ibge,
        public string $estado,
        public string $cep,
        public OmieCreateCustomerDeliveryAddressDto $enderecoEntrega,
        public bool $inativo,
        public ?string $inscricao_estadual = null,
        public ?string $inscricao_municipal = null,
        public ?string $complemento = null,
        public ?string $codigo_pais = '1058',
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'codigo_cliente_omie' => $this->codigo_cliente_omie,
            'email' => $this->email,
            'razao_social' => $this->razao_social,
            'nome_fantasia' => $this->nome_fantasia,
            'cnpj_cpf' => $this->cnpj_cpf,
            'telefone1_ddd' => $this->telefone1_ddd,
            'telefone1_numero' => $this->telefone1_numero,
            'contato' => $this->contato,
            'endereco' => $this->endereco,
            'endereco_numero' => $this->endereco_numero,
            'bairro' => $this->bairro,
            'cidade_ibge' => $this->cidade_ibge,
            'estado' => $this->estado,
            'cep' => $this->cep,
            'enderecoEntrega' => $this->enderecoEntrega->toArray(),
            'inativo' => $this->inativo ? 'S' : 'N',
            'inscricao_estadual' => $this->inscricao_estadual,
            'inscricao_municipal' => $this->inscricao_municipal,
            'complemento' => $this->complemento,
            'codigo_pais' => $this->codigo_pais,
        ]);
    }
}
