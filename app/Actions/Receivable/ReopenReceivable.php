<?php

namespace App\Actions\Receivable;

use App\Actions\BankSlip\CancelBankSlip;
use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieDeleteReceivableDto;
use App\Http\Integrations\Omie\Services\OmieReceivableService;
use App\Models\BankSlip;
use App\Models\Receivable;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ReopenReceivable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @return void
     */
    public function handle(Receivable $receivable): void
    {
        $receivable->bankSlips
            ->filter(fn(BankSlip $bankSlip): bool => $bankSlip->status === BankSlipStatusEnum::Open->value)
            ->each(function (BankSlip $bankSlip): void {
                try {
                    CancelBankSlip::run($bankSlip);
                } catch (Throwable $th) {
                    error($th);
                }
            });

        DB::transaction(function () use ($receivable): void {
            if ($receivable->omie_id) {
                OmieReceivableService::make()->delete($receivable, new OmieDeleteReceivableDto($receivable->omie_id));
            }

            $receivable->update(['omie_id' => null]);
        });

        $receivable->document->incomeCustomerCoupons()->delete();

        $receivable->document->incomeCollections()->delete();

        $receivable->document->delete();

        $receivable->delete();
    }
}
