<?php

namespace App\Actions\Receivable;

use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateBankSlipDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableBatchDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Http\Integrations\Omie\Services\OmieReceivableService;
use App\Models\Income;
use App\Models\OmieReceivableBatch;
use App\Models\Operator;
use App\Models\Receivable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class ConsolidateReceivables
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.default_names.integrations.omie.receivable.send'));
    }

    /**
     * Handle the action.
     *
     * @param  int|null $userId
     * @return void
     */
    public function handle(?int $userId = null): void
    {
        while (true) {
            /** @var \Illuminate\Support\Collection $pendingReceivables */
            $pendingReceivables = Receivable::query()
                ->whereNull('omie_id')
                ->get()
                ->take(10);

            if ($pendingReceivables->count() === 0) {
                break;
            }

            $receivableDtos = array_values(
                $pendingReceivables
                    ->filter(fn(Receivable $receivable): bool => !is_null($receivable->customer->omie_id))
                    ->map(fn(Receivable $receivable): array => [
                        'codigo_lancamento_integracao' => $receivable->document->document_number,
                        'codigo_cliente_fornecedor' => $receivable->customer->omie_id,
                        'data_vencimento' => carbon($receivable->expires_at)->format('d/m/Y'),
                        'data_previsao' => carbon($receivable->expires_at)->format('d/m/Y'),
                        'valor_documento' => $receivable->updated_amount,
                        'id_conta_corrente' => $receivable->customer->defaultBankAccountWallet->omie_id,
                        'numero_documento' => $receivable->document->document_number,
                        'data_emissao' => carbon($receivable->issued_at)->format('d/m/Y'),
                    ])
                    ->toArray()
            );

            try {
                $lastOmieReceivableBatchId = OmieReceivableBatch::query()
                    ->latest()
                    ->first()
                    ->id ?? 0;

                $response = DB::transaction(function () use ($lastOmieReceivableBatchId, $receivableDtos, $pendingReceivables, $userId) {
                    /** @var \App\Models\OmieReceivableBatch $omieReceivableBatch */
                    $omieReceivableBatch = OmieReceivableBatch::create(['operator_id' => Operator::query()->where('user_id', $userId)->first()->id]);

                    foreach ($pendingReceivables as $pendingReceivable) {
                        /** @var \App\Models\Receivable $pendingReceivable */
                        $omieReceivableBatch->omieReceivableBatchReceivables()->create(['receivable_id' => $pendingReceivable->id]);
                    }

                    return OmieReceivableService::make()->createBatch(
                        $omieReceivableBatch,
                        new OmieCreateReceivableBatchDto($receivableDtos, $lastOmieReceivableBatchId + 1)
                    );
                });
            } catch (Throwable $th) {
                $pendingReceivables->each(function (Receivable $receivable): void {
                    $receivable->update(['omie_id' => '-1']);
                });

                throw_error($th);
            }

            collect($response->status_lote)->each(function (mixed $omieApiReceivable) use (&$pendingReceivables): void {
                try {
                    /** @var \App\Models\Receivable $pendingReceivable */
                    $pendingReceivable = $pendingReceivables
                        ->filter(fn(Receivable $receivable): bool => $receivable->document->document_number === $omieApiReceivable->codigo_lancamento_integracao)
                        ->first();

                    $pendingReceivable->update(['omie_id' => $omieApiReceivable->codigo_lancamento_omie]);

                    dispatch(function () use ($pendingReceivable): void {
                        sleep(3);

                        $pendingReceivable->bankSlips()->create([
                            'customer_id' => $pendingReceivable->customer_id,
                            'amount' => $pendingReceivable->updated_amount,
                            'expires_at' => $pendingReceivable->expires_at,
                            'issued_at' => now(),
                            'omie_data' => OmieBankSlipService::make()->create($pendingReceivable, new OmieCreateBankSlipDto($pendingReceivable->omie_id)),
                        ]);
                    })->onQueue(config('queue.default_names.integrations.omie.bank_slip.send'));
                } catch (Throwable $th) {
                    error($th);
                }
            });
        }

        if ($userId) {
            $receivablesNotIntegrated = trim(
                Income::query()
                    ->select(['id', 'document_number'])
                    ->whereHas('receivables', function (Builder $query): Builder {
                        return $query->where('omie_id', '-1');
                    })
                    ->get()
                    ->pluck('document_number')
                    ->implode(',')
            );

            $message = 'O processamento de consolidação das faturas foi finalizado.';

            if (trim($receivablesNotIntegrated !== '')) {
                $message .= ' As seguintes faturas não foram integradas: ' . $receivablesNotIntegrated;
            }

            success_database_notification($userId, $message, true);
        }
    }
}
