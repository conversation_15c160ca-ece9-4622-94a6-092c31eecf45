<?php

namespace App\Actions\Receivable;

use App\Actions\ReceivableSettlement\CreateReceivableSettlement;
use App\Enums\ReceivableStatusEnum;
use App\Models\BankAccountWallet;
use App\Models\Receivable;
use App\Models\ReceivableSettlement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class SettleReceivable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  float $settlementAmount
     * @param  \Carbon\Carbon $settledAt
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @param  string|null $additionalInfo
     * @param  bool $integrate
     * @return \App\Models\Receivable
     */
    public function handle(
        Receivable $receivable,
        float $settlementAmount,
        Carbon $settledAt,
        BankAccountWallet $bankAccountWallet,
        ?string $additionalInfo = null,
        bool $integrate = true
    ): Receivable {
        if ($receivable->settled_at) {
            return $receivable;
        }

        try {
            $receivable->refresh()->load('receivableSettlements');

            $settlementsAmount = $receivable->receivableSettlements->sum(function (ReceivableSettlement $receivableSettlement): float {
                return (float) $receivableSettlement->updated_amount;
            });

            if ($settlementsAmount >= (float) $receivable->updated_amount) {
                $receivable->update([
                    'status' => ReceivableStatusEnum::Settled->value,
                    'settled_at' => $settledAt ?? now()
                ]);

                return $receivable;
            }

            DB::transaction(function () use ($receivable, $settlementAmount, $settledAt, $bankAccountWallet, $additionalInfo, $integrate) {
                CreateReceivableSettlement::run(
                    $receivable,
                    $settlementAmount,
                    $settledAt ?? now(),
                    1,
                    $bankAccountWallet,
                    $additionalInfo,
                    $integrate
                );

                $receivable->update([
                    'status' => ReceivableStatusEnum::Settled->value,
                    'settled_at' => $settledAt ?? now()
                ]);
            });

            return $receivable;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
