<?php

namespace App\Actions\Receivable;

use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieExtendBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\BankSlip;
use App\Models\Receivable;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class UpdateReceivable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  array $data
     * @return \App\Models\Receivable
     */
    public function handle(Receivable $receivable, array $data): Receivable
    {
        $oldExpiresAt = $receivable->expires_at;

        try {
            $receivable->update($data);

            $openBankSlips = $receivable->bankSlips->filter(fn(BankSlip $bankSlip): bool => $bankSlip->status === BankSlipStatusEnum::Open->value);

            if ($openBankSlips->count() > 0 && carbon($data['expires_at'])->startOfDay()->notEqualTo(carbon($oldExpiresAt)->startOfDay())) {
                $this->handleBankSlipExtension($openBankSlips, carbon($data['expires_at']));
            }

            return $receivable;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    /**
     * Handle the bank slip date extension.
     *
     * @param  \Illuminate\Support\Collection $openBankSlips
     * @param  \Carbon\Carbon $newExpiresAt
     * @return void
     */
    protected function handleBankSlipExtension(Collection $openBankSlips, Carbon $newExpiresAt): void
    {
        $openBankSlips->each(function (BankSlip $bankSlip) use ($newExpiresAt): void {
            $bankSlip->update(['expires_at' => $newExpiresAt]);

            $bankSlip->receivable->update([
                'omie_data' => OmieBankSlipService::make()->extend($bankSlip->receivable, new OmieExtendBankSlipDto($bankSlip->receivable->omie_id, $newExpiresAt))
            ]);
        });
    }
}
