<?php

namespace App\Actions\Receivable;

use App\Enums\BankSlipStatusEnum;
use App\Enums\CustomerPaymentRecoveryLogTypeEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\Receivable;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class LoadWhatsAppWaMeLink
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $record
     * @return mixed
     */
    public function handle(Receivable $record): mixed
    {
        /** @var \App\Models\BankSlip $latestBankSlip */
        $latestBankSlip = $record->bankSlips()
            ->where('status', BankSlipStatusEnum::Open->value)
            ->latest()
            ->first();

        $latestBankSlip->update(['omie_data' => OmieBankSlipService::make()->get($record, new OmieGetBankSlipDto($record->omie_id))]);

        $bankSlipUrl = route('receivables.bank_slip', ['token' => base64_encode($record->document_id . '-' . Str::random(32))]);

        $unmaskedPhone = $record->customer->billing_phone
            ? unmask_phone($record->customer->billing_phone)
            : unmask_phone($record->customer->phone_1);

        $baseUrl = 'https://wa.me/55' . $unmaskedPhone . '?text=';

        $text = "*Prezado cliente*,\n"
            . "Ainda não identificamos o pagamento da fatura #{$record->document_id}, com vencimento em " . format_date($record->expires_at) . ".\n"
            . "Seguem os links abaixo para conveniência:\n\n"
            . "Fatura: " . route('receivables.billing', ['token' => base64_encode($record->document_id . '-' . Str::random(32))]) . "\n\n"
            . "Boleto: " . $bankSlipUrl;

        $record->customer->customerPaymentRecoveryLogs()->create([
            'receivable_id' => $record->id,
            'type' => CustomerPaymentRecoveryLogTypeEnum::WaMe->value,
        ]);

        return redirect()->to($baseUrl . urlencode($text));
    }
}
