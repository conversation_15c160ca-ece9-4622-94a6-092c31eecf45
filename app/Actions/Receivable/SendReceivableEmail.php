<?php

namespace App\Actions\Receivable;

use App\Actions\Core\SendEmail;
use App\Models\Receivable;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisle<PERSON>\Actions\Decorators\JobDecorator;
use Throwable;

class SendReceivableEmail
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(
            config('queue.default_names.p4m.emails.data_send')
        );
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  bool $automatic
     * @param  bool $throwExceptionOnError
     * @return void
     */
    public function handle(Receivable $receivable, bool $automatic = false, bool $throwExceptionOnError = false): void
    {
        $toEmails = explode(',', $receivable->customer->billing_email);

        if (is_null($toEmails)) {
            throw_if(!$automatic, 'Não existem endereços de e-mail cadastrados para envio do e-mail de faturamento.');
            return;
        }

        $subject = p4m_tenant()->getCompanyName() . " - fatura nº{$receivable->id} ({$receivable->customer->name})";

        $message = view('emails.receivable-email', [
            'receivable' => $receivable,
            'companyName' => p4m_tenant()->getCompanyName()
        ])->render();

        try {
            SendEmail::run($toEmails, $subject, $message);
        } catch (Throwable $th) {
            error($th);

            if ($throwExceptionOnError) {
                throw $th;
            }

            return;
        }

        $receivable->update(['email_sent' => true]);

        $receivable->receivableEmails()->create([
            'to_emails' => $toEmails,
            'message' => $message,
            'email_sent_at' => now()
        ]);
    }
}
