<?php

namespace App\Actions\Receivable\Integrations\Omie;

use App\Actions\BankSlip\CancelBankSlip;
use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateBankSlipDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateReceivableDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Http\Integrations\Omie\Services\OmieReceivableService;
use App\Models\BankSlip;
use App\Models\Receivable;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateReceivableInOmie
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  int $userId
     * @return \App\Models\Receivable
     */
    public function handle(Receivable $receivable, int $userId): Receivable
    {
        try {
            $receivable->bankSlips
                ->filter(fn(BankSlip $bankSlip): bool => $bankSlip->status === BankSlipStatusEnum::Open->value)
                ->each(function (BankSlip $bankSlip): void {
                    try {
                        CancelBankSlip::run($bankSlip);
                    } catch (Throwable $th) {
                        error($th);
                    }
                });

            return DB::transaction(function () use ($receivable): Receivable {
                $omieCreateReceivableDto = new OmieCreateReceivableDto(
                    codigo_lancamento_integracao: $receivable->id,
                    codigo_cliente_fornecedor: $receivable->customer->omie_id,
                    data_vencimento: carbon($receivable->expires_at)->format('d/m/Y'),
                    data_previsao: carbon($receivable->expires_at)->format('d/m/Y'),
                    valor_documento: $receivable->updated_amount,
                    id_conta_corrente: $receivable->customer->defaultBankAccountWallet->omie_id,
                    numero_documento: $receivable->document->id,
                    data_emissao: carbon($receivable->issued_at)->format('d/m/Y'),
                );

                $receivable->update(['omie_id' => OmieReceivableService::make()->create($receivable, $omieCreateReceivableDto)]);

                $receivable->bankSlips()->create([
                    'customer_id' => $receivable->customer_id,
                    'amount' => $receivable->updated_amount,
                    'expires_at' => $receivable->expires_at,
                    'issued_at' => now(),
                    'omie_data' => json_decode(json_encode(OmieBankSlipService::make()->create($receivable, new OmieCreateBankSlipDto($receivable->omie_id))), true),
                ]);

                return $receivable;
            });
        } catch (Throwable $th) {
            database_notification(
                userId: $userId,
                body: "O boleto da fatura #$receivable->document_id não foi gerado.",
                onlyCreationUser: true
            );

            throw_error($th);
        }
    }
}
