<?php

namespace App\Actions\Collections;

use App\Models\Collection;
use App\Models\Customer;
use App\Models\DeliveryBatchDelivery;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class BuildCollectionsHistoryTable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function handle(): Factory|View
    {
        return view('reports.collections.history', [
            'lines' => Collection::query()
                ->with(['customer:id,trading_name'])
                ->whereNull('deleted_at')
                ->orderBy('collected_at', 'desc')
                ->take(5000)
                ->get()
                ->map(function (Collection $collection): array {
                    $date = carbon($collection->collected_at);

                    // Get day of week (1 = Sunday, 2 = Monday, etc.)
                    $dayOfWeek = $date->dayOfWeek + 1;
                    if ($dayOfWeek === 8) {
                        $dayOfWeek = 1; // Convert 8 (next Sunday) to 1
                    }

                    // Get day name in Portuguese
                    $dayNames = [
                        1 => 'Domingo',
                        2 => 'Segunda',
                        3 => 'Terça',
                        4 => 'Quarta',
                        5 => 'Quinta',
                        6 => 'Sexta',
                        7 => 'Sábado'
                    ];

                    // Buscar o roteiro da entrega correspondente
                    $roteiro = DeliveryBatchDelivery::query()
                        ->where('customer_id', $collection->customer_id)
                        ->whereHas('delivery', fn(Builder $query): Builder => $query->whereDate('delivered_at', $date->format('Y-m-d')))
                        ->value('itinerary');

                    return [
                        'data_coleta_id_cliente' => $date->format('d/m/Y') . '_' . $collection->customer_id,
                        'data' => $date->format('d/m/Y'),
                        'dia_semana' => $dayNames[$dayOfWeek],
                        'id_cliente' => $collection->customer_id,
                        'nome_fantasia' => $collection->customer->trading_name,
                        'roteiro' => $roteiro,
                        'id_cliente_num_dia_semana' => $collection->customer_id . '_' . $dayOfWeek,
                    ];
                })
                ->toArray()
        ]);
    }
}