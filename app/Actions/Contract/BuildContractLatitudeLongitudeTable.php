<?php

namespace App\Actions\Contract;

use App\Models\Customer;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class BuildContractLatitudeLongitudeTable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function handle(): Factory|View
    {
        return view('reports.contracts.contract-latitude-longitude-table', [
            'lines' => Customer::query()
                ->select([
                    'id',
                    'trading_name',
                    'address_address',
                    'address_number',
                    'address_district',
                    'address_city_id',
                    'address_state_id',
                    'payment_recovery_setting_id',
                ])
                ->with([
                    'city:id,name',
                    'state:id,abbreviation',
                    'receivables',
                    'paymentRecoverySetting:id,overdue_day_count_from'
                ])
                ->whereNull('deleted_at')
                ->get()
                ->map(function (Customer $customer): array {
                    $openReceivables = $customer->receivables
                        ->whereNull('settled_at')
                        ->where('expires_at', '<', now()->format('Y-m-d'))
                        ->sortByDesc('expires_at');

                    return [
                        'customer_id' => $customer->id,
                        'customer_trading_name' => $customer?->trading_name,
                        'latitude_longitude' => $customer?->latitude . ',' . $customer?->longitude,
                        'open_receivable_count' => $openReceivables->count(),
                        'max_overdue_days' => $openReceivables->count() > 0
                            ? carbon($openReceivables->first()->expires_at)->diffInDays(now())
                            : 0,
                        'payment_recovery_rule' => $customer?->paymentRecoverySetting?->overdue_day_count_from,
                    ];
                })
                ->toArray()
        ]);
    }
}
