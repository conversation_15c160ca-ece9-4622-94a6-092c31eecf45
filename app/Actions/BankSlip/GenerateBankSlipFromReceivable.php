<?php

namespace App\Actions\BankSlip;

use App\Actions\Receivable\Integrations\Omie\CreateReceivableInOmie;
use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\BankAccountWallet;
use App\Models\BankSlip;
use App\Models\Receivable;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GenerateBankSlipFromReceivable
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(
            config('queue.default_names.integrations.tecnospeed.plugboleto.bank_slips.data_send')
        );
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  \App\Models\BankAccountWallet|null $bankAccountWallet
     * @param  int $userId
     * @return \App\Models\BankSlip
     */
    public function handle(
        Receivable $receivable,
        ?BankAccountWallet $bankAccountWallet,
        int $userId
    ): BankSlip {
        $receivable->bankSlips
            ->filter(fn(BankSlip $bankSlip): bool => $bankSlip->status === BankSlipStatusEnum::Open->value)
            ->each(function (BankSlip $bankSlip): void {
                try {
                    CancelBankSlip::run($bankSlip);
                } catch (Throwable $th) {
                    error($th);
                }
            });

        if (app()->isLocal()) {
            /** @var \App\Models\BankSlip $bankSlip */
            $bankSlip = $receivable->bankSlips()->create([
                'customer_id' => $receivable->customer_id,
                'bank_id' => $bankAccountWallet?->bankAccount->bank_id,
                'bank_code' => $bankAccountWallet?->bankAccount->bank->code,
                'bank_name' => $bankAccountWallet?->bankAccount->bank->name,
                'bank_account_id' => $bankAccountWallet?->bank_account_id,
                'bank_account_branch_number' => $bankAccountWallet?->bankAccount->branch_number,
                'bank_account_branch_digit' => $bankAccountWallet?->bankAccount->branch_digit,
                'bank_account_number' => $bankAccountWallet?->bankAccount->number,
                'bank_account_digit' => $bankAccountWallet?->bankAccount->digit,
                'bank_account_wallet_id' => $bankAccountWallet?->id,
                'bank_account_wallet_number' => $bankAccountWallet?->number,
                'amount' => $receivable->updated_amount,
                'expires_at' => $receivable->expires_at,
                'issued_at' => now(),
            ]);

            return $bankSlip;
        }

        if (!$receivable->omie_id) {
            CreateReceivableInOmie::run($receivable, $userId);
            $receivable->refresh();
        }

        try {
            sleep(1);

            // $response = TecnospeedPlugBoletoBankSlipService::make()->createBatch(
            //     [$bankSlip],
            //     $bankAccountWallet
            // );

            // /** @var \App\Models\TecnospeedPlugBoletoIntegrationLog $tecnospeedPlugBoletoIntegrationLog */
            // $tecnospeedPlugBoletoIntegrationLog = TecnospeedPlugBoletoIntegrationLog::create([
            //     'integratable_id' => $receivable->id,
            //     'integratable_type' => get_class($receivable),
            //     'response' => $response
            // ]);

            // if (trim(mb_strtolower($tecnospeedPlugBoletoIntegrationLog->response['_status'])) === 'sucesso') {
            //     $bankSlip->update([
            //         'tecnospeed_id' => $tecnospeedPlugBoletoIntegrationLog->response['_dados']['_sucesso'][0]['idintegracao']
            //     ]);
            // } else {
            //     error('ERROR: ' . json_encode($response));
            // }

            /** @var \App\Models\BankSlip $bankSlip */
            $bankSlip = $receivable->bankSlips()->create([
                'customer_id' => $receivable->customer_id,
                'bank_id' => $bankAccountWallet?->bankAccount->bank_id,
                'bank_code' => $bankAccountWallet?->bankAccount->bank->code,
                'bank_name' => $bankAccountWallet?->bankAccount->bank->name,
                'bank_account_id' => $bankAccountWallet?->bank_account_id,
                'bank_account_branch_number' => $bankAccountWallet?->bankAccount->branch_number,
                'bank_account_branch_digit' => $bankAccountWallet?->bankAccount->branch_digit,
                'bank_account_number' => $bankAccountWallet?->bankAccount->number,
                'bank_account_digit' => $bankAccountWallet?->bankAccount->digit,
                'bank_account_wallet_id' => $bankAccountWallet?->id,
                'bank_account_wallet_number' => $bankAccountWallet?->number,
                'amount' => $receivable->updated_amount,
                'expires_at' => $receivable->expires_at,
                'issued_at' => now(),
                'omie_data' => OmieBankSlipService::make()->create($receivable, new OmieCreateBankSlipDto($receivable->omie_id)),
            ]);

            success_database_notification($userId, "O boleto referente à fatura {$receivable->document->document_number} foi gerado.");
        } catch (Throwable $th) {
            database_notification(
                userId: $userId,
                body: "O boleto da fatura #$receivable->document_id não foi gerado.",
                onlyCreationUser: true
            );

            throw_error($th);
        }

        return $bankSlip;
    }
}
