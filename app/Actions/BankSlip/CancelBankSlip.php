<?php

namespace App\Actions\BankSlip;

use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieCancelBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\BankSlip;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CancelBankSlip
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\BankSlip $bankSlip
     * @param  bool $fromWebhook
     * @param  mixed $webhookData
     * @return \App\Models\BankSlip
     */
    public function handle(BankSlip $bankSlip): BankSlip {
        if (!is_null($bankSlip->cancelled_at)) {
            return $bankSlip;
        }

        try {
            OmieBankSlipService::make()->cancel(
                $bankSlip->receivable,
                new OmieCancelBankSlipDto($bankSlip->receivable->omie_id)
            );

            $bankSlip->update([
                'cancelled_at' => now(),
                'status' => BankSlipStatusEnum::Cancelled->value,
            ]);

            return $bankSlip;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
