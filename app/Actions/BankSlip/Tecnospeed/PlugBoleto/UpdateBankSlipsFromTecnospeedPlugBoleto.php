<?php

namespace App\Actions\BankSlip\Tecnospeed\PlugBoleto;

use App\Actions\Webhook\Tecnospeed\PlugBoleto\ProcessTecnospeedPlugBoletoCancelledBankSlip;
use App\Actions\Webhook\Tecnospeed\PlugBoleto\ProcessTecnospeedPlugBoletoPaidBankSlip;
use App\Http\Integrations\Tecnospeed\PlugBoleto\Services\TecnospeedPlugBoletoBankSlipService;
use App\Models\BankSlip;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateBankSlipsFromTecnospeedPlugBoleto
{
    use AsAction;

    protected TecnospeedPlugBoletoBankSlipService $service;

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(string $draweeTaxIdNumber): void
    {
        $this->service = TecnospeedPlugBoletoBankSlipService::make($draweeTaxIdNumber);

        BankSlip::query()
            ->get()
            ->each(function (BankSlip $bankSlip): void {
                tap(
                    $this->service->getById($bankSlip->tecnospeed_id),
                    fn ($response) => $this->processBankSlip($response)
                );
            });
    }

    /**
     * Process the bank slip.
     *
     * @param  mixed $response
     * @return void
     */
    protected function processBankSlip(mixed $response): void
    {
        if (empty($response->_dados)) {
            return;
        }

        $tecnospeedPlugBoletoData = $response->_dados[0];

        if ($tecnospeedPlugBoletoData->situacao === 'LIQUIDADO') {
            ProcessTecnospeedPlugBoletoPaidBankSlip::run(json_decode(json_encode([
                'titulo' => [
                    'situacao' => 'LIQUIDADO',
                    'idintegracao' => $tecnospeedPlugBoletoData->IdIntegracao,
                    'PagamentoDataCredito' => $tecnospeedPlugBoletoData->PagamentoDataCredito,
                    'PagamentoValorPago' => $tecnospeedPlugBoletoData->PagamentoValorCredito,
                ]
            ])));

            return;
        }

        if ($tecnospeedPlugBoletoData->situacao === 'BAIXADO') {
            ProcessTecnospeedPlugBoletoCancelledBankSlip::run(json_decode(json_encode([
                'titulo' => [
                    'situacao' => 'BAIXADO',
                    'idintegracao' => $tecnospeedPlugBoletoData->IdIntegracao,
                    'TituloMovimentos' => [[
                        'data' => $tecnospeedPlugBoletoData->TituloMovimentos[0]->data
                    ]]
                ]
            ])));

            return;
        }
    }
}
