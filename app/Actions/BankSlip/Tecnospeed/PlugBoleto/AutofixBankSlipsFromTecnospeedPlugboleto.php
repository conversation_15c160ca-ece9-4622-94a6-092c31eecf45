<?php

namespace App\Actions\BankSlip\Tecnospeed\Plugboleto;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Services\TecnospeedPlugBoletoBankSlipService;
use App\Models\BankAccountWallet;
use App\Models\BankSlip;
use App\Models\Income;
use App\Models\Receivable;
use App\Models\TecnospeedPlugBoletoIntegrationLog;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class AutofixBankSlipsFromTecnospeedPlugboleto
{
    use AsAction;

    public string $commandSignature = 'plugboleto:autofix_bank_slips_from_tecnospeed_plugboleto {tenant?}';
    public string $commandDescription = 'Autofix bank slips with existing logs but no bank slip models.';

    protected TecnospeedPlugBoletoBankSlipService $service;

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        $tenant = $command->argument('tenant') ?? 'brisa';

        $this->handle($tenant);

        $command->info('Done!');
    }

    /**
     * Handle the action.
     *
     * @param  string $tenant
     * @return void
     */
    public function handle(string $tenant): void
    {
        tenancy()->initialize($tenant);

        $this->service = TecnospeedPlugBoletoBankSlipService::make(tenant('tax_id_number'));

        TecnospeedPlugBoletoIntegrationLog::query()
            ->get()
            ->each(function (TecnospeedPlugBoletoIntegrationLog $tecnospeedPlugBoletoIntegrationLog): void {
                if (
                    $tecnospeedPlugBoletoIntegrationLog->response['_status'] !== 'sucesso'
                    || !isset($tecnospeedPlugBoletoIntegrationLog->response['_dados']['_sucesso'][0]['idintegracao'])
                ) {
                    return;
                }

                /** @var \App\Models\BankSlip $bankSlip */
                $bankSlip = BankSlip::query()
                    ->where('tecnospeed_id', $tecnospeedPlugBoletoIntegrationLog->response['_dados']['_sucesso'][0]['idintegracao'])
                    ->first();

                if ($bankSlip) {
                    return;
                }

                tap(
                    $this->service->getById($tecnospeedPlugBoletoIntegrationLog->response['_dados']['_sucesso'][0]['idintegracao']),
                    fn ($response) => $this->processBankSlip($response)
                );
            });
    }

    /**
     * Process the bank slip.
     *
     * @param  mixed $response
     * @return void
     */
    protected function processBankSlip(mixed $response): void
    {
        if (empty($response->_dados)) {
            return;
        }

        $tecnospeedPlugBoletoData = $response->_dados[0];

        if (!isset($tecnospeedPlugBoletoData->situacao) || !in_array($tecnospeedPlugBoletoData->situacao, ['REGISTRADO', 'SALVO', 'EMITIDO'])) {
            if (!isset($tecnospeedPlugBoletoData->situacao)) {
                info('AutofixBankSlipsFromTecnospeedPlugboleto::processBankSlip: ERROR for structure - ' . json_encode($tecnospeedPlugBoletoData));
            }

            return;
        }

        /** @var \App\Models\Receivable $receivable */
        $receivable = Receivable::query()
            ->whereHasMorph('document', [Income::class], function (Builder $query) use ($tecnospeedPlugBoletoData): Builder {
                return $query->where('document_number', $tecnospeedPlugBoletoData->TituloNumeroDocumento);
            })
            ->first();

        if (!$receivable) {
            return;
        }

        /** @var \App\Models\BankAccountWallet $bankAccountWallet */
        $bankAccountWallet = BankAccountWallet::query()
            ->where('number', $tecnospeedPlugBoletoData->CedenteNumeroConvenio)
            ->whereRelation('bankAccount', 'number', $tecnospeedPlugBoletoData->CedenteConta)
            ->whereRelation('bankAccount.bank', 'code', substr($tecnospeedPlugBoletoData->CedenteCodigoBanco, 1))
            ->first();

        $receivable->bankSlips()->create([
            'customer_id' => $receivable->customer_id,
            'bank_id' => $bankAccountWallet->bankAccount->bank_id,
            'bank_code' => $bankAccountWallet->bankAccount->bank->code,
            'bank_name' => $bankAccountWallet->bankAccount->bank->name,
            'bank_account_id' => $bankAccountWallet->bank_account_id,
            'bank_account_branch_number' => $bankAccountWallet->bankAccount->branch_number,
            'bank_account_branch_digit' => $bankAccountWallet->bankAccount->branch_digit,
            'bank_account_number' => $bankAccountWallet->bankAccount->number,
            'bank_account_digit' => $bankAccountWallet->bankAccount->digit,
            'bank_account_wallet_id' => $bankAccountWallet->id,
            'bank_account_wallet_number' => $bankAccountWallet->number,
            'amount' => $receivable->updated_amount,
            'expires_at' => $receivable->expires_at,
            'issued_at' => Carbon::createFromFormat('d/m/Y H:i:s', $tecnospeedPlugBoletoData->TituloDataEmissao),
            'tecnospeed_id' => $tecnospeedPlugBoletoData->IdIntegracao,
        ]);
    }
}
