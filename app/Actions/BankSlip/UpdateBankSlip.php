<?php

namespace App\Actions\BankSlip;

use App\Models\BankSlip;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class UpdateBankSlip
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\BankSlip $bankSlip
     * @param  array $data
     * @return \App\Models\BankSlip
     */
    public function handle(BankSlip $bankSlip, array $data): BankSlip
    {
        try {
            $bankSlip->update($data);
            $bankSlip->receivable->update($data);

            CancelBankSlip::run($bankSlip);

            GenerateBankSlipFromReceivable::run($bankSlip->receivable, $bankSlip->bankAccountWallet);

            return $bankSlip;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
