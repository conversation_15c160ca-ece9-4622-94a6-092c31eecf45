<?php

namespace App\Actions\BankSlip\Integrations\Omie;

use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\Receivable;
use Lorisleiva\Actions\Concerns\AsAction;

class OpenOmieBankSlip
{
    use AsAction;

    public function asController(string $token)
    {
        $decodedToken = base64_decode($token);

        return $this->handle(
            Receivable::query()
                ->where('document_id', explode('-', $decodedToken)[0])
                ->first()
        );
    }

    public function handle(Receivable $receivable)
    {
        $response = OmieBankSlipService::make()->get($receivable, new OmieGetBankSlipDto($receivable->omie_id));

        $bankSlip = $receivable->bankSlips()
            ->where('status', BankSlipStatusEnum::Open->value)
            ->latest()
            ->first();

        $bankSlip->update(['omie_data' => $response]);

        return redirect()->to($bankSlip->omie_data['cLinkBoleto']);
    }
}
