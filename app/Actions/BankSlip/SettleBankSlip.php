<?php

namespace App\Actions\BankSlip;

use App\Actions\Receivable\SettleReceivable;
use App\Enums\BankSlipStatusEnum;
use App\Models\BankSlip;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class SettleBankSlip
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\BankSlip $bankSlip
     * @param  float $paidAmount
     * @param  \Carbon\Carbon $settledAt
     * @return \App\Models\BankSlip
     */
    public function handle(BankSlip $bankSlip, float $paidAmount, Carbon $settledAt): BankSlip
    {
        try {
            $bankSlip->update([
                'status' => BankSlipStatusEnum::Settled->value,
                'settled_at' => $settledAt,
            ]);

            SettleReceivable::run(
                $bankSlip->receivable,
                $paidAmount,
                $settledAt,
                $bankSlip->bankAccountWallet,
                'LIQ. AUT. BOLETOS'
            );

            return $bankSlip;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
