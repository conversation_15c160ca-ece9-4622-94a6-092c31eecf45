<?php

namespace App\Actions\Customer\Integrations\Omie;

use App\Http\Integrations\Omie\DataTransferObjects\OmieCreateCustomerDeliveryAddressDto;
use App\Http\Integrations\Omie\DataTransferObjects\OmieUpdateCustomerDto;
use App\Http\Integrations\Omie\Services\OmieCustomerService;
use App\Models\Customer;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class UpdateCustomerInOmie
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.default_names.integrations.omie.customer.send'));
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return \App\Models\Customer
     */
    public function handle(Customer $customer): Customer
    {
        try {
            $omieUpdateCustomerDto = new OmieUpdateCustomerDto(
                codigo_cliente_omie: $customer->omie_id,
                email: $customer->email,
                razao_social: substr($customer->name, 0, 60),
                nome_fantasia: $customer->trading_name,
                cnpj_cpf: $customer->tax_id_number,
                telefone1_ddd: substr(get_numbers($customer->phone_1), 0, 2),
                telefone1_numero: substr(get_numbers($customer->phone_1), 2),
                contato: $customer->in_charge_person_1,
                endereco: $customer->address_address,
                endereco_numero: $customer->address_number,
                bairro: $customer->address_district,
                cidade_ibge: $customer->address_city_id,
                estado: $customer->state->abbreviation,
                cep: get_numbers($customer->address_zipcode),
                enderecoEntrega: new OmieCreateCustomerDeliveryAddressDto(
                    entRazaoSocial: substr($customer->name, 0, 60),
                    entCnpjCpf: $customer->tax_id_number,
                    entEndereco: $customer->address_address,
                    entNumero: $customer->address_number,
                    entBairro: $customer->address_district,
                    entCEP: get_numbers($customer->address_zipcode),
                    entEstado: $customer->state->abbreviation,
                    entCidade: $customer->city->name,
                    entComplemento: $customer->address_additional_info
                        ? substr($customer->address_additional_info, 0, 60)
                        : null,
                    entIE: str_replace(' ', '', $customer->state_registration),
                ),
                inativo: !$customer->active,
                inscricao_estadual: str_replace(' ', '', $customer->state_registration),
                inscricao_municipal: str_replace(' ', '', $customer->city_registration),
                complemento: $customer->address_additional_info
                    ? substr($customer->address_additional_info, 0, 60)
                    : null,
            );

            OmieCustomerService::make()->update($customer, $omieUpdateCustomerDto);

            return $customer;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
