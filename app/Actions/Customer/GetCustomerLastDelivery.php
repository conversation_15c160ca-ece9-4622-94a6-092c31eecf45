<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use App\Models\Delivery;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomerLastDelivery
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return \App\Models\Delivery|null
     */
    public function handle(Customer $customer): ?Delivery
    {
        return Delivery::query()
            ->with('deliveryItems.product:id,name')
            ->where('entity_id', $customer->id)
            ->where('entity_type', Customer::class)
            ->orderByDesc('delivered_at')
            ->first();
    }
}
