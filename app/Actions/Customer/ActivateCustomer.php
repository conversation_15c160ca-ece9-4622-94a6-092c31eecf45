<?php

namespace App\Actions\Customer;

use App\Actions\Customer\Integrations\Omie\CreateCustomerInOmie;
use App\Actions\Customer\Integrations\Omie\UpdateCustomerInOmie;
use App\Models\Customer;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ActivateCustomer
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return \App\Models\Customer
     */
    public function handle(Customer $customer): Customer
    {
        try {
            $customer->update(['active' => true]);

            if (is_null($customer->omie_id)) {
                CreateCustomerInOmie::dispatch($customer);
            } else {
                UpdateCustomerInOmie::dispatch($customer);
            }

            return $customer;
        } catch (Throwable $th) {
            throw $th;
        }
    }
}
