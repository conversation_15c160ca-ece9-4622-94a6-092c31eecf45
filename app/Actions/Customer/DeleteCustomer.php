<?php

namespace App\Actions\Customer;

use App\Http\Integrations\Omie\DataTransferObjects\OmieDeleteCustomerDto;
use App\Http\Integrations\Omie\Services\OmieCustomerService;
use App\Models\Customer;
use App\Models\StockLocation;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteCustomer
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return void
     */
    public function handle(Customer $customer): void
    {
        $customer->load(['collections', 'deliveries', 'customerProducts', 'lendingStockAdjustments', 'billings']);

        if ($customer->collections->count() > 0) {
            throw new Exception(__('customers.responses.delete.errors.existing_collections'));
        }

        if ($customer->deliveries->count() > 0) {
            throw new Exception(__('customers.responses.delete.errors.existing_deliveries'));
        }

        if ($customer->customerProducts->count() > 0) {
            throw new Exception(__('customers.responses.delete.errors.existing_customer_products'));
        }

        if ($customer->lendingStockAdjustments->count() > 0) {
            throw new Exception(__('customers.responses.delete.errors.existing_lending_stock_adjustments'));
        }

        if ($customer->billings->count() > 0) {
            throw new Exception(__('customers.responses.delete.errors.existing_billings'));
        }

        try {
            DB::transaction(function () use ($customer) {
                $customer->integrationLogs()->delete();

                $customer->stockLocations->each(function (StockLocation $stockLocation) {
                    $stockLocation->stockLocationProducts()->delete();
                    $stockLocation->delete();
                });

                OmieCustomerService::make()->delete($customer, new OmieDeleteCustomerDto($customer->omie_id, $customer->id));

                $customer->update(['omie_id' => null]);
                $customer->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
