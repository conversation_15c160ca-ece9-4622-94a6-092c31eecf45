<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use App\Models\CustomerProduct;
use App\Models\CustomerProductsReadjustmentItem;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class UndoLastReadjustment
{
    use AsAction;

    protected Customer $customer;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return \App\Models\Customer
     */
    public function handle(Customer $customer): Customer
    {
        try {
            $this->customer = $customer;

            return DB::transaction(function (): Customer {
                /** @var \App\Models\CustomerProductsReadjustment $lastReadjustment */
                $lastReadjustment = $this->customer->customerProductsReadjustments()
                    ->latest()
                    ->first();

                $lastReadjustment->customerProductsReadjustmentItems
                    ->each(function (CustomerProductsReadjustmentItem $customerProductsReadjustmentItem): void {
                        $this->customer->customerProducts
                            ->filter(fn(CustomerProduct $customerProduct): bool => (int) $customerProduct->id === (int) $customerProductsReadjustmentItem->customer_product_id)
                            ->first()
                            ->update([
                                'unit_amount' => $customerProductsReadjustmentItem->old_amount,
                                'total_amount' => $customerProductsReadjustmentItem->old_amount,
                            ]);

                        $customerProductsReadjustmentItem->delete();
                    });

                $lastReadjustment->delete();

                return $this->customer;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
