<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use App\Models\Index;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ReadjustCustomerProducts
{
    use AsAction;

    protected array $errorCustomerIds = [];

    public function handle(
        array $customerIds,
        ?int $indexId = null,
        int $readjustmentMonthCount = 12,
        bool $readjustAmounts = true,
        ?int $userId = null,
        ?Carbon $referenceDate = null
    ): void {
        $referenceDate ??= now();

        collect($customerIds)
            ->each(function (int $customerId) use ($indexId, $readjustmentMonthCount, $readjustAmounts, $referenceDate): void {
                /** @var \App\Models\Customer $customer */
                $customer = Customer::find($customerId);

                /** @var \App\Models\CustomerProductsReadjustment $lastReadjustment */
                $lastReadjustment = $customer->customerProductsReadjustments()
                    ->latest()
                    ->first();

                if (carbon($lastReadjustment->new_ended_at)->gt($referenceDate)) {
                    return;
                }

                try {
                    ReadjustCustomerProduct::run(
                        $customer,
                        is_null($indexId) ? $customer->index : Index::find($indexId),
                        $readjustmentMonthCount,
                        $readjustAmounts
                    );
                } catch (Throwable $th) {
                    $this->errorCustomerIds[] = $customerId;
                    error($th);
                }
            });

        if ($userId) {
            $message = "Os preços foram reajustados.";

            if (count($this->errorCustomerIds) > 0) {
                $message .= ' Os seguintes IDs de cliente apresentaram erro: ' . implode(', ', $this->errorCustomerIds);
            }

            success_database_notification($userId, $message, true);
        }
    }
}
