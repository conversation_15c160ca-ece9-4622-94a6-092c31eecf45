<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use App\Models\CustomerProduct;
use App\Models\CustomerProductsReadjustment;
use App\Models\Index;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ReadjustCustomerProduct
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @param  \App\Models\Index $index
     * @param  int $readjustmentMonthCount
     * @param  bool $readjustAmounts
     * @param  \Carbon\Carbon|null $newEndedAt
     * @return \App\Models\CustomerProductsReadjustment
     */
    public function handle(Customer $customer, ?Index $index, int $readjustmentMonthCount, bool $readjustAmounts, ?Carbon $newEndedAt = null): CustomerProductsReadjustment
    {
        return DB::transaction(function () use ($customer, $index, $readjustmentMonthCount, $readjustAmounts, $newEndedAt): CustomerProductsReadjustment {
            /** @var \App\Models\CustomerProductsReadjustment $lastCustomerProductsReadjustment */
            $lastCustomerProductsReadjustment = $customer->customerProductsReadjustments()
                ->latest()
                ->first();

            /** @var \App\Models\CustomerProductsReadjustment $customerProductsReadjustment */
            $customerProductsReadjustment = $customer->customerProductsReadjustments()->create([
                'operator_id' => auth()->user()?->operator->id,
                'index_id' => $index?->id,
                'batch_no' => $lastCustomerProductsReadjustment?->batch_no
                    ? ($lastCustomerProductsReadjustment?->batch_no + 1)
                    : 1,
                'referring_month' => carbon($customer->service_started_at)->month,
                'referring_year' => now()->year,
                'old_amount' => $lastCustomerProductsReadjustment->new_amount,
                'new_amount' => $lastCustomerProductsReadjustment->new_amount,
                'old_ended_at' => $lastCustomerProductsReadjustment->new_ended_at,
                'new_ended_at' => $newEndedAt
                    ? $newEndedAt->format('Y-m-d')
                    : carbon($lastCustomerProductsReadjustment->new_ended_at)->addMonths($readjustmentMonthCount)->format('Y-m-d'),
            ]);

            if ($readjustAmounts) {
                $customer->customerProducts->each(function (CustomerProduct $customerProduct) use (&$customerProductsReadjustment, $index): void {
                    /** @var \App\Models\CustomerProductsReadjustmentItem $customerProductsReadjustmentItem */
                    $customerProductsReadjustmentItem = $customerProductsReadjustment->customerProductsReadjustmentItems()->create([
                        'customer_product_id' => $customerProduct->id,
                        'index_id' => $index->id,
                        'old_amount' => $customerProduct->unit_amount,
                        'new_amount' => $customerProduct->unit_amount * (1 + ($index->amount / 100)),
                    ]);

                    $customerProduct->update([
                        'unit_amount' => $customerProductsReadjustmentItem->new_amount,
                        'total_amount' => $customerProduct->quantity * $customerProductsReadjustmentItem->new_amount,
                    ]);
                });
            } else {
                $customer->customerProducts->each(function (CustomerProduct $customerProduct) use (&$customerProductsReadjustment, $index): void {
                    $customerProductsReadjustment->customerProductsReadjustmentItems()->create([
                        'customer_product_id' => $customerProduct->id,
                        'index_id' => $index->id,
                        'old_amount' => $customerProduct->unit_amount,
                        'new_amount' => $customerProduct->unit_amount,
                    ]);
                });
            }

            return $customerProductsReadjustment;
        });
    }
}
