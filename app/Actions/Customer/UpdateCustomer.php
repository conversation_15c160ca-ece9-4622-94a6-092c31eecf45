<?php

namespace App\Actions\Customer;

use App\Actions\Customer\Integrations\Omie\CreateCustomerInOmie;
use App\Actions\Customer\Integrations\Omie\UpdateCustomerInOmie;
use App\Models\Customer;
use App\Models\CustomerContact;
use App\Models\CustomerContactType;
use App\Models\CustomerCoupon;
use App\Models\CustomerProduct;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class UpdateCustomer
{
    use AsAction;

    private Customer $customer;

    public function handle(Customer $customer, array $data): Customer
    {
        $this->customer = $customer;

        $this->customer = DB::transaction(function () use ($data): Customer {
            $tags = $data['tags'];
            $itemsCollection = collect($data['items']);

            $customerCoupons = isset($data['customer_coupons'])
                ? $data['customer_coupons']
                : [];

            $customerContacts = isset($data['customer_contacts'])
                ? $data['customer_contacts']
                : [];

            unset($data['tags']);
            unset($data['customer_coupons']);
            unset($data['customer_contacts']);
            unset($data['items']);

            if (isset($data['replicate_main_address_to_delivery_address']) && (bool) $data['replicate_main_address_to_delivery_address']) {
                $data['delivery_address_zipcode'] = $data['address_zipcode'];
                $data['delivery_address_address'] = $data['address_address'];
                $data['delivery_address_number'] = $data['address_number'];
                $data['delivery_address_additional_info'] = $data['address_additional_info'];
                $data['delivery_address_district'] = $data['address_district'];
                $data['delivery_address_city_id'] = $data['address_city_id'];
                $data['delivery_address_state_id'] = $data['address_state_id'];
            }

            $this->customer->update($data);
            $this->customer->refresh();

            $itemsCollection->each(function (array $item): void {
                /** @var \App\Models\CustomerProduct $customerProduct */
                $customerProduct = CustomerProduct::query()
                    ->where('customer_id', $this->customer->id)
                    ->where('product_id', $item['product_id'])
                    ->first();

                if ($customerProduct) {
                    $customerProduct->update([
                        'quantity' => (float) $item['quantity'],
                        'unit_amount' => unmask_money($item['unit_amount']),
                        'total_amount' => (float) $item['quantity'] * unmask_money($item['unit_amount']),
                        'visible_in_collections' => $item['visible_in_collections'],
                        'print_product_id' => $item['print_product_id'],
                    ]);
                } else {
                    /** @var \App\Models\CustomerProduct $customerProduct */
                    $customerProduct = $this->customer->customerProducts()->create([
                        'product_id' => $item['product_id'],
                        'quantity' => (float) $item['quantity'],
                        'unit_amount' => unmask_money($item['unit_amount']),
                        'total_amount' => (float) $item['quantity'] * unmask_money($item['unit_amount']),
                        'visible_in_collections' => $item['visible_in_collections'],
                        'print_product_id' => $item['print_product_id'],
                    ]);
                }
            });

            $this->customer->customerProducts
                ->where('customer_id', $this->customer->id)
                ->whereNotIn('product_id', $itemsCollection->pluck('product_id'))
                ->each(fn(CustomerProduct $customerProduct) => $customerProduct->delete());

            $this->customer->customerCustomerTags()->delete();

            collect($tags)->each(function (string $tagId): void {
                $this->customer->customerCustomerTags()->create(['customer_tag_id' => $tagId]);
            });

            $this->handleCoupons($customerCoupons);
            $this->handleContacts($customerContacts);

            return $this->customer;
        });

        if (is_null($this->customer->omie_id)) {
            CreateCustomerInOmie::dispatch($this->customer);
        } else {
            UpdateCustomerInOmie::dispatch($this->customer);
        }

        return $this->customer;
    }

    protected function handleCoupons(array $customerCoupons): void
    {
        $this->customer->customerCoupons->each(function (CustomerCoupon $customerCoupon): void {
            try {
                $customerCoupon->delete();
            } catch (Throwable $th) {
                error($th);
            }
        });

        collect($customerCoupons)->each(function (array $coupon): void {
            $this->customer->customerCoupons()->create($coupon);
        });
    }

    protected function handleContacts(array $customerContacts): void
    {
        $contactsCollection = collect($customerContacts);

        // Get existing contact IDs that are being updated
        $existingContactIds = $contactsCollection
            ->filter(fn(array $contact) => isset($contact['id']) && !empty($contact['id']))
            ->pluck('id')
            ->toArray();

        // Delete contacts that are no longer in the form data
        $this->customer->customerContacts()
            ->whereNotIn('id', $existingContactIds)
            ->delete();

        // Process each contact from the form
        $contactsCollection->each(function (array $contact): void {
            // Extract contact types and ID
            $contactId = $contact['id'] ?? null;
            $contactTypes = $contact['types'] ?? [];

            // Remove ID and types from contact data for create/update operations
            unset($contact['id'], $contact['types']);

            if ($contactId && !empty($contactId)) {
                // Update existing contact
                /** @var \App\Models\CustomerContact $customerContact */
                $customerContact = CustomerContact::query()
                    ->where('id', $contactId)
                    ->where('customer_id', $this->customer->id)
                    ->first();

                if ($customerContact) {
                    // Update contact basic info
                    $customerContact->update($contact);

                    // Sync contact types
                    $this->syncContactTypes($customerContact, $contactTypes);
                }
            } else {
                // Create new contact
                /** @var \App\Models\CustomerContact $customerContact */
                $customerContact = $this->customer->customerContacts()->create($contact);

                // Create contact types
                $this->syncContactTypes($customerContact, $contactTypes);
            }
        });
    }

    protected function syncContactTypes(CustomerContact $customerContact, array $types): void
    {
        // Delete existing contact types
        $customerContact->customerContactTypes()->delete();

        // Create new contact types
        foreach ($types as $type) {
            $customerContact->customerContactTypes()->create(['type' => $type]);
        }
    }
}
