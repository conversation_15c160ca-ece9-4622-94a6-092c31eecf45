<?php

namespace App\Actions\Customer;

use App\Actions\Customer\Integrations\Omie\CreateCustomerInOmie;
use App\Enums\StockNatureLocationTypeEnum;
use App\Models\Customer;
use App\Models\StockLocation;
use App\Models\StockNature;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateCustomer
{
    use AsAction;

    public function handle(array $data): Customer
    {
        /** @var \App\Models\Customer $customer */
        $customer = DB::transaction(function () use ($data) {
            $tags = $data['tags'];
            $items = $data['items'];

            $customerCoupons = isset($data['customer_coupons'])
                ? $data['customer_coupons']
                : [];

            $customerContacts = isset($data['customer_contacts'])
                ? $data['customer_contacts']
                : [];

            unset($data['tags']);
            unset($data['customer_coupons']);
            unset($data['customer_contacts']);
            unset($data['items']);

            /** @var \App\Models\Customer $customer */
            $customer = Customer::create($data);

            StockLocation::create([
                'stock_nature_id' => StockNature::query()
                    ->where('location_type', StockNatureLocationTypeEnum::InThirdParty->value)
                    ->first()
                    ->id,
                'customer_id' => $customer->id,
                'name' => $customer->name
            ]);

            // Here, we'll iterate over the items array and create each contract item.
            collect($items)->each(function (array $item) use (&$customer) {
                $customer->customerProducts()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => (float) $item['quantity'],
                    'unit_amount' => (float) unmask_money($item['unit_amount']),
                    'total_amount' => (float) $item['quantity'] * (float) unmask_money($item['unit_amount']),
                    'visible_in_collections' => (bool) $item['visible_in_collections'],
                    'print_product_id' => $item['print_product_id'],
                ]);
            });

            collect($tags)->each(function (string $tagId) use (&$customer): void {
                $customer->customerCustomerTags()->create(['customer_tag_id' => $tagId]);
            });

            collect($customerCoupons)->each(function (array $coupon) use (&$customer): void {
                $customer->customerCoupons()->create($coupon);
            });

            collect($customerContacts)->each(function (array $contact) use (&$customer): void {
                // Extract contact types
                $contactTypes = $contact['types'] ?? [];
                unset($contact['types']);

                // Create the contact
                /** @var \App\Models\CustomerContact $customerContact */
                $customerContact = $customer->customerContacts()->create($contact);

                // Create contact types
                foreach ($contactTypes as $type) {
                    $customerContact->customerContactTypes()->create(['type' => $type]);
                }
            });

            $customer->customerProductsReadjustments()->create([
                'operator_id' => auth()->user()->operator->id,
                'index_id' => $customer->index_id,
                'batch_no' => 1,
                'referring_month' => now()->month,
                'referring_year' => now()->year,
                'old_amount' => 0,
                'new_amount' => 0,
                'old_ended_at' => $customer->service_started_at,
                'new_ended_at' => carbon($customer->service_started_at)->addMonths(12),
            ]);

            return $customer;
        });

        if (is_null($customer->omie_id)) {
            CreateCustomerInOmie::dispatch($customer);
        }

        return $customer;
    }
}
