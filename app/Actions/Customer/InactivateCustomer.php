<?php

namespace App\Actions\Customer;

use App\Actions\Customer\Integrations\Omie\CreateCustomerInOmie;
use App\Actions\Customer\Integrations\Omie\UpdateCustomerInOmie;
use App\Models\Collection;
use App\Models\Customer;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class InactivateCustomer
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @return \App\Models\Customer
     */
    public function handle(Customer $customer): Customer
    {
        $collectionsNotBilledCount = Collection::query()
            ->where('customer_id', $customer->id)
            ->whereDoesntHave('incomeCollections')
            ->get('id')
            ->count();

        if ($collectionsNotBilledCount > 0) {
            throw new Exception('Não foi possível inativar o cliente pois ainda existem coletas a serem faturadas.');
        }

        try {
            $customer->update(['active' => false]);

            if (is_null($customer->omie_id)) {
                CreateCustomerInOmie::dispatch($customer);
            } else {
                UpdateCustomerInOmie::dispatch($customer);
            }

            return $customer;
        } catch (Throwable $th) {
            throw $th;
        }
    }
}
