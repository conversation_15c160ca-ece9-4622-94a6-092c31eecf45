<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Throwable;

class ImportCustomer
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Customer|null
     */
    public function handle(array $data): ?Customer
    {
        if (!isset($data['name']) || !$data['name'] || $data['name'] === '') {
            error("{$data['name']} - Razão social vazia/nula.");
            return null;
        }

        if (!isset($data['trading_name']) || !$data['trading_name'] || $data['trading_name'] === '') {
            error("{$data['name']} - Nome fantasia vazio/nulo.");
            return null;
        }

        if (!isset($data['tax_id_number']) || !$data['tax_id_number'] || $data['tax_id_number'] === '') {
            error("{$data['name']} - CNPJ vazio/nulo.");
            return null;
        }

        try {
            /** @var \App\Models\Customer $customer */
            $customer = Customer::query()
                ->where('name', normalize_text_upper($data['name']))
                ->where('trading_name', normalize_text_upper($data['trading_name']))
                ->where('tax_id_number', get_numbers($data['tax_id_number']))
                ->first();

            return !$customer
                ? CreateCustomer::run($data)
                : UpdateCustomer::run($customer, $data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
