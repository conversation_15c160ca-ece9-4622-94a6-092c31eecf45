<?php

namespace App\Actions\CustomerProduct\Queries;

use App\Models\CustomerProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomerProductByCustomerIdAndProductId
{
    use AsAction;

    public function handle(int $customerId, int $productId): ?CustomerProduct
    {
        return CustomerProduct::query()
            ->where('customer_id', $customerId)
            ->where('product_id', $productId)
            ->first();
    }
}
