<?php

namespace App\Actions\AvailableBank\BrasilApi;

use App\Http\Integrations\BrasilApi\Services\BrasilApiBankService;
use App\Models\AvailableBank;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAvailableBanksFromBrasilApi
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return void
     */
    public function handle(): void
    {
        $apiBanks = (new BrasilApiBankService())->get();

        foreach ($apiBanks as $apiBank) {
            if (is_null($apiBank->code)) {
                continue;
            }

            /** @var \App\Models\AvailableBank $availableBank */
            $availableBank = AvailableBank::query()
                ->where('code', $apiBank->code)
                ->first();

            if (!is_null($availableBank)) {
                $availableBank->update([
                    'name' => mb_strtoupper(Str::remove(['\'', '"'], Str::ascii($apiBank->fullName)))
                ]);

                continue;
            }

            AvailableBank::create([
                'code' => $apiBank->code,
                'name' => mb_strtoupper(Str::remove(['\'', '"'], Str::ascii($apiBank->fullName)))
            ]);
        }
    }
}
