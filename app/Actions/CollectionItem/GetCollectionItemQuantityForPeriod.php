<?php

namespace App\Actions\CollectionItem;

use App\Models\CollectionItem;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCollectionItemQuantityForPeriod
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\CollectionItem $collectionItem
     * @param  string $dateFrom
     * @param  mixed $dateTo
     * @return float
     */
    public function handle(CollectionItem $collectionItem, string $dateFrom, mixed $dateTo = null): float
    {
        if (!$dateTo) {
            $dateTo = now();
        }

        return CollectionItem::query()
            ->where('product_id', $collectionItem->product_id)
            ->whereHas('collection', function (Builder $query) use ($collectionItem, $dateFrom, $dateTo) {
                return $query
                    ->where('customer_id', $collectionItem->collection->customer_id)
                    ->where('collected_at', '>', carbon($dateFrom)->format('Y-m-d'))
                    ->where('collected_at', '<=', now()->format('Y-m-d'));
            })
            ->get()
            ->sum(function (CollectionItem $collectionItem) {
                return $collectionItem->quantity;
            });
    }
}
