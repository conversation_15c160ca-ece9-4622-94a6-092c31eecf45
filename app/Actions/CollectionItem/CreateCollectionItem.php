<?php

namespace App\Actions\CollectionItem;

use App\Actions\StockLocationProduct\DecreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\CustomerProduct;
use App\Models\StockLocation;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateCollectionItem
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @param  int $productId
     * @param  float $productQuantity
     * @param  \App\Models\StockLocation $customerStockLocation
     * @return \App\Models\CollectionItem
     */
    public function handle(
        Collection &$collection,
        int $productId,
        float $productQuantity,
        StockLocation $customerStockLocation
    ): CollectionItem {
        /** @var \App\Models\CollectionItem $collectionItem */
        $collectionItem = $collection->collectionItems()->create([
            'product_id' => $productId,
            'quantity' => $productQuantity
        ]);

        /** @var \App\Models\CustomerProduct $existingCustomerProduct */
        $existingCustomerProduct = CustomerProduct::query()
            ->where('customer_id', $collection->customer_id)
            ->where('product_id', $productId)
            ->first();

        if (!$existingCustomerProduct) {
            $existingCustomerProduct = $collection->customer->customerProducts()->create([
                'product_id' => $productId,
                'quantity' => (float) $productQuantity,
                'unit_amount' => $collectionItem->product->default_price,
                'total_amount' => $collectionItem->product->default_price,
                'visible_in_collections' => false,
            ]);
        }

        DecreaseStockLocationProductQuantity::run(
            $customerStockLocation,
            $productId,
            $productQuantity
        );

        HandleStockMovementSummary::run($collectionItem);

        return $collectionItem;
    }
}
