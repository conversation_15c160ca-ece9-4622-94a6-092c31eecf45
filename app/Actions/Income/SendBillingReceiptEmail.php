<?php

namespace App\Actions\Income;

use App\Actions\Core\SendEmail;
use App\Enums\BankSlipStatusEnum;
use App\Models\Receivable;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class SendBillingReceiptEmail
{
    use AsAction;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.default_names.p4m.emails.data_send'));
    }

    public function handle(
        Receivable $receivable,
        bool $automatic = false,
        bool $throwExceptionOnError = false,
        ?int $userId = null,
        bool $deconsiderSentEmail = false,
        ?string $overrideToEmails = null,
    ): void {
        if ($deconsiderSentEmail && $receivable->email_sent) {
            return;
        }

        $toEmails = $overrideToEmails
            ? explode(',', $overrideToEmails)
            : explode(',', $receivable->document->customer->billing_email);

        if (is_null($toEmails)) {
            throw_if(!$automatic, 'Não existem endereços de e-mail cadastrados para envio do e-mail de faturamento.');
            return;
        }

        $subject = 'Faturamento ' . p4m_tenant()->getCompanyName() . " - {$receivable->document->customer->trading_name}";

        $token = base64_encode($receivable->document_id . '-' . Str::random(32));

        $invoiceHref = (app()->isLocal() ? 'http://' : 'https://') . tenant('id') . '.' . env('TENANCY_CENTRAL_DOMAIN') . "/receivables/$token/billing";

        /** @var \App\Models\BankSlip $bankSlip */
        $bankSlip = $receivable->bankSlips()
            ->where('status', BankSlipStatusEnum::Open->value)
            ->first();

        $bankSlipHref = '';

        if ($bankSlip) {
            $bankSlipHref = (app()->isLocal() ? 'http://' : 'https://') . tenant('id') . '.' . env('TENANCY_CENTRAL_DOMAIN') . "/receivables/$token/bank-slip";
        }

        $message = view('emails.receivable-email', [
            'document' => $receivable->document,
            'invoiceHref' => $invoiceHref,
            'hasBankSlip' => !is_null($bankSlip),
            'bankSlipHref' => $bankSlipHref,
            'companyName' => p4m_tenant()->getCompanyName()
        ])->render();

        try {
            $mailgunMessageId = SendEmail::run($toEmails, $subject, $message);
        } catch (Throwable $th) {
            error($th);
            database_notification($userId, "Não foi possível enviar o e-mail da fatura #{$receivable->document_id}. Verifique se o e-mail cadastrado está correto e tente novamente.");

            if ($throwExceptionOnError) {
                throw $th;
            }

            return;
        }

        $receivable->update(['email_sent' => true]);

        $receivable->receivableEmails()->create([
            'to_emails' => $toEmails,
            'mailgun_message_id' => $mailgunMessageId,
            'message' => $message,
            'email_sent_at' => now(),
        ]);
    }
}
