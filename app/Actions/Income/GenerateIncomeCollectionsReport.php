<?php

namespace App\Actions\Income;

use App\Models\CollectionItem;
use App\Models\Income;
use App\Models\IncomeCollection;
use App\Models\IncomeItem;
use App\Models\Product;
use Barryvdh\DomPDF\Facade\Pdf;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateIncomeCollectionsReport
{
    use AsAction;

    /**
     * Handle the action as a controller.
     *
     * @param  string $token
     * @return mixed
     */
    public function asController(string $token): mixed
    {
        $decodedToken = base64_decode($token);

        return $this->handle(
            Income::find(explode('-', $decodedToken)[0])
        );
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Income $income
     * @return mixed
     */
    public function handle(Income $income): mixed
    {
        $resumeItems = [];

        $income->incomeCollections->each(function (IncomeCollection $incomeCollection) use (&$resumeItems): void {
            $incomeCollection->collection->collectionItems->each(function (CollectionItem $collectionItem) use (&$resumeItems, $incomeCollection): void {
                if (isset($resumeItems[$collectionItem->product_id])) {
                    $resumeItems[$collectionItem->product_id]['quantity'] += (float) $collectionItem->quantity;
                    return;
                }

                $resumeItems[$collectionItem->product_id] = [
                    'product_name' => $collectionItem->product->name ?? Product::query()->withTrashed()->find($collectionItem->product_id)->name,
                    'unit_amount' => (float) IncomeItem::query()
                        ->where('product_id', $collectionItem->product_id)
                        ->where('income_id', $incomeCollection->income_id)
                        ->first()
                        ->unit_amount,
                    'quantity' => (float) $collectionItem->quantity
                ];
            });
        });

        return Pdf::loadView('reports.incomes.income-receipt-report', [
            'document' => $income->load('incomeCollections.collection.collectionItems'),
            'resumeItems' => collect($resumeItems),
            'minimumBillingAmount' => $income->customer->minimum_billing_amount,
            'logoSrc' => p4m_tenant()->getLogoSrc()
        ])->stream();
    }
}
