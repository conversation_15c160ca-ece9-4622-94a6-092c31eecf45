<?php

namespace App\Actions\Income;

use App\Actions\CustomerProduct\Queries\GetCustomerProductByCustomerIdAndProductId;
use App\Actions\Income\Queries\GetIncomeByCustomerIdAndIssuedAt;
use App\Actions\Income\Queries\GetLatestIncomeDocumentNumber;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\Customer;
use App\Models\CustomerCoupon;
use App\Models\Income;
use App\Models\IncomeItem;
use App\Models\Product;
use App\Models\Receivable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateIncomeForCustomer
{
    use AsAction;

    private array $customerCache = [];
    private array $productCache = [];
    private int $userId;

    private Income $income;

    public function handle(Customer $customer, Carbon $dateTo): ?Income
    {
        /** @var \App\Models\Income $existingIncomeForPeriod */
        $existingIncomeForPeriod = GetIncomeByCustomerIdAndIssuedAt::run($customer->id, $dateTo);

        if (!is_null($existingIncomeForPeriod)) {
            return null;
        }

        return DB::transaction(function () use ($customer, $dateTo): Income {
            $this->createIncome($customer, $dateTo);
            $this->createIncomeCollectionLinks($dateTo);
            $this->createIncomeItems($customer);
            $this->createReceivable();

            return $this->income;
        });
    }

    private function createIncome(Customer $customer, Carbon $dateTo): void
    {
        $this->income = Income::create([
            'customer_id' => $customer->id,
            'document_number' => str_pad(GetLatestIncomeDocumentNumber::run() + 1, 10, '0', STR_PAD_LEFT),
            'amount' => 0,
            'additional_info' => 'Faturamento do cliente ' . $customer->id,
            'issued_at' => $dateTo->format('Y-m-d'),
        ]);
    }

    private function createIncomeCollectionLinks(Carbon $dateTo): void
    {
        Collection::query()
            ->where('customer_id', $this->income->customer_id)
            ->where('collected_at', '<=', $dateTo->format('Y-m-d'))
            ->whereNull('deleted_at')
            ->whereDoesntHave('incomeCollections')
            ->get()
            ->each(function (Collection $collection): void {
                $this->income->incomeCollections()->firstOrCreate(['collection_id' => $collection->id]);
            });
    }

    private function createIncomeItems(Customer $customer): void
    {
        CollectionItem::query()
            ->whereIn('collection_id', $this->income->incomeCollections->pluck('collection_id')->toArray())
            ->whereNull('deleted_at')
            ->get()
            ->groupBy('product_id')
            ->each(function (\Illuminate\Support\Collection $productGroup, $key): void {
                /** @var \App\Models\CustomerProduct $customerProduct */
                $customerProduct = GetCustomerProductByCustomerIdAndProductId::run($this->income->customer_id, $key);

                $quantity = $productGroup->sum(fn(CollectionItem $collectionItem) => $collectionItem->quantity);

                $unitAmount = round((float) $customerProduct->unit_amount, 2);

                $this->income->incomeItems()->create([
                    'product_id' => $key,
                    'quantity' => $quantity,
                    'unit_amount' => $unitAmount,
                    'total_amount' => $quantity * $unitAmount,
                ]);
            });

        $minimumBillingAmount = (float) $customer->minimum_billing_amount;

        $incomeAmount = $this->income->incomeItems->sum(function (IncomeItem $incomeItem): float {
            return $incomeItem->total_amount;
        });

        $willUseMinimumAmount = $incomeAmount < $minimumBillingAmount;

        if (!$willUseMinimumAmount) {
            $incomeAmount -= $this->handleCoupons($customer, $incomeAmount, false);
            $this->income->update(['amount' => $incomeAmount]);

            return;
        }

        $this->income->incomeItems()->create([
            'product_id' => Product::query()
                ->where('erp_flex_id', app()->isProduction() ? '5574611' : '5525819')
                ->first()
                ->id,
            'quantity' => 1,
            'unit_amount' => $minimumBillingAmount - $incomeAmount,
            'total_amount' => $minimumBillingAmount - $incomeAmount,
        ]);

        $incomeAmount = $minimumBillingAmount - $this->handleCoupons($customer, $incomeAmount, true);

        $this->income->update(['amount' => $incomeAmount]);
    }

    private function handleCoupons(Customer $customer, float $incomeAmount, bool $isMinimumBilling = false): float
    {
        $incomeDate = carbon($this->income->issued_at)->format('Y-m-d');

        $customerCoupons = CustomerCoupon::query()
            ->where('customer_id', $customer->id)
            ->where('starting_at', '<=', $incomeDate)
            ->where('expires_at', '>=', $incomeDate)
            ->when($isMinimumBilling, function ($query) {
                return $query->where('apply_to_minimum_amount', true);
            })
            ->when(!$isMinimumBilling, function ($query) {
                return $query->where('apply_to_regular_amount', true);
            })
            ->get();

        $totalDiscount = 0;

        $customerCoupons->each(function (CustomerCoupon $customerCoupon) use (&$totalDiscount, $incomeAmount): void {
            $discountAmount = $customerCoupon->type === \App\Enums\CouponTypeEnum::PERCENTAGE
                ? $incomeAmount * ($customerCoupon->amount / 100)
                : $customerCoupon->amount;

            $totalDiscount += $discountAmount;

            $this->income->incomeCustomerCoupons()->create(['customer_coupon_id' => $customerCoupon->id]);
        });

        return round($totalDiscount, 2);
    }

    private function createReceivable(): void
    {
        $this->income->receivables()->create([
            'customer_id' => $this->income->customer_id,
            'document_id' => $this->income->id,
            'document_type' => Income::class,
            'sequence' => 1,
            'original_amount' => $this->income->amount,
            'updated_amount' => $this->income->amount,
            'issued_at' => $this->income->issued_at,
            'expires_at' => carbon($this->income->issued_at)->addDays($this->income->customer->default_due_day),
            'settled_at' => null
        ]);

        $this->income->receivables->each(function (Receivable $receivable): void {
            $receivable->update(['collection_count' => $this->income->incomeCollections->count()]);
        });
    }
}
