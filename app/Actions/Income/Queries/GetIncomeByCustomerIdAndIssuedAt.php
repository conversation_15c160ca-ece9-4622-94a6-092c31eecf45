<?php

namespace App\Actions\Income\Queries;

use App\Models\Income;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetIncomeByCustomerIdAndIssuedAt
{
    use AsAction;

    public function handle(int $customerId, Carbon $dateTo): ?Income
    {
        return Income::query()
            ->where('customer_id', $customerId)
            ->where('issued_at', $dateTo->format('Y-m-d'))
            ->first();
    }
}
