<?php

namespace App\Actions\Income;

use App\Actions\Customer\ReadjustCustomerProducts;
use App\Models\Customer;
use App\Models\Index;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateIncomesForCustomers
{
    use AsAction;

    private array $errors = [];
    private array $successReadjustments = [];

    public function handle(array $data, int $userId): void
    {
        foreach ($data['indices'] as $index) {
            Index::find($index['id'])->update(['amount' => $index['amount']]);
        }

        Customer::query()
            ->where('active', true)
            ->when(isset($data['customer_id']), function (Builder $query) use ($data): Builder {
                return $query->where('id', $data['customer_id']);
            })
            ->when(!isset($data['customer_id']), function (Builder $query) use ($data): Builder {
                return $query->where('default_billing_period', $data['default_billing_period']);
            })
            ->get()
            ->each(function (Customer $customer) use ($data): void {
                try {
                    ReadjustCustomerProducts::run(
                        [$customer->id],
                        (int) $data['index_id'] === 0 ? $customer->index_id : (int) $data['index_id'],
                        $data['readjustment_month_count'],
                        (bool) $data['readjust_amounts'],
                        null,
                        carbon($data['date_to'])
                    );

                    $customer->refresh();

                    GenerateIncomeForCustomer::run($customer, carbon($data['date_to']));

                    $this->successReadjustments[] = $customer->id;
                } catch (Throwable $th) {
                    $this->errors[] = $customer->id;
                    error($th);
                }
            });

        $message = 'O processamento das faturas foi finalizado. Foram reajustados ' . count($this->successReadjustments) . ' clientes.';

        if (count($this->errors) > 0) {
            $message .= ' Os seguintes IDs de cliente apresentaram erro e não puderam ser reajustados: ' . implode(',', $this->errors);
        }

        success_database_notification($userId, $message);
    }
}
