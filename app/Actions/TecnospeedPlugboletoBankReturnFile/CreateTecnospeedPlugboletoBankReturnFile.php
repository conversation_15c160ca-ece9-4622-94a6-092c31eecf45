<?php

namespace App\Actions\TecnospeedPlugboletoBankReturnFile;

use App\Http\Integrations\Tecnospeed\PlugBoleto\Services\TecnospeedPlugBoletoBankReturnFileService;
use App\Models\BankAccountWallet;
use App\Models\TecnospeedPlugboletoBankReturnFile;
use App\Models\TecnospeedPlugBoletoIntegrationLog;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateTecnospeedPlugboletoBankReturnFile
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\TecnospeedPlugboletoBankReturnFile
     */
    public function handle(array $data): TecnospeedPlugboletoBankReturnFile
    {
        /** @var \App\Models\TecnospeedPlugboletoBankReturnFile $tecnospeedPlugboletoBankReturnFile */
        $tecnospeedPlugboletoBankReturnFile = TecnospeedPlugboletoBankReturnFile::create($data);

        try {
            $response = TecnospeedPlugBoletoBankReturnFileService::make()->send(
                BankAccountWallet::first(),
                $data['name'],
                $data['path']
            );

            TecnospeedPlugBoletoIntegrationLog::create([
                'integratable_id' => $tecnospeedPlugboletoBankReturnFile->id,
                'integratable_type' => get_class($tecnospeedPlugboletoBankReturnFile),
                'response' => $response
            ]);

            if (mb_strtolower($response->_status) === 'sucesso') {
                $tecnospeedPlugboletoBankReturnFile->update(['protocol' => $response->_dados->protocolo]);
            }
        } catch (Throwable $th) {
            $tecnospeedPlugboletoBankReturnFile->delete();

            error_notification($th->getMessage())->send();

            throw_error($th);
        }

        return $tecnospeedPlugboletoBankReturnFile;
    }
}
