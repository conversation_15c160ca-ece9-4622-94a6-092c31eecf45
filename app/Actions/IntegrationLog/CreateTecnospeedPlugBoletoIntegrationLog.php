<?php

namespace App\Actions\IntegrationLog;

use App\Models\IntegrationLog;
use App\Models\TecnospeedPlugboletoIntegration;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateTecnospeedPlugboletoIntegrationLog
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\TecnospeedPlugboletoIntegration $tecnospeedPlugboletoIntegration
     * @param  mixed $entity
     * @param  string $dataFlow
     * @param  array $data
     * @return \App\Models\IntegrationLog
     */
    public function handle(
        TecnospeedPlugboletoIntegration $tecnospeedPlugboletoIntegration,
        mixed $entity,
        string $dataFlow,
        array $data
    ): IntegrationLog {
        return IntegrationLog::create([
            'integration_id' => $tecnospeedPlugboletoIntegration->id,
            'integration_type' => get_class($tecnospeedPlugboletoIntegration),
            'entity_id' => $entity->id,
            'entity_type' => get_class($entity),
            'data_flow' => $dataFlow,
            'data' => $data
        ]);
    }
}
