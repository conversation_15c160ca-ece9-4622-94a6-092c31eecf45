<?php

namespace App\Actions\LendingStockAdjustment;

use App\Actions\CustomerHistory\CreateCustomerHistory;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\LendingStockAdjustment;
use App\Models\StockLocationProduct;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateLendingStockAdjustment
{
    use AsAction;

    protected LendingStockAdjustment $lendingStockAdjustment;

    public function handle(array $data): LendingStockAdjustment
    {
        $products = $data['products'];
        unset($data['products']);

        if (empty($products)) {
            throw new Exception('O ajuste de estoque não possui itens.');
        }

        try {
            $this->lendingStockAdjustment = $this->createLendingStockAdjustment($data, $products);
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->lendingStockAdjustment;
    }

    protected function createLendingStockAdjustment(array $data, array $products): LendingStockAdjustment
    {
        return DB::transaction(function () use ($data, $products) {
            /** @var \App\Models\LendingStockAdjustment $lendingStockAdjustment */
            $this->lendingStockAdjustment = LendingStockAdjustment::create($data);

            if ($this->lendingStockAdjustment->additional_info) {
                $this->createCustomerHistory();
            }

            $this->createItems($products);

            return $this->lendingStockAdjustment;
        });
    }

    protected function createCustomerHistory(): void
    {
        CreateCustomerHistory::run(
            $this->lendingStockAdjustment->customer,
            $this->lendingStockAdjustment->id,
            LendingStockAdjustment::class,
            $this->lendingStockAdjustment->additional_info
        );
    }

    protected function createItems(array $products): void
    {
        collect($products)->each(function (array $product) {
            /** @var \App\Models\StockLocationProduct $stockLocationProduct */
            $stockLocationProduct = StockLocationProduct::query()
                ->where('product_id', $product['product_id'])
                ->whereRelation('stockLocation', 'customer_id', $this->lendingStockAdjustment->customer_id)
                ->first();

            if (!$stockLocationProduct) {
                $stockLocationProduct = StockLocationProduct::create([
                    'stock_location_id' => $this->lendingStockAdjustment->customer->stockLocations->first()->id,
                    'product_id' => $product['product_id'],
                    'initial_quantity' => 0,
                    'current_quantity' => 0,
                ]);
            }

            /** @var \App\Models\LendingStockAdjustmentItem $lendingStockAdjustmentItem */
            $lendingStockAdjustmentItem = $this->lendingStockAdjustment->lendingStockAdjustmentItems()->create([
                'product_id' => $product['product_id'],
                'old_stock_quantity' => $product['old_stock_quantity'],
                'collected_quantity' => $product['collected_quantity'],
                'delivered_quantity' => $product['delivered_quantity'],
                'out_of_movement_quantity' => $product['out_of_movement_quantity'],
                'adjustment_quantity' => $product['adjustment_quantity'],
                'current_stock_quantity_before_update' => $stockLocationProduct->current_quantity,
                'current_stock_quantity' => $product['current_stock_quantity']
            ]);

            $stockLocationProduct->update([
                'current_quantity' => (float) $stockLocationProduct->current_quantity + (float) $product['adjustment_quantity']
            ]);

            HandleStockMovementSummary::run($lendingStockAdjustmentItem);
        });
    }
}
