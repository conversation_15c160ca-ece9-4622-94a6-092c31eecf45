<?php

namespace App\Actions\LendingStockAdjustment;

use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\LendingStockAdjustment;
use App\Models\LendingStockAdjustmentItem;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteLendingStockAdjustment
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\LendingStockAdjustment $lendingStockAdjustment
     * @return void
     */
    public function handle(LendingStockAdjustment $lendingStockAdjustment): void
    {
        try {
            DB::transaction(function () use ($lendingStockAdjustment): void {
                $lendingStockAdjustment->lendingStockAdjustmentItems->each(
                    function (LendingStockAdjustmentItem $lendingStockAdjustmentItem): void {
                        HandleStockMovementSummary::run($lendingStockAdjustmentItem, true);
                        $lendingStockAdjustmentItem->delete();
                    }
                );

                $lendingStockAdjustment->customerHistories()->delete();
                $lendingStockAdjustment->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
