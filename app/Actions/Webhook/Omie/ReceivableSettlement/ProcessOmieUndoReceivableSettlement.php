<?php

namespace App\Actions\Webhook\Omie\ReceivableSettlement;

use App\Enums\ReceivableStatusEnum;
use App\Models\Receivable;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessOmieUndoReceivableSettlement
{
    use AsAction;

    public function handle(mixed $payload): void
    {
        foreach ($payload->event as $data) {
            /** @var \App\Models\Receivable $receivable */
            $receivable = Receivable::query()
                ->where('omie_id', $data->conta_a_receber[0]->codigo_lancamento_omie)
                ->whereNotNull('settled_at')
                ->first();

            if (!$receivable) {
                continue;
            }

            DB::transaction(function () use ($receivable): void {
                $receivable->receivableSettlements()->delete();

                $receivable->update([
                    'status' => ReceivableStatusEnum::Open->value,
                    'settled_at' => null,
                ]);
            });
        }
    }
}
