<?php

namespace App\Actions\Webhook\Omie\ReceivableSettlement;

use App\Models\ReceivedWebhook;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessOmieReceivableSettlementWebhook
{
    use AsAction;

    public function asController(ActionRequest $request): void
    {
        $payload = json_decode($request->getContent());

        ReceivedWebhook::create(['data' => $payload]);

        if (!isset($payload->topic)) {
            return;
        }

        $this->handle($payload);
    }

    public function handle(mixed $payload): void
    {
        switch ($payload->topic) {
            case 'Financas.ContaReceber.BaixaRealizada':
                ProcessOmieReceivableSettlement::run($payload);
                break;
            default:
                break;
        }
    }
}
