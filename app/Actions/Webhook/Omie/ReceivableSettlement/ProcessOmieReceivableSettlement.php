<?php

namespace App\Actions\Webhook\Omie\ReceivableSettlement;

use App\Actions\Receivable\SettleReceivable;
use App\Models\BankAccountWallet;
use App\Models\Receivable;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessOmieReceivableSettlement
{
    use AsAction;

    public function handle(mixed $payload): void
    {
        $dataArray = $payload->event;
        $bankAccountWallet = BankAccountWallet::first();

        foreach ($dataArray as $data) {
            /** @var \App\Models\Receivable $receivable */
            $receivable = Receivable::query()
                ->where('omie_id', $data->conta_a_receber[0]->codigo_lancamento_omie)
                ->whereNull('settled_at')
                ->first();

            if (!$receivable) {
                continue;
            }

            SettleReceivable::run(
                $receivable,
                $data->valor,
                carbon(explode('T', $data->data)[0]),
                $bankAccountWallet,
                null,
                false
            );
        }
    }
}
