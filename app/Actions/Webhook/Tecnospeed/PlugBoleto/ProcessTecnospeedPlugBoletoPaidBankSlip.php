<?php

namespace App\Actions\Webhook\Tecnospeed\PlugBoleto;

use App\Actions\BankSlip\SettleBankSlip;
use App\Models\BankSlip;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ProcessTecnospeedPlugBoletoPaidBankSlip
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  mixed $data
     * @return void
     */
    public function handle(mixed $data): void
    {
        if (mb_strtoupper($data->titulo->situacao) !== 'LIQUIDADO') {
            return;
        }

        try {
            /** @var \App\Models\BankSlip $bankSlip */
            $bankSlip = BankSlip::query()
                ->where('tecnospeed_id', $data->titulo->idintegracao)
                ->firstOrFail();

            try {
                $settledAt = Carbon::createFromFormat('d/m/Y', $data->titulo->PagamentoDataCredito);
            } catch (Throwable) {
                $settledAt = Carbon::createFromFormat('d/m/Y H:i:s', $data->titulo->PagamentoDataCredito);
            }

            SettleBankSlip::run($bankSlip, unmask_money($data->titulo->PagamentoValorPago), $settledAt);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
