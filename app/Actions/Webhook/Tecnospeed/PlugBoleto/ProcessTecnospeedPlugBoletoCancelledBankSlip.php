<?php

namespace App\Actions\Webhook\Tecnospeed\PlugBoleto;

use App\Actions\BankSlip\CancelBankSlip;
use App\Models\BankSlip;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ProcessTecnospeedPlugBoletoCancelledBankSlip
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  mixed $data
     * @return void
     */
    public function handle(mixed $data): void
    {
        if (mb_strtoupper($data->titulo->situacao) !== 'BAIXADO') {
            return;
        }

        try {
            /** @var \App\Models\BankSlip $bankSlip */
            $bankSlip = BankSlip::query()
                ->where('tecnospeed_id', $data->titulo->idintegracao)
                ->firstOrFail();

            $cancelledAt = Carbon::createFromFormat('d/m/Y H:i:s', $data->titulo->TituloMovimentos[0]->data);

            CancelBankSlip::run($bankSlip, true, $data, $cancelledAt);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
