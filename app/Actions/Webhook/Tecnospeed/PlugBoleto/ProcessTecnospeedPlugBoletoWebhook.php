<?php

namespace App\Actions\Webhook\Tecnospeed\PlugBoleto;

use App\Enums\TecnospeedPlugBoletoWebhookTypeEnum;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessTecnospeedPlugBoletoWebhook
{
    use AsAction;

    /**
     * Handle the action as a controller.
     *
     * @param  \Lorisleiva\Actions\ActionRequest $request
     * @return void
     */
    public function asController(ActionRequest $request): void
    {
        if (!$request->hasHeader('tecnospeed-plugboleto-token')) {
            return;
        }

        if ($request->header('tecnospeed-plugboleto-token') !== config('tecnospeed.plugboleto.webhook.token')) {
            return;
        }

        $this->handle(
            json_decode($request->getContent())
        );
    }

    /**
     * Handle the action.
     *
     * @param  mixed $data
     * @return void
     */
    public function handle(mixed $data): void
    {
        switch ($data->tipoWH) {
            case TecnospeedPlugBoletoWebhookTypeEnum::Paid->value:
                ProcessTecnospeedPlugBoletoPaidBankSlip::run($data);
                break;
            case TecnospeedPlugBoletoWebhookTypeEnum::Cancelled->value:
                ProcessTecnospeedPlugBoletoCancelledBankSlip::run($data);
                break;
            default:
                break;
        }
    }
}
