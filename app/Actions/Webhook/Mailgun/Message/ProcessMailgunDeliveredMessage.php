<?php

namespace App\Actions\Webhook\Mailgun\Message;

use App\Models\ReceivableEmail;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class ProcessMailgunDeliveredMessage
{
    use AsAction;

    public function handle(ActionRequest $request)
    {
        $payload = $request->all();

        if ($payload['event-data']['event'] !== 'delivered') {
            return;
        }

        /** @var \App\Models\ReceivableEmail $receivableEmail */
        $receivableEmail = ReceivableEmail::query()
            ->where('mailgun_message_id', '<' . $payload['event-data']['message']['headers']['message-id'] . '>')
            ->first();

        if (!$receivableEmail) {
            return;
        }

        $receivableEmail->receivableEmailInteractions()->create([
            'event' => $payload['event-data']['event'],
            'recipient' => $payload['event-data']['message']['headers']['to'],
            'timestamp' => $payload['event-data']['timestamp'],
        ]);
    }
}
