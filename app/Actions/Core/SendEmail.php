<?php

namespace App\Actions\Core;

use App\Actions\SmtpConfiguration\GetActiveSmtpConfiguration;
use App\Http\Integrations\Mailgun\Services\MailgunMessageService;
use App\Models\SmtpConfiguration;
use Exception;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Throwable;

class SendEmail
{
    use AsAction;

    public function handle(
        array $toAddresses,
        string $subject,
        string $message,
        ?string $attachmentStream = null,
        ?string $attachmentName = null,
        ?SmtpConfiguration $smtpConfiguration = null,
        bool $receiveOwnCopy = true,
    ): mixed {
        /** @var \App\Models\SmtpConfiguration|null $smtpConfiguration */
        $smtpConfiguration ??= GetActiveSmtpConfiguration::run();

        if (!$smtpConfiguration) {
            throw new Exception(__('core.errors.email.send.errors.no_active_configuration'));
        }

        if (config('mailgun.send_through_api')) {
            return MailgunMessageService::make()->create(
                config('mailgun.api.domain'),
                $smtpConfiguration->from_address,
                implode(',', $toAddresses),
                $subject,
                $message,
                $receiveOwnCopy ? '<EMAIL>' : null,
            );
        }

        try {
            $authData = "$smtpConfiguration->username:$smtpConfiguration->password";
            $fullHost = "$smtpConfiguration->host:$smtpConfiguration->port";

            $mailer = new Mailer(Transport::fromDsn("smtp://$authData@$fullHost?verify_peer=false"));

            $email = (new Email())
                ->from(new Address($smtpConfiguration->from_address, $smtpConfiguration->from_name))
                ->subject($subject)
                ->html($message);

            if ($attachmentStream) {
                $email->attach($attachmentStream, $attachmentName);
            }

            foreach ($toAddresses as $toAddress) {
                $email->addTo(trim($toAddress));
            }

            if ($receiveOwnCopy) {
                $email->addCc('<EMAIL>');
            }

            $mailer->send($email);

            return null;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
