<?php

namespace App\Actions\Core;

use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CalculateAddressLatitudeLongitude
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $address
     * @param  string|int $number
     * @param  string $district
     * @param  string $city
     * @param  string $state
     * @return array
     */
    public function handle(string $address, string|int $number, string $district, string $city, string $state): array
    {
        $latitude = null;
        $longitude = null;

        $address = utf8_encode(
            urlencode("{$address}, {$number}, {$district}, {$city} - {$state}")
        );

        $url = config('google_maps.api.url.geocode') . "?address=$address&sensor=false&key=" . config('google_maps.api.key');

        try {
            $geocode = json_decode(file_get_contents($url), true);

            $latitude = strval($geocode['results'][0]['geometry']['location']['lat']);
            $longitude = strval($geocode['results'][0]['geometry']['location']['lng']);
        } catch (Throwable $th) {
            error($th);
        }

        return [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ];
    }
}
