<?php

namespace App\Actions\Collection;

use App\Models\Collection;
use Barryvdh\DomPDF\Facade\Pdf;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateCollectionReceipts
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \Illuminate\Support\Collection $collections
     * @return mixed
     */
    public function handle(string $recordsToken): mixed
    {
        return Pdf::loadView('reports.collections.collection-receipts', [
            'collections' => Collection::query()
                ->whereIn('id', explode(',', base64_decode($recordsToken)))
                ->get(),
            'logoSrc' => p4m_tenant()->getLogoSrc()
        ])->stream();
    }
}
