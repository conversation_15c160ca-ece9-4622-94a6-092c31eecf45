<?php

namespace App\Actions\Collection;

use App\Actions\StockLocationProduct\DecreaseStockLocationProductQuantity;
use App\Actions\StockLocationProduct\IncreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\Contract;
use App\Models\ContractItem;
use App\Models\Product;
use App\Models\StockLocation;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditCollection
{
    use AsAction;

    protected Collection $collection;
    protected StockLocation $customerStockLocation;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @param  array $data
     * @param  int $userId
     * @param  bool $sendEmail
     * @return \App\Models\Collection
     */
    public function handle(Collection $collection, array $data, int $userId, bool $sendEmail = true): Collection
    {
        $products = $data['products'];
        unset($data['products']);

        $products = array_filter($products, function (array $collectionProduct): bool {
            return !is_null($collectionProduct['product_id']) && $collectionProduct['quantity'] > 0;
        });

        if (empty($products)) {
            throw new Exception('A coleta não possui itens.');
        }

        $this->collection = DB::transaction(function () use ($collection, $data, $products) {
            $this->collection = $collection;
            $this->customerStockLocation = $this->collection->customer->stockLocations->first();

            $this->undoItemMovements(collect($products));

            $this->collection->update($data);

            $this->updateItems(collect($products));

            return $this->collection;
        });

        if ($sendEmail && isset($data['send_email']) && (bool) $data['send_email']) {
            $this->sendEmail($userId);
        }

        return $this->collection;
    }

    /**
     * Undo the item movements.
     *
     * @param  \Illuminate\Support\Collection $productsCollection
     * @return void
     */
    protected function undoItemMovements(\Illuminate\Support\Collection $productsCollection): void
    {
        foreach ($productsCollection as $productData) {
            /** @var \App\Models\CollectionItem $existingCollectionItem */
            $existingCollectionItem = CollectionItem::query()
                ->where('collection_id', $this->collection->id)
                ->where('product_id', $productData['product_id'])
                ->first();

            if (!$existingCollectionItem) {
                continue;
            }

            IncreaseStockLocationProductQuantity::run($this->customerStockLocation, $existingCollectionItem->product_id, $existingCollectionItem->quantity);
            HandleStockMovementSummary::run($existingCollectionItem, true);
        };

        $this->deleteUnmatchingItems($productsCollection);
    }

    /**
     * Update the collection items.
     *
     * @param  \Illuminate\Support\Collection $productsCollection
     * @return void
     */
    protected function updateItems(\Illuminate\Support\Collection $productsCollection): void
    {
        foreach ($productsCollection as $productData) {
            /** @var \App\Models\CollectionItem $existingCollectionItem */
            $existingCollectionItem = CollectionItem::query()
                ->where('collection_id', $this->collection->id)
                ->where('product_id', $productData['product_id'])
                ->first();

            if ($existingCollectionItem) {
                $this->updateSingleItem($existingCollectionItem, $productData);
                continue;
            }

            $this->createSingleItem($productData);
        };
    }

    /**
     * Update a single item.
     *
     * @param  \App\Models\CollectionItem $existingCollectionItem
     * @param  array $productData
     * @return void
     */
    protected function updateSingleItem(CollectionItem $existingCollectionItem, array $productData): void
    {
        $existingCollectionItem->update(['quantity' => (float) $productData['quantity']]);

        DecreaseStockLocationProductQuantity::run(
            $this->customerStockLocation,
            $existingCollectionItem->product_id,
            $existingCollectionItem->quantity
        );

        HandleStockMovementSummary::run($existingCollectionItem);
    }

    /**
     * Create a single item.
     *
     * @param  array $productData
     * @return void
     */
    protected function createSingleItem(array $productData): void
    {
        /** @var \App\Models\CollectionItem $collectionItem */
        $collectionItem = $this->collection->collectionItems()->create([
            'product_id' => $productData['product_id'],
            'quantity' => (float) $productData['quantity']
        ]);

        /** @var \App\Models\ContractItem $existingContractItem */
        $existingContractItem = ContractItem::query()
            ->where('contract_id', $this->collection->contract_id)
            ->where('item_id', $collectionItem->product_id)
            ->where('item_type', Product::class)
            ->first();

        if (!$existingContractItem) {
            $existingContractItem = $this->createContractItem($collectionItem);
        }

        DecreaseStockLocationProductQuantity::run(
            $this->customerStockLocation,
            $productData['product_id'],
            (float) $productData['quantity']
        );

        HandleStockMovementSummary::run($collectionItem);
    }

    /**
     * Create a contract item for a detached customer product.
     *
     * @param  \App\Models\CollectionItem $collectionItem
     * @return \App\Models\ContractItem
     */
    protected function createContractItem(CollectionItem $collectionItem): ContractItem
    {
        /** @var \App\Models\Contract $contract */
        $contract = Contract::find($this->collection->contract_id);

        /** @var \App\Models\ContractItem $contractItem */
        $contractItem = $contract->contractItems()->create([
            'item_id' => $collectionItem->product_id,
            'item_type' => Product::class,
            'quantity' => 1,
            'unit_amount' => $collectionItem->product->default_price,
            'total_amount' => $collectionItem->product->default_price,
            'visible_in_collections' => false,
        ]);

        return $contractItem;
    }

    /**
     * Delete the unmatching items.
     *
     * @param  \Illuminate\Support\Collection $productsCollection
     * @return void
     */
    protected function deleteUnmatchingItems(\Illuminate\Support\Collection $productsCollection): void
    {
        $this->collection->collectionItems
            ->where('collection_id', $this->collection->id)
            ->whereNotIn('product_id', $productsCollection->pluck('product_id'))
            ->each(function (CollectionItem $collectionItem): void {
                IncreaseStockLocationProductQuantity::run(
                    $this->customerStockLocation,
                    $collectionItem->product_id,
                    $collectionItem->quantity
                );

                HandleStockMovementSummary::run($collectionItem, true);

                $collectionItem->delete();
            });
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendCollectionReceiptEmail::dispatch($this->collection, false, false, auth()->id(), true);
        } catch (Throwable $th) {
            database_notification($userId, "A coleta #{$this->collection->id} foi atualizada, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");
            error($th);
        }
    }
}
