<?php

namespace App\Actions\Collection;

use App\Models\Collection;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateCollectionPdf
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @return \Illuminate\Http\Response
     */
    public function handle(Collection $collection): Response
    {
        try {
            return Pdf::loadView('reports.collections.collection-receipt-report', [
                'collection' => $collection,
                'logoSrc' => p4m_tenant()->getLogoSrc()
            ])->stream();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
