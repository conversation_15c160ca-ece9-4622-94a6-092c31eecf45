<?php

namespace App\Actions\Collection;

use App\Models\Collection;
use Barryvdh\DomPDF\Facade\Pdf;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateCollectionReceipt
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @return mixed
     */
    public function handle(Collection $collection): mixed
    {
        return Pdf::loadView('reports.collections.collection-receipt', [
            'collection' => $collection,
            'logoSrc' => p4m_tenant()->getLogoSrc()
        ])->stream();
    }
}
