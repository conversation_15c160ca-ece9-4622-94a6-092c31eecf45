<?php

namespace App\Actions\Collection;

use App\Actions\StockLocationProduct\IncreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Collection;
use App\Models\CollectionItem;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteCollection
{
    use AsAction;

    protected int $collectionId;
    protected string $operationEmail;
    protected string $tradingName;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @param  int $userId
     * @param  bool $sendEmail
     * @return void
     */
    public function handle(Collection $collection, int $userId, bool $sendEmail = true): void
    {
        try {
            $this->collectionId = $collection->id;
            $this->operationEmail = $collection->customer->operation_email;
            $this->tradingName = $collection->customer->trading_name;

            DB::transaction(function () use ($collection) {
                $customerStockLocation = $collection->customer->stockLocations->first();

                $collection->customerHistories()->delete();

                $collection->collectionItems->each(function (CollectionItem $collectionItem) use ($customerStockLocation) {
                    IncreaseStockLocationProductQuantity::run(
                        $customerStockLocation,
                        $collectionItem->product_id,
                        $collectionItem->quantity
                    );

                    HandleStockMovementSummary::run($collectionItem, true);

                    $collectionItem->delete();
                });

                $collection->delete();
            });

            if ($sendEmail) {
                $this->sendEmail($userId);
            }
        } catch (Throwable $th) {
            throw_error($th);
        }
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendCollectionDeleteReceiptEmail::dispatch(
                $this->collectionId,
                $this->operationEmail,
                $this->tradingName,
                false,
                false,
                auth()->id()
            );
        } catch (Throwable $th) {
            database_notification($userId, "A coleta #{$this->collectionId} foi excluída, mas não foi possível enviar seu e-mail de confirmação.");
            error($th);
        }
    }
}
