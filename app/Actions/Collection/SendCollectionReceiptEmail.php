<?php

namespace App\Actions\Collection;

use App\Actions\Core\SendEmail;
use App\Models\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisle<PERSON>\Actions\Decorators\JobDecorator;
use Throwable;

class SendCollectionReceiptEmail
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(
            config('queue.default_names.p4m.emails.data_send')
        );
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Collection $collection
     * @param  bool $automatic
     * @param  bool $throwExceptionOnError
     * @param  int|null $userId
     * @param  bool $updatedCollection
     * @return void
     */
    public function handle(
        Collection $collection,
        bool $automatic = false,
        bool $throwExceptionOnError = false,
        ?int $userId = null,
        bool $updatedCollection = false
    ): void {
        $toEmails = explode(',', $collection->customer->operation_email);

        if (is_null($toEmails)) {
            throw_if(!$automatic, 'Não existem endereços de e-mail de operação cadastrados para o cliente desta coleta.');
            return;
        }

        $subject = 'Movimentação de coleta ' . p4m_tenant()->getCompanyName() . " - {$collection->customer->trading_name}";

        $viewName = $updatedCollection
            ? 'emails.collection-update-receipt-email'
            : 'emails.collection-receipt-email';

        $message = view($viewName, [
            'collection' => $collection,
            'companyName' => p4m_tenant()->getCompanyName()
        ])->render();

        try {
            SendEmail::run($toEmails, $subject, $message);
        } catch (Throwable $th) {
            error($th);
            database_notification($userId, "A coleta #{$collection->id} foi salva, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");

            if ($throwExceptionOnError) {
                throw $th;
            }

            return;
        }

        $collection->update(['email_sent' => true]);

        $collection->collectionReceiptEmails()->create([
            'to_emails' => $toEmails,
            'message' => $message,
            'email_sent_at' => now()
        ]);
    }
}
