<?php

namespace App\Actions\Collection;

use App\Actions\CollectionItem\CreateCollectionItem;
use App\Actions\CustomerHistory\CreateCustomerHistory;
use App\Models\Collection;
use App\Models\StockLocation;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateCollection
{
    use AsAction;

    protected Collection $collection;
    protected StockLocation $customerStockLocation;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @param  int $userId
     * @param  bool $sendEmail
     * @return \App\Models\Collection
     */
    public function handle(array $data, int $userId, bool $sendEmail = true): Collection
    {
        $products = $data['products'];
        unset($data['products']);

        $products = array_filter($products, function (array $collectionProduct): bool {
            return !is_null($collectionProduct['product_id']) && $collectionProduct['quantity'] > 0;
        });

        if (empty($products)) {
            throw new Exception('A coleta não possui itens.');
        }

        try {
            $this->collection = DB::transaction(function () use ($data, $products) {
                $this->collection = Collection::create($data);
                $this->customerStockLocation = $this->collection->customer->stockLocations->first();

                if ($this->collection->additional_info) {
                    $this->createCustomerHistory();
                }

                $this->createItems($products);

                return $this->collection;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }

        if ($sendEmail && isset($data['send_email']) && (bool) $data['send_email']) {
            $this->sendEmail($userId);
        }

        return $this->collection;
    }

    /**
     * Create the customer history.
     *
     * @return void
     */
    protected function createCustomerHistory(): void
    {
        CreateCustomerHistory::run(
            $this->collection->customer,
            $this->collection->id,
            Collection::class,
            $this->collection->additional_info
        );
    }

    /**
     * Create the collection items.
     *
     * @param  array $products
     * @return void
     */
    protected function createItems(array $products): void
    {
        collect($products)->each(function (array $product) {
            CreateCollectionItem::run(
                $this->collection,
                $product['product_id'],
                (float) $product['quantity'],
                $this->customerStockLocation
            );
        });
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendCollectionReceiptEmail::dispatch($this->collection, false, false, auth()->id());
        } catch (Throwable $th) {
            database_notification($userId, "A coleta #{$this->collection->id} foi salva, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");
            error($th);
        }
    }
}
