<?php

namespace App\Actions\Supplier;

use App\Models\Supplier;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteSupplier
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Supplier $supplier
     * @return void
     */
    public function handle(Supplier $supplier): void
    {
        $supplier->load(['contracts']);

        if ($supplier->contracts->count() > 0) {
            throw new Exception('asd');
        }

        try {
            DB::transaction(function () use ($supplier) {
                $supplier->integrationLogs()->delete();
                $supplier->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
