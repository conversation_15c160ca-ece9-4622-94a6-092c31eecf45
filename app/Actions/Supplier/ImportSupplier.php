<?php

namespace App\Actions\Supplier;

use App\Models\Supplier;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ImportSupplier
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Supplier|null
     */
    public function handle(array $data): ?Supplier
    {
        if (!isset($data['tax_id_number']) || !$data['tax_id_number'] || $data['tax_id_number'] === '') {
            error("{$data['name']} - CNPJ vazio/nulo.");
            return null;
        }

        try {
            /** @var \App\Models\Supplier $supplier */
            $supplier = Supplier::query()
                ->where('tax_id_number', get_numbers($data['tax_id_number']))
                ->first();

            return !$supplier
                ? CreateSupplier::run($data)
                : UpdateSupplier::run($supplier, $data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
