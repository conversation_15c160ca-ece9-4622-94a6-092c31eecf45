<?php

namespace App\Actions\Supplier;

use App\Models\Supplier;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateSupplier
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Supplier
     */
    public function handle(array $data): Supplier
    {
        /** @var \App\Models\Supplier $supplier */
        $supplier = Supplier::create($data);

        return $supplier;
    }
}
