<?php

namespace App\Actions\Supplier;

use App\Models\Supplier;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateSupplier
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Supplier $supplier
     * @param  array $data
     * @return \App\Models\Supplier
     */
    public function handle(Supplier $supplier, array $data): Supplier
    {
        $supplier->update($data);

        return $supplier;
    }
}
