<?php

namespace App\Actions\StockLocationProduct;

use App\Models\StockLocation;
use App\Models\StockLocationProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class DecreaseStockLocationProductQuantity
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\StockLocation $stockLocation
     * @param  int $productId
     * @param  float $quantity
     * @return \App\Models\StockLocation
     */
    public function handle(StockLocation $stockLocation, int $productId, float $quantity): StockLocation
    {
        StockLocationProduct::query()
            ->where('stock_location_id', $stockLocation->id)
            ->where('product_id', $productId)
            ->decrement('current_quantity', $quantity);

        return $stockLocation;
    }
}
