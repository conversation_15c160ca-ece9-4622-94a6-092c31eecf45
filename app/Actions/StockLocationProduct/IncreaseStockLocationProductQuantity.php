<?php

namespace App\Actions\StockLocationProduct;

use App\Models\StockLocation;
use App\Models\StockLocationProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class IncreaseStockLocationProductQuantity
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\StockLocation $stockLocation
     * @param  int $productId
     * @param  float $quantity
     * @return \App\Models\StockLocation
     */
    public function handle(StockLocation $stockLocation, int $productId, float $quantity): StockLocation
    {
        /** @var \App\Models\StockLocationProduct $stockLocationProduct */
        $stockLocationProduct = StockLocationProduct::query()
            ->where('stock_location_id', $stockLocation->id)
            ->where('product_id', $productId)
            ->first();

        if (is_null($stockLocationProduct)) {
            /** @var \App\Models\StockLocationProduct $stockLocationProduct */
            $stockLocationProduct = StockLocationProduct::create([
                'stock_location_id' => $stockLocation->id,
                'product_id' => $productId,
                'initial_quantity' => $quantity,
                'current_quantity' => $quantity
            ]);

            return $stockLocation;
        }

        $stockLocationProduct->increment('current_quantity', $quantity);

        return $stockLocation;
    }
}
