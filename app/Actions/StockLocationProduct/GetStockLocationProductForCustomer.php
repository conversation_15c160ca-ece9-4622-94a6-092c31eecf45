<?php

namespace App\Actions\StockLocationProduct;

use App\Models\StockLocationProduct;
use Lorisleiva\Actions\Concerns\AsAction;

class GetStockLocationProductForCustomer
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  int $productId
     * @param  int $customerId
     * @return \App\Models\StockLocationProduct|null
     */
    public function handle(int $productId, int $customerId): ?StockLocationProduct
    {
        return StockLocationProduct::query()
            ->where('product_id', $productId)
            ->whereRelation('stockLocation', 'customer_id', $customerId)
            ->first();
    }
}
