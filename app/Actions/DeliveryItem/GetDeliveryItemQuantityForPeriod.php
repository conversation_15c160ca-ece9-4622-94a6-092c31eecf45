<?php

namespace App\Actions\DeliveryItem;

use App\Models\DeliveryItem;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;

class GetDeliveryItemQuantityForPeriod
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\DeliveryItem $deliveryItem
     * @param  string|null $dateFrom
     * @param  mixed $dateTo
     * @return float
     */
    public function handle(DeliveryItem $deliveryItem, ?string $dateFrom, mixed $dateTo = null): float
    {
        if (!$dateTo) {
            $dateTo = now();
        }

        return DeliveryItem::query()
            ->where('product_id', $deliveryItem->product_id)
            ->whereHas('delivery', function (Builder $query) use ($deliveryItem, $dateFrom, $dateTo): Builder {
                return $query
                    ->where('customer_id', $deliveryItem->delivery->customer_id)
                    ->where('delivered_at', '>', carbon($dateFrom)->format('Y-m-d'))
                    ->where('delivered_at', '<=', carbon($dateTo)->format('Y-m-d'));
            })
            ->get()
            ->sum(function (DeliveryItem $deliveryItem) {
                return $deliveryItem->quantity;
            });
    }
}
