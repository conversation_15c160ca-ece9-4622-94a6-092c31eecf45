<?php

namespace App\Actions\CustomerBasicStockStats;

use App\Models\CollectionItem;
use App\Models\Customer;
use App\Models\CustomerBasicStockStats;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class CalculateCustomerBasicStockStats
{
    use AsAction;

    public string $commandSignature = 'p4m:calculate_customer_basic_stock_stats {customer_id?}';

    protected Customer $customer;
    protected Carbon $dateFrom;
    protected Carbon $dateTo;

    /**
     * Handle the action as a command.
     *
     * @param  \Illuminate\Console\Command $command
     * @return void
     */
    public function asCommand(Command $command): void
    {
        tenancy()->initialize('brisa');

        $customer = $command->argument('customer_id')
            ? Customer::find($command->argument('customer_id'))
            : null;

        $this->handle($customer);

        $command->info('Done!');
    }

    public function handle(?Customer $customer = null, ?Carbon $dateFrom = null, ?Carbon $dateTo = null): void
    {
        $this->dateFrom = $dateFrom ?? now()->subMonths(3);
        $this->dateTo = $dateTo ?? now();

        CollectionItem::query()
            ->join('collections', 'collections.id', 'collection_items.collection_id')
            ->select([
                'collections.customer_id',
                'collection_items.product_id',
                'collection_items.quantity',
                'collections.collected_at',
                DB::raw('weekday(collections.collected_at) as weekday'),
            ])
            ->whereHas('collection', function (Builder $query) use ($customer): Builder {
                return $query
                    ->where('collected_at', '>=', $this->dateFrom->format('Y-m-d'))
                    ->where('collected_at', '<=', $this->dateTo->format('Y-m-d'))
                    ->when($customer, fn (Builder $query): Builder => $query->where('customer_id', $customer->id));
            })
            ->get()
            ->map(fn (mixed $collectionItem): array => [
                'customer_id' => $collectionItem->customer_id,
                'product_id' => $collectionItem->product_id,
                'quantity' => (float) $collectionItem->quantity,
                'collected_at' => $collectionItem->collected_at,
                'weekday' => $collectionItem->weekday,
            ])
            ->groupBy('customer_id')
            ->each(function (\Illuminate\Support\Collection $customerGroup, int $key): void {
                $this->handleCustomerGroup($key, $customerGroup);
            });
    }

    /**
     * Handle the customer group.
     *
     * @param  int $customerId
     * @param  \Illuminate\Support\Collection $customerGroup
     * @return void
     */
    protected function handleCustomerGroup(
        int $customerId,
        \Illuminate\Support\Collection $customerGroup
    ): void {
        CustomerBasicStockStats::query()
            ->where('customer_id', $customerId)
            ->delete();

        $customerGroup
            ->groupBy('weekday')
            ->each(function (\Illuminate\Support\Collection $customerWeekdayGroup, int $weekday) use ($customerId): void {
                $this->handleCustomerWeekdayGroup($customerId, $weekday, $customerWeekdayGroup);
            });
    }

    /**
     * Handle the customer weekday group.
     *
     * @param  int $customerId
     * @param  int $weekday
     * @param  \Illuminate\Support\Collection $customerWeekdayGroup
     * @return void
     */
    protected function handleCustomerWeekdayGroup(
        int $customerId,
        int $weekday,
        \Illuminate\Support\Collection $customerWeekdayGroup
    ): void {
        $customerWeekdayGroup
            ->groupBy('product_id')
            ->each(function (\Illuminate\Support\Collection $customerWeekdayProductGroup, int $productId) use ($customerId, $weekday): void {
                $this->handleCustomerWeekdayProductGroup($customerId, $weekday, $productId, $customerWeekdayProductGroup);
            });
    }

    /**
     * Handle the customer weekday product group.
     *
     * @param  int $customerId
     * @param  int $weekday
     * @param  int $productId
     * @param  \Illuminate\Support\Collection $customerWeekdayProductGroup
     * @return void
     */
    protected function handleCustomerWeekdayProductGroup(
        int $customerId,
        int $weekday,
        int $productId,
        \Illuminate\Support\Collection $customerWeekdayProductGroup
    ): void {
        $basicData = [
            'customer_id' => $customerId,
            'product_id' => $productId,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'weekday' => ($weekday + 1) % 7,
        ];

        $maxAmount = $this->calculateMaxAmount($customerWeekdayProductGroup);
        CustomerBasicStockStats::create(array_merge($basicData, [
            'amount' => $maxAmount,
            'stats_type' => 'max',
        ]));

        $minAmount = $this->calculateMinAmount($customerWeekdayProductGroup);
        CustomerBasicStockStats::create(array_merge($basicData, [
            'amount' => $minAmount,
            'stats_type' => 'min',
        ]));

        $basicMean = $this->calculateBasicMean($customerWeekdayProductGroup);
        CustomerBasicStockStats::create(array_merge($basicData, [
            'amount' => $basicMean,
            'stats_type' => 'basic-mean',
        ]));

        $standardDeviation = $this->calculateStandardDeviation($customerWeekdayProductGroup, $basicMean);
        CustomerBasicStockStats::create(array_merge($basicData, [
            'amount' => $standardDeviation,
            'stats_type' => 'standard-deviation',
        ]));
    }

    /**
     * Calculate the max amount.
     *
     * @param  \Illuminate\Support\Collection $customerWeekdayProductGroup
     * @return float
     */
    protected function calculateMaxAmount(\Illuminate\Support\Collection $customerWeekdayProductGroup): float
    {
        return $customerWeekdayProductGroup->max(fn ($item): float => $item['quantity']);
    }

    /**
     * Calculate the min amount.
     *
     * @param  \Illuminate\Support\Collection $customerWeekdayProductGroup
     * @return float
     */
    protected function calculateMinAmount(\Illuminate\Support\Collection $customerWeekdayProductGroup): float
    {
        return $customerWeekdayProductGroup->min(fn ($item): float => $item['quantity']);
    }

    /**
     * Calculate the basic mean.
     *
     * @param  \Illuminate\Support\Collection $customerWeekdayProductGroup
     * @return float
     */
    protected function calculateBasicMean(\Illuminate\Support\Collection $customerWeekdayProductGroup): float
    {
        $totalAmount = $customerWeekdayProductGroup->sum(fn ($item): float => $item['quantity']);
        return $totalAmount / $customerWeekdayProductGroup->count();
    }

    /**
     * Calculate the standard deviation.
     *
     * @param  \Illuminate\Support\Collection $customerWeekdayProductGroup
     * @param  float $basicMean
     * @return float
     */
    protected function calculateStandardDeviation(
        \Illuminate\Support\Collection $customerWeekdayProductGroup,
        float $basicMean = 0
    ): float {
        $n = $customerWeekdayProductGroup->count();

        if ($n === 0) {
            trigger_error("The array has zero elements", E_USER_WARNING);
            return false;
        }

        $basicMean = $basicMean > 0
            ? $basicMean
            : $this->calculateBasicMean($customerWeekdayProductGroup);

        $carry = 0.0;

        foreach ($customerWeekdayProductGroup as $customerWeekdayProduct) {
            $d = ((float) $customerWeekdayProduct['quantity']) - $basicMean;
            $carry += ($d * $d);
        }

        return sqrt($carry / $n);
    }
}
