<?php

namespace App\Actions\Product;

use App\Enums\CategoryTypeEnum;
use App\Models\Category;
use App\Models\Product;
use App\Models\Subcategory;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ImportProduct
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Product
     */
    public function handle(array $data): Product
    {
        try {
            /** @var \App\Models\Category $category */
            $category = Category::updateOrCreate([
                'name' => $data['category_name'],
                'type' => CategoryTypeEnum::Product->value,
            ]);

            /** @var \App\Models\Subcategory $subcategory */
            $subcategory = Subcategory::updateOrCreate([
                'category_id' => $category->id,
                'name' => $data['subcategory_name']
            ]);

            return Product::updateOrCreate([
                'code' => $data['code']
            ], [
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'subcategory_id' => $subcategory->id
            ]);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
