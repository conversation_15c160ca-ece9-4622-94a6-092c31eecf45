<?php

namespace App\Actions\Product;

use App\Models\Product;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateProduct
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Product
     */
    public function handle(array $data): Product
    {
        /** @var \App\Models\Product $product */
        $product = Product::create($data);

        return $product;
    }
}
