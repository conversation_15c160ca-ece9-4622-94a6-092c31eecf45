<?php

namespace App\Actions\ApiRequestLog;

use App\Models\ApiRequestLog;
use Lorisleiva\Actions\Concerns\AsAction;
use Sammyjo20\Saloon\Http\SaloonResponse;
use Throwable;

class CreateApiRequestLog
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  mixed $model
     * @param  \Sammyjo20\Saloon\Http\SaloonResponse $response
     * @param  bool|null $success
     * @param  string|null $errorDescription
     * @param  array|null $requestBody
     * @return \App\Models\ApiRequestLog
     */
    public function handle(
        mixed $model,
        SaloonResponse $response,
        ?bool $success = null,
        ?string $errorDescription = null,
        array $requestBody = null
    ): ApiRequestLog {
        try {
            $request = $response->getOriginalRequest();

            if (is_null($success)) {
                $success = $response->successful();
            }

            $requestHeaders = openssl_encrypt(json_encode(['data' => $request->getHeaders()], true), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'));

            $requestBody = openssl_encrypt(json_encode($requestBody), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'));

            $responseHeaders = openssl_encrypt(json_encode($response->headers()), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'));

            $responseBody = is_null(json_decode($response->body(), true))
                ? openssl_encrypt(json_encode(['data' => $response->body()]), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'))
                : openssl_encrypt($response->body(), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'));

            $debugBacktrace = debug_backtrace();

            return ApiRequestLog::create([
                'model_id' => $model->id,
                'model_type' => get_class($model),
                'service_name' => $debugBacktrace[2]['class'] ?? null,
                'service_method' => $debugBacktrace[2]['function'] ?? null,
                'success' => $success,
                'status_code' => $response->status(),
                'response_headers' => $responseHeaders,
                'response_body' => $responseBody,
                'method' => $request->getMethod(),
                'url' => $request->getFullRequestUrl(),
                'request_headers' => $requestHeaders,
                'request_body' => $requestBody,
                'error_description' => $errorDescription,
            ]);
        } catch (Throwable $th) {
            error($th);
        }
    }
}
