<?php

namespace App\Actions\Delivery;

use App\Actions\StockLocationProduct\DecreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteDelivery
{
    use AsAction;

    protected int $deliveryId;
    protected string $operationEmail;
    protected string $tradingName;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Delivery $delivery
     * @param  int $userId
     * @param  bool $sendEmail
     * @return void
     */
    public function handle(Delivery $delivery, int $userId, bool $sendEmail = true): void
    {
        $this->deliveryId = $delivery->id;
        $this->operationEmail = $delivery->customer->operation_email;
        $this->tradingName = $delivery->customer->trading_name;

        DB::transaction(function () use ($delivery) {
            $customerStockLocation = $delivery->customer->stockLocations->first();

            $delivery->deliveryItems->each(function (DeliveryItem $deliveryItem) use ($customerStockLocation) {
                DecreaseStockLocationProductQuantity::run(
                    $customerStockLocation,
                    $deliveryItem->product_id,
                    $deliveryItem->quantity
                );

                HandleStockMovementSummary::run($deliveryItem, true);

                $deliveryItem->delete();
            });

            $delivery->delete();
        });

        if ($sendEmail) {
            $this->sendEmail($userId);
        }
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendDeliveryDeleteReceiptEmail::dispatch(
                $this->deliveryId,
                $this->operationEmail,
                $this->tradingName,
                false,
                false,
                auth()->id()
            );
        } catch (Throwable $th) {
            database_notification($userId, "A coleta #{$this->deliveryId} foi excluída, mas não foi possível enviar seu e-mail de confirmação.");
            error($th);
        }
    }
}
