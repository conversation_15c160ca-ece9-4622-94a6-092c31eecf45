<?php

namespace App\Actions\Delivery;

use App\Models\Delivery;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Response;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateDeliveryPdf
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Delivery $delivery
     * @return \Illuminate\Http\Response
     */
    public function handle(Delivery $delivery): Response
    {
        try {
            return Pdf::loadView('reports.deliveries.delivery-receipt-report', [
                'delivery' => $delivery,
                'logoSrc' => p4m_tenant()->getLogoSrc()
            ])->stream();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
