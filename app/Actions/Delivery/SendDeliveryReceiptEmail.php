<?php

namespace App\Actions\Delivery;

use App\Actions\Core\SendEmail;
use App\Models\Delivery;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Lorisle<PERSON>\Actions\Decorators\JobDecorator;
use Throwable;

class SendDeliveryReceiptEmail
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(
            config('queue.default_names.p4m.emails.data_send')
        );
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\Delivery $delivery
     * @param  bool $automatic
     * @param  bool $throwExceptionOnError
     * @param  int|null $userId
     * @param  bool $updatedDelivery
     * @return void
     */
    public function handle(
        Delivery $delivery,
        bool $automatic = false,
        bool $throwExceptionOnError = false,
        ?int $userId = null,
        bool $updatedDelivery = false
    ): void {
        $toEmails = explode(',', $delivery->customer->operation_email);

        if (is_null($toEmails)) {
            throw_if(!$automatic, 'Não existem endereços de e-mail de operação cadastrados para o cliente desta entrega.');
            return;
        }

        $subject = 'Movimentação de entrega ' . p4m_tenant()->getCompanyName() . " - {$delivery->customer->trading_name}";

        $viewName = $updatedDelivery
            ? 'emails.delivery-update-receipt-email'
            : 'emails.delivery-receipt-email';

        $message = view($viewName, [
            'delivery' => $delivery,
            'companyName' => p4m_tenant()->getCompanyName()
        ])->render();

        try {
            SendEmail::run($toEmails, $subject, $message);
        } catch (Throwable $th) {
            error($th);
            database_notification($userId, "A entrega #{$delivery->id} foi salva, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");

            if ($throwExceptionOnError) {
                throw $th;
            }

            return;
        }

        $delivery->update(['email_sent' => true]);

        $delivery->deliveryReceiptEmails()->create([
            'to_emails' => $toEmails,
            'message' => $message,
            'email_sent_at' => now()
        ]);
    }
}
