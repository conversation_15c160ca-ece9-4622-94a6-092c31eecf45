<?php

namespace App\Actions\Delivery;

use App\Actions\Core\SendEmail;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class SendDeliveryDeleteReceiptEmail
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(
            config('queue.default_names.p4m.emails.data_send')
        );
    }

    /**
     * Handle the action.
     *
     * @param  int $deliveryId
     * @param  string $operationEmail
     * @param  string $tradingName
     * @param  bool $automatic
     * @param  bool $throwExceptionOnError
     * @param  int|null $userId
     * @return void
     */
    public function handle(
        int $deliveryId,
        string $operationEmail,
        string $tradingName,
        bool $automatic = false,
        bool $throwExceptionOnError = false,
        ?int $userId = null,
    ): void {
        $toEmails = explode(',', $operationEmail);

        if (is_null($toEmails)) {
            throw_if(!$automatic, 'Não existem endereços de e-mail de operação cadastrados para o cliente desta entrega.');
            return;
        }

        $subject = 'Movimentação de entrega ' . p4m_tenant()->getCompanyName() . " - $tradingName";

        $message = view('emails.delivery-delete-receipt-email', [
            'deliveryId' => $deliveryId,
            'companyName' => p4m_tenant()->getCompanyName()
        ])->render();

        try {
            SendEmail::run($toEmails, $subject, $message);
        } catch (Throwable $th) {
            error($th);
            database_notification($userId, "A entrega #{$deliveryId} foi excluída, mas não foi possível enviar seu e-mail de recibo.");

            if ($throwExceptionOnError) {
                throw $th;
            }

            return;
        }
    }
}
