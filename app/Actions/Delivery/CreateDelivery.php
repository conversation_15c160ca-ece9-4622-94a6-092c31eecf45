<?php

namespace App\Actions\Delivery;

use App\Actions\StockLocationProduct\IncreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Delivery;
use App\Models\StockLocation;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateDelivery
{
    use AsAction;

    protected Delivery $delivery;
    protected StockLocation $customerStockLocation;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @param  int $userId
     * @param  bool $sendEmail
     * @param  float|null $percentage
     * @return \App\Models\Delivery
     */
    public function handle(array $data, int $userId, bool $sendEmail = true, ?float $percentage = null): Delivery
    {
        $products = $data['products'];
        unset($data['products']);

        $percentage = !is_null($percentage)
            ? (unmask_percentage($percentage) / 100)
            : 1;

        try {
            $this->delivery = DB::transaction(function () use ($data, $products, $percentage) {
                /** @var \App\Models\Delivery $delivery */
                $this->delivery = Delivery::create($data);

                $this->customerStockLocation = $this->delivery->customer->stockLocations->first();

                $this->createItems($products, $percentage);

                return $this->delivery;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }

        if ($sendEmail && isset($data['send_email']) && (bool) $data['send_email']) {
            $this->sendEmail($userId);
        }

        return $this->delivery;
    }

    /**
     * Create the items.
     *
     * @param  array $products
     * @param  float $percentage
     * @return void
     */
    protected function createItems(array $products, float $percentage): void
    {
        collect($products)->each(function (array $product) use ($percentage) {
            $quantity = ceil(((float) $product['quantity']) * $percentage);

            /** @var \App\Models\DeliveryItem $deliveryItem */
            $deliveryItem = $this->delivery->deliveryItems()->create([
                'product_id' => $product['product_id'],
                'quantity' => $quantity
            ]);

            IncreaseStockLocationProductQuantity::run(
                $this->customerStockLocation,
                $product['product_id'],
                $quantity
            );

            HandleStockMovementSummary::run($deliveryItem);
        });
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendDeliveryReceiptEmail::dispatch($this->delivery, false, false, auth()->id());
        } catch (Throwable $th) {
            database_notification($userId, "A entrega #{$this->delivery->id} foi salva, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");
            error($th);
        }
    }
}
