<?php

namespace App\Actions\Delivery;

use App\Models\CustomerProduct;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use App\Models\Product;
use Barryvdh\DomPDF\Facade\Pdf;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateDeliveryReceipts
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \Illuminate\Support\Collection $deliveries
     * @return mixed
     */
    public function handle(string $recordsToken): mixed
    {
        $deliveries = Delivery::query()
            ->with(['customer', 'deliveryItems'])
            ->whereIn('id', explode(',', base64_decode($recordsToken)))
            ->get()
            ->map(function (Delivery $delivery): array {
                return array_merge($delivery->toArray(), [
                    'delivery_items' => $delivery->deliveryItems
                        ->map(function (DeliveryItem $deliveryItem): array {
                            /** @var \App\Models\CustomerProduct $customerProductWithPrintProduct */
                            $customerProductWithPrintProduct = $deliveryItem->delivery->customer->customerProducts
                                ->filter(fn (CustomerProduct $customerProduct): bool => !is_null($customerProduct->print_product_id) && ((int) $customerProduct->item_id === (int) $deliveryItem->product_id))
                                ->first();

                            if (is_null($customerProductWithPrintProduct)) {
                                return [
                                    'id' => $deliveryItem->id,
                                    'product_id' => $deliveryItem->product_id,
                                    'name' => $deliveryItem->product->name ?? \App\Models\Product::query()->withTrashed()->findOrFail($deliveryItem->product_id)->name,
                                    'quantity' => $deliveryItem->quantity,
                                ];
                            }

                            return [
                                'id' => $deliveryItem->id,
                                'product_id' => $deliveryItem->product_id,
                                'name' => $customerProductWithPrintProduct->printProduct->name,
                                'quantity' => $deliveryItem->quantity,
                            ];
                        })
                ]);
            });

        $deliveryItemSummary = DeliveryItem::query()
            ->whereIn('delivery_id', $deliveries->pluck('id')->toArray())
            ->get()
            ->groupBy('product.name')
            ->map(function (\Illuminate\Support\Collection $deliveryItemGroup, string $key): array {
                return [
                    'product_name' => is_null($key) || $key === ''
                        ? Product::query()->withTrashed()->findOrFail($deliveryItemGroup->first()->product_id)->name
                        : $key,
                    'quantity' => $deliveryItemGroup->sum(fn (DeliveryItem $deliveryItem): float => $deliveryItem->quantity)
                ];
            })
            ->sortByDesc('quantity');

        return Pdf::loadView('reports.deliveries.delivery-receipts', [
            'deliveries' => $deliveries,
            'deliveryItemSummary' => $deliveryItemSummary,
            'logoSrc' => p4m_tenant()->getLogoSrc()
        ])->stream();
    }
}
