<?php

namespace App\Actions\Delivery;

use App\Actions\StockLocationProduct\DecreaseStockLocationProductQuantity;
use App\Actions\StockLocationProduct\IncreaseStockLocationProductQuantity;
use App\Actions\StockMovementSummary\HandleStockMovementSummary;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use App\Models\StockLocation;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditDelivery
{
    use AsAction;

    protected Delivery $delivery;
    protected StockLocation $customerStockLocation;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Delivery $delivery
     * @param  array $data
     * @param  int $userId
     * @param  bool $sendEmail
     * @return \App\Models\Delivery
     */
    public function handle(Delivery $delivery, array $data, int $userId, bool $sendEmail = true): Delivery
    {
        $this->delivery = DB::transaction(function () use ($delivery, $data) {
            $this->delivery = $delivery;
            $this->customerStockLocation = $this->delivery->customer->stockLocations->first();

            $this->delivery->update($data);

            $this->updateItems(collect($data['products']));

            return $delivery;
        });

        if ($sendEmail && isset($data['send_email']) && (bool) $data['send_email']) {
            $this->sendEmail($userId);
        }

        return $this->delivery;
    }

    /**
     * Update the items.
     *
     * @param  \Illuminate\Support\Collection $productsCollection
     * @return void
     */
    protected function updateItems(Collection $productsCollection): void
    {
        $productsCollection->each(function (array $productData) {
            $existingDeliveryItem = DeliveryItem::query()
                ->where('delivery_id', $this->delivery->id)
                ->where('product_id', $productData['product_id'])
                ->first();

            if ($existingDeliveryItem) {
                $this->updateSingleItem($existingDeliveryItem, $productData);
                return;
            }

            $this->createSingleItem($productData);
        });

        $this->deleteUnmatchingItems($productsCollection);
    }

    /**
     * Update a single item.
     *
     * @param  \App\Models\DeliveryItem $existingDeliveryItem
     * @param  array $productData
     * @return void
     */
    protected function updateSingleItem(DeliveryItem $existingDeliveryItem, array $productData): void
    {
        $oldQuantity = $existingDeliveryItem->quantity;

        DecreaseStockLocationProductQuantity::run(
            $this->customerStockLocation,
            $existingDeliveryItem->product_id,
            $oldQuantity
        );

        HandleStockMovementSummary::run($existingDeliveryItem, true);

        $existingDeliveryItem->update(['quantity' => (float) $productData['quantity']]);

        IncreaseStockLocationProductQuantity::run(
            $this->customerStockLocation,
            $existingDeliveryItem->product_id,
            $existingDeliveryItem->quantity
        );

        HandleStockMovementSummary::run($existingDeliveryItem);
    }

    /**
     * Create a single item.
     *
     * @param  array $productData
     * @return void
     */
    protected function createSingleItem(array $productData): void
    {
        /** @var \App\Models\DeliveryItem $deliveryItem */
        $deliveryItem = $this->delivery->deliveryItems()->create([
            'product_id' => $productData['product_id'],
            'quantity' => (float) $productData['quantity']
        ]);

        DecreaseStockLocationProductQuantity::run(
            $this->customerStockLocation,
            $productData['product_id'],
            (float) $productData['quantity']
        );

        HandleStockMovementSummary::run($deliveryItem);
    }

    /**
     * Delete the unmatching items.
     *
     * @param  \Illuminate\Support\Collection $productsCollection
     * @return void
     */
    protected function deleteUnmatchingItems(\Illuminate\Support\Collection $productsCollection): void
    {
        $this->delivery->deliveryItems
            ->where('delivery_id', $this->delivery->id)
            ->whereNotIn('product_id', $productsCollection->pluck('product_id'))
            ->each(function (DeliveryItem $deliveryItem): void {
                DecreaseStockLocationProductQuantity::run(
                    $this->customerStockLocation,
                    $deliveryItem->product_id,
                    $deliveryItem->quantity
                );

                HandleStockMovementSummary::run($deliveryItem, true);

                $deliveryItem->delete();
            });
    }

    /**
     * Send the e-mail message.
     *
     * @param  int $userId
     * @return void
     */
    protected function sendEmail(int $userId): void
    {
        try {
            SendDeliveryReceiptEmail::dispatch($this->delivery, false, false, auth()->id(), true);
        } catch (Throwable $th) {
            database_notification($userId, "A entrega #{$this->delivery->id} foi atualizada, mas não foi possível enviar seu e-mail de recibo. Verifique se o e-mail cadastrado está correto e tente novamente.");
            error($th);
        }
    }
}
