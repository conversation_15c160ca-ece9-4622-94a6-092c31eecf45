<?php

namespace App\Actions\Delivery;

use App\Models\CustomerProduct;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use Barryvdh\DomPDF\Facade\Pdf;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateDeliveryReceipt
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Delivery $delivery
     * @return mixed
     */
    public function handle(Delivery $delivery): mixed
    {
        return Pdf::loadView('reports.deliveries.delivery-receipt', [
            'delivery' => $delivery,
            'deliveryItems' => $delivery->deliveryItems
                ->map(function (DeliveryItem $deliveryItem): array {
                    /** @var \App\Models\CustomerProduct $customerProductWithPrintProduct */
                    $customerProductWithPrintProduct = $deliveryItem->delivery->customer->customerProducts
                        ->filter(fn (CustomerProduct $customerProduct): bool => !is_null($customerProduct->print_product_id) && ((int) $customerProduct->item_id === (int) $deliveryItem->product_id))
                        ->first();

                    if (is_null($customerProductWithPrintProduct)) {
                        return [
                            'id' => $deliveryItem->id,
                            'name' => $deliveryItem->product->name ?? \App\Models\Product::query()->withTrashed()->findOrFail($deliveryItem->product_id)->name,
                            'quantity' => $deliveryItem->quantity,
                        ];
                    }

                    return [
                        'id' => $deliveryItem->id,
                        'name' => $customerProductWithPrintProduct->printProduct->name,
                        'quantity' => $deliveryItem->quantity,
                    ];
                }),
            'logoSrc' => p4m_tenant()->getLogoSrc()
        ])->stream();
    }
}
