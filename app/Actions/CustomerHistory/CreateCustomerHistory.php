<?php

namespace App\Actions\CustomerHistory;

use App\Models\Customer;
use App\Models\CustomerHistory;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateCustomerHistory
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Customer $customer
     * @param  int $operationId
     * @param  string $operationType
     * @param  string $history
     * @return \App\Models\CustomerHistory
     */
    public function handle(
        Customer $customer,
        int $operationId,
        string $operationType,
        string $history
    ): CustomerHistory {
        return $customer->customerHistories()->create([
            'operation_id' => $operationId,
            'operation_type' => $operationType,
            'history' => $history
        ]);
    }
}
