<?php

namespace App\Actions\StockMovementSummary;

use App\Models\CollectionItem;
use App\Models\DeliveryItem;
use App\Models\LendingStockAdjustmentItem;
use App\Models\StockMovementSummary;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class HandleStockMovementSummary
{
    use AsAction;

    protected CollectionItem|DeliveryItem|LendingStockAdjustmentItem $documentItem;
    protected StockMovementSummary $stockMovementSummary;

    /**
     * Handle the action.
     *
     * @param  \App\Models\CollectionItem|\App\Models\DeliveryItem|\App\Models\LendingStockAdjustmentItem $documentItem
     * @param  bool $useOwnTransaction
     * @return void
     */
    public function handle(
        CollectionItem|DeliveryItem|LendingStockAdjustmentItem &$documentItem,
        bool $deleting = false,
        bool $useOwnTransaction = false
    ): void {
        $this->documentItem = $documentItem;

        if ($useOwnTransaction) {
            DB::beginTransaction();
        }

        $stockMovementSummary = $this->getStockMovementSummaryLine();
        $this->updateStockMovementSummaryLine($stockMovementSummary, $deleting);

        $previousStockMovementSummary = $this->getStockMovementSummaryLine(carbon($stockMovementSummary->movement_date));
        $this->updateNextStockMovementSummaryLines($previousStockMovementSummary, $deleting);

        if ($useOwnTransaction) {
            DB::commit();
        }
    }

    /**
     * Get the stock movement summary line.
     *
     * @param  \Carbon\Carbon $referenceDate
     * @return \App\Models\StockMovementSummary
     */
    protected function getStockMovementSummaryLine(?Carbon $referenceDate = null): StockMovementSummary
    {
        /** @var \App\Models\StockMovementSummary $stockMovementSummary */
        $stockMovementSummary = StockMovementSummary::query()
            ->where('customer_id', match (get_class($this->documentItem)) {
                CollectionItem::class => $this->documentItem->collection->customer_id,
                DeliveryItem::class => $this->documentItem->delivery->customer_id,
                LendingStockAdjustmentItem::class => $this->documentItem->lendingStockAdjustment->customer_id,
            })
            ->where('product_id', $this->documentItem->product_id)
            ->where('movement_date', match (get_class($this->documentItem)) {
                CollectionItem::class => carbon($referenceDate ?? $this->documentItem->collection->collected_at)->format('Y-m-d'),
                DeliveryItem::class => carbon($referenceDate ?? $this->documentItem->delivery->delivered_at)->format('Y-m-d'),
                LendingStockAdjustmentItem::class => carbon($referenceDate ?? $this->documentItem->lendingStockAdjustment->adjusted_at)->format('Y-m-d'),
            })
            ->first();

        if ($stockMovementSummary) {
            return $stockMovementSummary;
        }

        /** @var \App\Models\StockMovementSummary $previousStockMovementSummary */
        $previousStockMovementSummary = StockMovementSummary::query()
            ->where('customer_id', match (get_class($this->documentItem)) {
                CollectionItem::class => $this->documentItem->collection->customer_id,
                DeliveryItem::class => $this->documentItem->delivery->customer_id,
                LendingStockAdjustmentItem::class => $this->documentItem->lendingStockAdjustment->customer_id,
            })
            ->where('product_id', $this->documentItem->product_id)
            ->where('movement_date', '<', match (get_class($this->documentItem)) {
                CollectionItem::class => carbon($referenceDate ?? $this->documentItem->collection->collected_at)->format('Y-m-d'),
                DeliveryItem::class => carbon($referenceDate ?? $this->documentItem->delivery->delivered_at)->format('Y-m-d'),
                LendingStockAdjustmentItem::class => carbon($referenceDate ?? $this->documentItem->lendingStockAdjustment->adjusted_at)->format('Y-m-d'),
            })
            ->orderByDesc('movement_date')
            ->first();

        return StockMovementSummary::create([
            'customer_id' => match (get_class($this->documentItem)) {
                CollectionItem::class => $this->documentItem->collection->customer_id,
                DeliveryItem::class => $this->documentItem->delivery->customer_id,
                LendingStockAdjustmentItem::class => $this->documentItem->lendingStockAdjustment->customer_id,
            },
            'product_id' => $this->documentItem->product_id,
            'movement_date' => match (get_class($this->documentItem)) {
                CollectionItem::class => carbon($referenceDate ?? $this->documentItem->collection->collected_at)->format('Y-m-d'),
                DeliveryItem::class => carbon($referenceDate ?? $this->documentItem->delivery->delivered_at)->format('Y-m-d'),
                LendingStockAdjustmentItem::class => carbon($referenceDate ?? $this->documentItem->lendingStockAdjustment->adjusted_at)->format('Y-m-d'),
            },
            'previous_stock_quantity' => $previousStockMovementSummary?->stock_quantity ?? 0,
            'collected_quantity' => 0,
            'delivered_quantity' => 0,
            'out_of_movement_quantity' => $previousStockMovementSummary?->stock_quantity ?? 0,
            'stock_quantity' => $previousStockMovementSummary?->stock_quantity ?? 0,
        ]);
    }

    /**
     * Update the stock movement summary line.
     *
     * @param  \App\Models\StockMovementSummary $stockMovementSummary
     * @param  bool $deleting
     * @param  bool $updateMovementQuantity
     * @return void
     */
    protected function updateStockMovementSummaryLine(
        StockMovementSummary &$stockMovementSummary,
        bool $deleting = false,
        bool $updateMovementQuantity = true
    ): void {
        $updateData = match (get_class($this->documentItem)) {
            CollectionItem::class => $this->getCollectionItemUpdateData($stockMovementSummary, $deleting, $updateMovementQuantity),
            DeliveryItem::class => $this->getDeliveryItemUpdateData($stockMovementSummary, $deleting, $updateMovementQuantity),
            default => $this->getAdjustmentItemUpdateData($stockMovementSummary, $deleting, $updateMovementQuantity),
        };

        $stockMovementSummary->update(
            array_filter($updateData, function (mixed $value, mixed $key) use ($deleting): bool {
                if (
                    get_class($this->documentItem) === LendingStockAdjustmentItem::class
                    && $deleting
                    && $key === 'adjustment_quantity'
                ) {
                    return true;
                }

                return !is_null($value);
            }, ARRAY_FILTER_USE_BOTH)
        );
    }

    /**
     * Get the collection item update data.
     *
     * @param  \App\Models\StockMovementSummary $stockMovementSummary
     * @param  bool $deleting
     * @param  bool $updateMovementQuantity
     * @return array
     */
    protected function getCollectionItemUpdateData(
        StockMovementSummary &$stockMovementSummary,
        bool $deleting = false,
        bool $updateMovementQuantity = true
    ): array {
        if ($deleting) {
            return [
                'previous_stock_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->previous_stock_quantity
                    : $stockMovementSummary->previous_stock_quantity + $this->documentItem->quantity,
                'out_of_movement_quantity' => $stockMovementSummary->previous_stock_quantity - ($stockMovementSummary->collected_quantity - $this->documentItem->quantity),
                'collected_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->collected_quantity - $this->documentItem->quantity
                    : null,
                'stock_quantity' => $stockMovementSummary->stock_quantity + $this->documentItem->quantity,
            ];
        }

        return [
            'previous_stock_quantity' => $updateMovementQuantity
                ? $stockMovementSummary->previous_stock_quantity
                : $stockMovementSummary->previous_stock_quantity - $this->documentItem->quantity,
            'out_of_movement_quantity' => $stockMovementSummary->previous_stock_quantity - ($stockMovementSummary->collected_quantity + $this->documentItem->quantity),
            'collected_quantity' => $updateMovementQuantity
                ? $stockMovementSummary->collected_quantity + $this->documentItem->quantity
                : null,
            'stock_quantity' => $stockMovementSummary->stock_quantity - $this->documentItem->quantity,
        ];
    }

    /**
     * Get the delivery item update data.
     *
     * @param  \App\Models\StockMovementSummary $stockMovementSummary
     * @param  bool $deleting
     * @param  bool $updateMovementQuantity
     * @return array
     */
    protected function getDeliveryItemUpdateData(
        StockMovementSummary &$stockMovementSummary,
        bool $deleting = false,
        bool $updateMovementQuantity = true
    ): array {
        if ($deleting) {
            return [
                'previous_stock_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->previous_stock_quantity
                    : $stockMovementSummary->previous_stock_quantity - $this->documentItem->quantity,
                'delivered_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->delivered_quantity - $this->documentItem->quantity
                    : null,
                'stock_quantity' => $stockMovementSummary->stock_quantity - $this->documentItem->quantity,
            ];
        }

        return [
            'previous_stock_quantity' => $updateMovementQuantity
                ? $stockMovementSummary->previous_stock_quantity
                : $stockMovementSummary->previous_stock_quantity + $this->documentItem->quantity,
            'delivered_quantity' => $updateMovementQuantity
                ? $stockMovementSummary->delivered_quantity + $this->documentItem->quantity
                : null,
            'stock_quantity' => $stockMovementSummary->stock_quantity + $this->documentItem->quantity,
        ];
    }

    /**
     * Get the adjustment item update data.
     *
     * @param  \App\Models\StockMovementSummary $stockMovementSummary
     * @param  bool $deleting
     * @param  bool $updateMovementQuantity
     * @return array
     */
    protected function getAdjustmentItemUpdateData(
        StockMovementSummary &$stockMovementSummary,
        bool $deleting = false,
        bool $updateMovementQuantity = true
    ): array {
        if ($deleting) {
            if ($updateMovementQuantity) {
                return [
                    'previous_stock_quantity' => $updateMovementQuantity
                        ? $stockMovementSummary->previous_stock_quantity
                        : $stockMovementSummary->previous_stock_quantity - $this->documentItem->quantity,
                    'out_of_movement_quantity' => $stockMovementSummary->out_of_movement_quantity - $this->documentItem->adjustment_quantity,
                    'adjustment_quantity' => null,
                    'stock_quantity' => $stockMovementSummary->stock_quantity - $this->documentItem->adjustment_quantity,
                ];
            }

            return [
                'previous_stock_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->previous_stock_quantity
                    : $stockMovementSummary->previous_stock_quantity - $this->documentItem->adjustment_quantity,
                'out_of_movement_quantity' => $stockMovementSummary->out_of_movement_quantity - $this->documentItem->adjustment_quantity,
                'stock_quantity' => $stockMovementSummary->stock_quantity - $this->documentItem->adjustment_quantity,
            ];
        }

        if ($updateMovementQuantity) {
            return [
                'previous_stock_quantity' => $updateMovementQuantity
                    ? $stockMovementSummary->previous_stock_quantity
                    : $stockMovementSummary->previous_stock_quantity + $this->documentItem->adjustment_quantity,
                'out_of_movement_quantity' => $stockMovementSummary->out_of_movement_quantity + $this->documentItem->adjustment_quantity,
                'adjustment_quantity' => $stockMovementSummary->adjustment_quantity + $this->documentItem->adjustment_quantity,
                'stock_quantity' => $stockMovementSummary->stock_quantity + $this->documentItem->adjustment_quantity,
            ];
        }

        return [
            'previous_stock_quantity' => $updateMovementQuantity
                ? $stockMovementSummary->previous_stock_quantity
                : $stockMovementSummary->previous_stock_quantity + $this->documentItem->adjustment_quantity,
            'out_of_movement_quantity' => $stockMovementSummary->out_of_movement_quantity + $this->documentItem->adjustment_quantity,
            'stock_quantity' => $stockMovementSummary->stock_quantity + $this->documentItem->adjustment_quantity,
        ];
    }

    /**
     * Update the next stock movement summary lines.
     *
     * @param  \App\Models\StockMovementSummary $referenceStockMovementSummary
     * @param  bool $deleting
     * @return void
     */
    protected function updateNextStockMovementSummaryLines(
        StockMovementSummary $referenceStockMovementSummary,
        bool $deleting = false
    ): void {
        StockMovementSummary::query()
            ->where('customer_id', $referenceStockMovementSummary->customer_id)
            ->where('product_id', $referenceStockMovementSummary->product_id)
            ->where('movement_date', '>', carbon($referenceStockMovementSummary->movement_date)->format('Y-m-d'))
            ->get()
            ->each(function (StockMovementSummary $stockMovementSummary) use ($deleting): void {
                $this->updateStockMovementSummaryLine($stockMovementSummary, $deleting, false);
            });
    }
}
