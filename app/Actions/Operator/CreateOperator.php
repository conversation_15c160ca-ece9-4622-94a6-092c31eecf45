<?php

namespace App\Actions\Operator;

use App\Models\Operator;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class CreateOperator
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  array $data
     * @return \App\Models\Operator
     */
    public function handle(array $data): Operator
    {
        return DB::transaction(function () use ($data): Operator {
            $password = $data['password'] ?? Str::random(10);

            /** @var \App\Models\User $user */
            $user = User::create([
                'role_id' => $data['role_id'],
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($password),
            ]);

            $data = array_merge(
                collect($data)->only(['name', 'email', 'password', 'active', 'role_id'])->toArray(),
                ['permissions' => $this->definePermissions($data)]
            );

            $user->givePermissionTo(array_keys(array_filter($data['permissions'])));

            $data['user_id'] = $user->id;

            /** @var \App\Models\Operator $operator */
            $operator = Operator::create($data);

            return $operator;
        });
    }

    /**
     * Define the user's permissions.
     *
     * @param  array $data
     * @return array
     */
    protected function definePermissions(array $data): array
    {
        $permissions = [];
        $reflectionPermissions = Permission::getAvailablePermissions();

        foreach ($reflectionPermissions as $permission) {
            $permissions[$permission] = match ((int) $data['role_id']) {
                1 => true,
                3 => false,
                default => $data[$permission]
            };
        }

        return $permissions;
    }
}
