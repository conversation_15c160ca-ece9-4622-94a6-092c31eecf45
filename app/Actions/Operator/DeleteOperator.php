<?php

namespace App\Actions\Operator;

use App\Models\Operator;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteOperator
{
    use AsAction;

    /**
     * Handle the action;
     *
     * @param  \App\Models\Operator $operator
     * @return void
     */
    public function handle(Operator $operator): void
    {
        try {
            $operator->user->delete();
            $operator->delete();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
