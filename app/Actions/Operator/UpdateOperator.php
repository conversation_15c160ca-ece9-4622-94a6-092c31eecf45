<?php

namespace App\Actions\Operator;

use App\Models\Operator;
use App\Models\Permission;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateOperator
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Operator $operator
     * @param  array $data
     * @return \App\Models\Operator
     */
    public function handle(Operator $operator, array $data): Operator
    {
        return DB::transaction(function () use ($operator, $data): Operator {
            $operator->update($data);

            $data = array_merge(
                collect($data)->only(['name', 'email', 'password', 'active', 'role_id'])->toArray(),
                ['permissions' => $this->definePermissions($data)]
            );

            $operator->user->update($data);
            $operator->user->syncPermissions(array_keys(array_filter($data['permissions'])));

            return $operator;
        });
    }

    /**
     * Define the user's permissions.
     *
     * @param  array $data
     * @return array
     */
    protected function definePermissions(array $data): array
    {
        $permissions = [];
        $reflectionPermissions = Permission::getAvailablePermissions();

        foreach ($reflectionPermissions as $permission) {
            $permissions[$permission] = match ((int) $data['role_id']) {
                1 => true,
                3 => false,
                default => $data[$permission]
            };
        }

        return $permissions;
    }
}
