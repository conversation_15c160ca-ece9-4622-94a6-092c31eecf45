<?php

namespace App\Actions\ReceivableSettlement;

use App\Http\Integrations\Omie\DataTransferObjects\OmieRegisterReceivableSettlementDto;
use App\Http\Integrations\Omie\Services\OmieReceivableService;
use App\Models\BankAccountWallet;
use App\Models\Receivable;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateReceivableSettlement
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  \App\Models\Receivable $receivable
     * @param  float $amount
     * @param  \Carbon\Carbon $settledAt
     * @param  int $paymentMethodId
     * @param  \App\Models\BankAccountWallet $bankAccountWallet
     * @param  string|null $additionalInfo
     * @param  bool $integrate
     * @return \App\Models\Receivable
     */
    public function handle(
        Receivable $receivable,
        float $amount,
        Carbon $settledAt,
        int $paymentMethodId,
        BankAccountWallet $bankAccountWallet,
        ?string $additionalInfo = null,
        bool $integrate = true
    ): Receivable {
        if ($receivable->settled_at || $receivable->cancelled_at) {
            return $receivable;
        }

        try {
            $receivable->receivableSettlements()->create([
                'payment_method_id' => $paymentMethodId,
                'original_amount' => $amount,
                'updated_amount' => $amount,
                'additional_info' => $additionalInfo,
                'settled_at' => $settledAt
            ]);

            if ($integrate) {
                $omieRegisterReceivableSettlementDto = new OmieRegisterReceivableSettlementDto(
                    codigo_lancamento: $receivable->omie_id,
                    codigo_conta_corrente: $receivable->customer->defaultBankAccountWallet->omie_id,
                    valor: $receivable->updated_amount,
                    data: $settledAt->format('d/m/Y'),
                );

                OmieReceivableService::make()->registerSettlement($receivable, $omieRegisterReceivableSettlementDto);
            }

            return $receivable;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
