<?php

namespace App\Actions\SmtpConfiguration;

use App\Models\SmtpConfiguration;
use Lorisleiva\Actions\Concerns\AsAction;

class GetActiveSmtpConfiguration
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @return \App\Models\SmtpConfiguration|null
     */
    public function handle(): ?SmtpConfiguration
    {
        return SmtpConfiguration::query()
            ->where('active', true)
            ->first();
    }
}
