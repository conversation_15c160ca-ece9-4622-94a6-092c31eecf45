<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\ReceivablesTableExport;
use App\Models\IncomeItem;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateReceivablesTable extends BaseReport implements Reportable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->currentFormat = $this->parameters[1];

        $this->reportName = 'receivables_table';
        $this->moduleName = 'general';
        $this->excelFileName = 'tabela de faturas';
        $this->reportExportClassName = ReceivablesTableExport::class;
        $this->notificationSubjectReportName = 'tabela de faturas';
        $this->notificationBodyReportEntity = 'tabela de faturas';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'logoSrc' => p4m_tenant()->getLogoSrc(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return IncomeItem::query()
            ->with(['income', 'income.receivables', 'product'])
            ->whereHas('income.receivables', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->whereHas('product', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->get()
            ->map(fn (IncomeItem $incomeItem): array => [
                'id' => $incomeItem->income->id,
                'customer_id' => $incomeItem->income->customer_id,
                'document_number' => $incomeItem->income->document_number,
                'amount' => $incomeItem->income->amount,
                'additional_info' => $incomeItem->income->additional_info,
                'issued_at' => $incomeItem->income->issued_at,
                'created_at' => $incomeItem->income->created_at,
                'updated_at' => $incomeItem->income->updated_at,
                'product_id' => $incomeItem->product->id,
                'product_subcategory_id' => $incomeItem->product->subcategory_id,
                'product_code' => $incomeItem->product->code,
                'product_name' => $incomeItem->product->name,
                'product_description' => $incomeItem->product->description,
                'product_gross_weight' => $incomeItem->product->gross_weight,
                'product_net_weight' => $incomeItem->product->net_weight,
                'product_default_price' => $incomeItem->product->default_price,
                'product_created_at' => $incomeItem->product->created_at,
                'product_updated_at' => $incomeItem->product->updated_at,
                'receivable_id' => $incomeItem->income->receivables->first()->id,
                'receivable_customer_id' => $incomeItem->income->receivables->first()->customer_id,
                'receivable_document_id' => $incomeItem->income->receivables->first()->document_id,
                'receivable_document_type' => $incomeItem->income->receivables->first()->document_type,
                'receivable_sequence' => $incomeItem->income->receivables->first()->sequence,
                'receivable_original_amount' => $incomeItem->income->receivables->first()->original_amount,
                'receivable_pis_wht_amount' => $incomeItem->income->receivables->first()->pis_wht_amount,
                'receivable_cofins_wht_amount' => $incomeItem->income->receivables->first()->cofins_wht_amount,
                'receivable_csll_wht_amount' => $incomeItem->income->receivables->first()->csll_wht_amount,
                'receivable_irrf_wht_amount' => $incomeItem->income->receivables->first()->irrf_wht_amount,
                'receivable_inss_wht_amount' => $incomeItem->income->receivables->first()->inss_wht_amount,
                'receivable_iss_wht_amount' => $incomeItem->income->receivables->first()->iss_wht_amount,
                'receivable_addition_amount' => $incomeItem->income->receivables->first()->addition_amount,
                'receivable_discount_amount' => $incomeItem->income->receivables->first()->discount_amount,
                'receivable_updated_amount' => $incomeItem->income->receivables->first()->updated_amount,
                'receivable_status' => $incomeItem->income->receivables->first()->status,
                'receivable_email_sent' => $incomeItem->income->receivables->first()->email_sent,
                'receivable_collection_count' => $incomeItem->income->receivables->first()->collection_count,
                'receivable_issued_at' => $incomeItem->income->receivables->first()->issued_at,
                'receivable_expires_at' => $incomeItem->income->receivables->first()->expires_at,
                'receivable_settled_at' => $incomeItem->income->receivables->first()->settled_at,
                'receivable_created_at' => $incomeItem->income->receivables->first()->created_at,
                'receivable_updated_at' => $incomeItem->income->receivables->first()->updated_at,
            ])
            ->toArray();
    }
}
