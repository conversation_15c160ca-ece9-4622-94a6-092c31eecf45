<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\DeliveriesTableExport;
use App\Models\DeliveryItem;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateDeliveriesTable extends BaseReport implements Reportable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->currentFormat = $this->parameters[1];

        $this->reportName = 'deliveries_table';
        $this->moduleName = 'general';
        $this->excelFileName = 'tabela de entregas';
        $this->reportExportClassName = DeliveriesTableExport::class;
        $this->notificationSubjectReportName = 'tabela de entregas';
        $this->notificationBodyReportEntity = 'tabela de entregas';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'logoSrc' => p4m_tenant()->getLogoSrc(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return DeliveryItem::query()
            ->with(['delivery', 'product'])
            ->whereHas('delivery', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->whereHas('product', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->get()
            ->map(fn (DeliveryItem $deliveryItem): array => [
                'id' => $deliveryItem->delivery->id,
                'delivery_id' => $deliveryItem->delivery->delivery_id,
                'product_id' => $deliveryItem->delivery->product_id,
                'quantity' => $deliveryItem->delivery->quantity,
                'created_at' => $deliveryItem->delivery->created_at,
                'updated_at' => $deliveryItem->delivery->updated_at,
                'delivery_item_id' => $deliveryItem->id,
                'delivery_item_delivery_id' => $deliveryItem->delivery_id,
                'delivery_item_product_id' => $deliveryItem->product_id,
                'delivery_item_quantity' => $deliveryItem->quantity,
                'delivery_item_created_at' => $deliveryItem->created_at,
                'delivery_item_updated_at' => $deliveryItem->updated_at,
                'product_id' => $deliveryItem->product->id,
                'product_subcategory_id' => $deliveryItem->product->subcategory_id,
                'product_code' => $deliveryItem->product->code,
                'product_name' => $deliveryItem->product->name,
                'product_description' => $deliveryItem->product->description,
                'product_gross_weight' => $deliveryItem->product->gross_weight,
                'product_net_weight' => $deliveryItem->product->net_weight,
                'product_default_price' => $deliveryItem->product->default_price,
                'product_created_at' => $deliveryItem->product->created_at,
                'product_updated_at' => $deliveryItem->product->updated_at,
            ])
            ->toArray();
    }
}
