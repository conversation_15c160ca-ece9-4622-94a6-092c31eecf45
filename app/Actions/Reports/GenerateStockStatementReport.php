<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\StockStatementReportExport;
use App\Models\Customer;
use App\Models\StockLocationProduct;
use App\Models\StockMovementSummary;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateStockStatementReport extends BaseReport implements Reportable
{
    use AsAction;

    protected int $customerId;
    protected Carbon $dateFrom;
    protected Carbon $dateTo;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->customerId = $this->parameters[0];
        $this->dateFrom = carbon($this->parameters[1]);
        $this->dateTo = carbon($this->parameters[2]);
        $this->currentFormat = $this->parameters[3];

        $this->reportName = 'stock_statement';
        $this->moduleName = 'supply_chain';
        $this->excelFileName = 'extrato_de_estoque';
        $this->reportExportClassName = StockStatementReportExport::class;
        $this->notificationSubjectReportName = 'extrato de estoque';
        $this->notificationBodyReportEntity = 'extrato de estoque';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'customer' => Customer::find($this->customerId),
            'dateFrom' => format_date($this->dateFrom),
            'dateTo' => format_date($this->dateTo),
            'logoSrc' => p4m_tenant()->getLogoSrc(),
            'customerStockLocationProducts' => StockLocationProduct::query()
                ->whereRelation('stockLocation', 'customer_id', $this->customerId)
                ->get()
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return StockMovementSummary::query()
            ->with('product:id,name')
            ->select([
                'id',
                'product_id',
                'movement_date',
                'previous_stock_quantity',
                'collected_quantity',
                'delivered_quantity',
                'out_of_movement_quantity',
                'adjustment_quantity',
                'stock_quantity',
            ])
            ->where('customer_id', $this->customerId)
            ->where('movement_date', '>=', $this->dateFrom->format('Y-m-d'))
            ->where('movement_date', '<=', $this->dateTo->format('Y-m-d'))
            ->orderBy('movement_date')
            ->get()
            ->groupBy('movement_date')
            ->toArray();
    }
}
