<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\CollectionDeviationReportExport;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateCollectionDeviationReport extends BaseReport implements Reportable
{
    use AsAction;

    public ?string $collectedAt;
    public int $minUnitDifference;
    public float $minDifferencePercentage;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->collectedAt = $this->parameters[0];
        $this->minUnitDifference = $this->parameters[1];
        $this->minDifferencePercentage = unmask_percentage($this->parameters[2]);
        $this->currentFormat = $this->parameters[3];

        $this->reportName = 'collection_deviation';
        $this->moduleName = 'supply_chain';
        $this->excelFileName = 'desvio_de_coletas';
        $this->reportExportClassName = CollectionDeviationReportExport::class;
        $this->notificationSubjectReportName = 'desvio de coletas';
        $this->notificationBodyReportEntity = 'desvio de coletas';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'collectedAt' => format_date($this->collectedAt),
            'minUnitDifference' => $this->minUnitDifference,
            'minDifferencePercentage' => $this->minDifferencePercentage,
            'logoSrc' => p4m_tenant()->getLogoSrc(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return DB::select("
            -- Relatório de Desvios nas Coletas
            WITH customer_stats AS (
                -- Obtenção das estatísticas dos clientes (média, desvio padrão, máximo e mínimo) por produto e dia da semana
                SELECT
                    cbss.customer_id,
                    cbss.product_id,
                    cbss.weekday,
                    MAX(CASE WHEN cbss.stats_type = 'basic-mean' THEN cbss.amount END) AS mean,
                    MAX(CASE WHEN cbss.stats_type = 'standard-deviation' THEN cbss.amount END) AS deviation,
                    MAX(CASE WHEN cbss.stats_type = 'max' THEN cbss.amount END) AS max,
                    MAX(CASE WHEN cbss.stats_type = 'min' THEN cbss.amount END) AS min
                FROM
                    customer_basic_stock_stats cbss
                GROUP BY
                    cbss.customer_id,
                    cbss.product_id,
                    cbss.weekday
            ),
            collection_data AS (
                -- Seleção dos dados de coletas específicas, incluindo filtragem por data e exclusão de registros deletados
                SELECT
                    c.customer_id,
                    c.id AS collection_id,
                    ci.product_id,
                    ci.quantity,
                    WEEKDAY(c.collected_at) + 1 AS weekday,
                    c.collected_at AS collection_date
                FROM
                    collections c
                    JOIN collection_items ci ON c.id = ci.collection_id
                WHERE
                    c.collected_at = '$this->collectedAt'  -- Data específica da coleta (permite personalização)
                    AND c.deleted_at IS NULL
                    AND ci.deleted_at IS NULL
            )
            SELECT
                cd.customer_id AS ID,
                cust.trading_name AS Cliente,
                cd.collection_id AS Coleta_ID,
                prod.name AS Item,
                -- Determinação do status da coleta (Abaixo ou Acima)
                CASE
                    WHEN cd.quantity < cs.mean - cs.deviation THEN 'Abaixo'
                    WHEN cd.quantity > cs.mean + cs.deviation THEN 'Acima'
                END AS Status,
                cd.quantity AS Coletado,
                -- Cálculo do valor esperado baseado na média e desvio padrão
                CASE
                    WHEN cd.quantity < cs.mean - cs.deviation THEN cs.mean - cs.deviation
                    WHEN cd.quantity > cs.mean + cs.deviation THEN cs.mean + cs.deviation
                END AS Esperado,
                -- Seleção do valor máximo ou mínimo mais próximo da quantidade coletada
                CASE
                    WHEN ABS(cd.quantity - cs.max) < ABS(cd.quantity - cs.min) THEN cs.max
                    ELSE cs.min
                END AS 'MaxMin',
                -- Cálculo do desvio percentual em relação ao valor esperado
                CASE
                    WHEN cd.quantity < cs.mean - cs.deviation THEN (cs.mean - cs.deviation) / cd.quantity - 1
                                        ELSE cd.quantity / (cs.mean + cs.deviation) - 1
                END * 100 AS Desvio
            FROM
                collection_data cd
                JOIN customer_stats cs ON cd.customer_id = cs.customer_id
                    AND cd.product_id = cs.product_id
                    AND cd.weekday = cs.weekday
                JOIN customers cust ON cd.customer_id = cust.id
                JOIN products prod ON cd.product_id = prod.id
            WHERE
                -- Filtragem para incluir apenas registros com desvios significativos
                (cd.quantity < cs.mean - cs.deviation OR cd.quantity > cs.mean + cs.deviation)
                AND ABS(cd.quantity - CASE
                                        WHEN cd.quantity < cs.mean - cs.deviation THEN cs.mean - cs.deviation
                                                            ELSE cs.mean + cs.deviation
                                    END) > $this->minUnitDifference  -- Limite mínimo de diferença em unidades (permite personalização)
            AND CASE
                    WHEN cd.quantity < cs.mean - cs.deviation THEN (cs.mean - cs.deviation) / cd.quantity - 1
                                            ELSE cd.quantity / (cs.mean + cs.deviation) - 1
                END * 100 > $this->minDifferencePercentage -- Limite mínimo de diferença percentual (permite personalização)
            ORDER BY
                Status, Desvio DESC;
        ");
    }
}
