<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\CustomersTableExport;
use App\Models\Customer;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateCustomersTable extends BaseReport implements Reportable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->currentFormat = $this->parameters[1];

        $this->reportName = 'customers_table';
        $this->moduleName = 'general';
        $this->excelFileName = 'tabela de clientes';
        $this->reportExportClassName = CustomersTableExport::class;
        $this->notificationSubjectReportName = 'tabela de clientes';
        $this->notificationBodyReportEntity = 'tabela de clientes';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'logoSrc' => p4m_tenant()->getLogoSrc(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return Customer::query()
            ->get()
            ->map(fn (Customer $customer): array => [
                'id' => $customer->id,
                'salesman_id' => $customer->salesman_id,
                'customer_group_id' => $customer->customer_group_id,
                'payment_recovery_setting_id' => $customer->payment_recovery_setting_id,
                'name' => $customer->name,
                'trading_name' => $customer->trading_name,
                'tax_id_number' => $customer->tax_id_number,
                'exempt_from_state_registration' => $customer->exempt_from_state_registration,
                'state_registration' => $customer->state_registration,
                'city_registration' => $customer->city_registration,
                'email' => $customer->email,
                'billing_email' => $customer->billing_email,
                'billing_phone' => $customer->billing_phone,
                'operation_email' => $customer->operation_email,
                'phone_1' => $customer->phone_1,
                'in_charge_person_1' => $customer->in_charge_person_1,
                'phone_2' => $customer->phone_2,
                'in_charge_person_2' => $customer->in_charge_person_2,
                'address_zipcode' => $customer->address_zipcode,
                'address_address' => $customer->address_address,
                'address_number' => $customer->address_number,
                'address_additional_info' => $customer->address_additional_info,
                'address_district' => $customer->address_district,
                'address_city_id' => $customer->address_city_id,
                'address_state_id' => $customer->address_state_id,
                'latitude' => $customer->latitude,
                'longitude' => $customer->longitude,
                'preferred_charging_method' => $customer->preferred_charging_method,
                'previous_collection_count' => $customer->previous_collection_count,
                'minimum_billing_amount' => $customer->minimum_billing_amount,
                'default_due_day' => $customer->default_due_day,
                'default_billing_period' => $customer->default_billing_period,
                'default_tax_condition' => $customer->default_tax_condition,
                'default_delivery_model' => $customer->default_delivery_model,
                'stock_safety_percentage' => $customer->stock_safety_percentage,
                'active' => $customer->active,
                'created_at' => $customer->created_at,
                'updated_at' => $customer->updated_at,
            ])
            ->toArray();
    }
}
