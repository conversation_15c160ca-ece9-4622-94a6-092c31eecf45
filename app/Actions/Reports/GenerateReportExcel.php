<?php

namespace App\Actions\Reports;

use App\Core\Reports\ReportGeneratedNotification;
use App\Models\User;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Maatwebsite\Excel\Facades\Excel;

class GenerateReportExcel
{
    use AsAction;

    /**
     * Configure the job instance.
     *
     * @param  \Lorisleiva\Actions\Decorators\JobDecorator $job
     * @return void
     */
    public function configureJob(JobDecorator $job)
    {
        $job->onQueue(
            config('queue.default_queue_names.reports')
        );
    }

    /**
     * Handle the action.
     *
     * @param  int $userId
     * @param  array $reportData
     * @param  string $moduleName
     * @param  string $filename
     * @param  string $reportExportClassName
     * @param  string $notificationSubjectReportName
     * @param  string $notificationBodyReportEntity
     * @return void
     */
    public function handle(
        int $userId,
        array $reportData,
        string $moduleName,
        string $filename,
        string $reportExportClassName,
        string $notificationSubjectReportName,
        string $notificationBodyReportEntity
    ): void {
        $reportFullPath = "reports/$moduleName/" . date('YmdHis') . "_{$filename}.xlsx";

        Excel::store(new $reportExportClassName($reportData), $reportFullPath);

        $notification = new ReportGeneratedNotification(
            $notificationSubjectReportName,
            $notificationBodyReportEntity,
            $reportFullPath,
            $filename
        );

        User::query()
            ->find($userId)
            ->notify($notification);
    }
}
