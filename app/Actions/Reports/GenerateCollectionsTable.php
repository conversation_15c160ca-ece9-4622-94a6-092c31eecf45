<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Reports\BaseReport;
use App\Exports\Reports\CollectionsTableExport;
use App\Models\CollectionItem;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateCollectionsTable extends BaseReport implements Reportable
{
    use AsAction;

    /**
     * Handle the action.
     *
     * @param  string $token
     * @return mixed
     */
    public function handle(string $token): mixed
    {
        $this->decodeReportParameters($token);

        $this->currentFormat = $this->parameters[1];

        $this->reportName = 'collections_table';
        $this->moduleName = 'general';
        $this->excelFileName = 'tabela de coletas';
        $this->reportExportClassName = CollectionsTableExport::class;
        $this->notificationSubjectReportName = 'tabela de coletas';
        $this->notificationBodyReportEntity = 'tabela de coletas';

        try {
            $this->reportData = $this->isScreenable()
                ? $this->buildScreenableData()
                : $this->buildExcelData();
        } catch (Throwable $th) {
            throw_error($th);
        }

        return $this->getReport();
    }

    /**
     * @inheritDoc
     */
    public function buildScreenableData(mixed ...$params): array
    {
        $data = $this->buildReportData();

        return [
            'data' => $data,
            'logoSrc' => p4m_tenant()->getLogoSrc(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        return $this->buildReportData();
    }

    /**
     * Build the report data.
     *
     * @return array
     */
    public function buildReportData(): array
    {
        return CollectionItem::query()
            ->with(['collection', 'product'])
            ->whereHas('collection', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->whereHas('product', fn (Builder $query): Builder => $query->whereNull('deleted_at'))
            ->get()
            ->map(fn (CollectionItem $collectionItem): array => [
                'id' => $collectionItem->collection->id,
                'customer_id' => $collectionItem->collection->customer_id,
                'contract_id' => $collectionItem->collection->contract_id,
                'additional_info' => $collectionItem->collection->additional_info,
                'email_sent' => $collectionItem->collection->email_sent,
                'collected_at' => $collectionItem->collection->collected_at,
                'created_at' => $collectionItem->collection->created_at,
                'updated_at' => $collectionItem->collection->updated_at,
                'collection_item_id' => $collectionItem->id,
                'collection_item_collection_id' => $collectionItem->collection_id,
                'collection_item_product_id' => $collectionItem->product_id,
                'collection_item_quantity' => $collectionItem->quantity,
                'collection_item_created_at' => $collectionItem->created_at,
                'collection_item_updated_at' => $collectionItem->updated_at,
                'product_id' => $collectionItem->product->id,
                'product_subcategory_id' => $collectionItem->product->subcategory_id,
                'product_code' => $collectionItem->product->code,
                'product_name' => $collectionItem->product->name,
                'product_description' => $collectionItem->product->description,
                'product_gross_weight' => $collectionItem->product->gross_weight,
                'product_net_weight' => $collectionItem->product->net_weight,
                'product_default_price' => $collectionItem->product->default_price,
                'product_created_at' => $collectionItem->product->created_at,
                'product_updated_at' => $collectionItem->product->updated_at,
            ])
            ->toArray();
    }
}
