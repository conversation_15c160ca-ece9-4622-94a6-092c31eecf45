<?php

namespace App\Integrations\Api\Receita\Services;

use App\Integrations\Api\Receita\Core\ReceitaApiClient;

class ReceitaService
{
    protected ReceitaApiClient $client;

    /**
     * Create a new instance.
     */
    public function __construct()
    {
        $this->client = new ReceitaApiClient();
    }

    /**
     * Get the company details.
     *
     * @param  string $taxIdNumber
     * @return mixed
     */
    public function getCompanyDetails(string $taxIdNumber): mixed
    {
        return $this->client->get(get_numbers($taxIdNumber));
    }
}
