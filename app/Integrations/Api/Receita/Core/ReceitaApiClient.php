<?php

namespace App\Integrations\Api\Receita\Core;

use App\Integrations\Core\BaseApiClient;

class ReceitaApiClient extends BaseApiClient
{
    /**
     * Create a new instance.
     */
    public function __construct()
    {
        parent::__construct(config('receita.api.url'));
    }

    /**
     * Execute a GET request in the server.
     *
     * @param  string $path
     * @return mixed
     */
    public function get(string $path): mixed
    {
        return json_decode(
            $this->client
                ->get($path)
                ->getBody()
                ->getContents()
        );
    }
}
