<?php

namespace App\Integrations\Api\ViaCep\Core;

use App\Integrations\Core\BaseApiClient;

class ViaCepClient extends BaseApiClient
{
    /**
     * Create a new instance.
     */
    public function __construct()
    {
        parent::__construct(config('via_cep.api.url'));
    }

    /**
     * Execute a GET request in the server.
     *
     * @param  string $zipcode
     * @return mixed
     */
    public function get(string $path): mixed
    {
        return json_decode(
            $this->client
                ->get($path)
                ->getBody()
                ->getContents()
        );
    }
}
