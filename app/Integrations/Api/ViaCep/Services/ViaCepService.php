<?php

namespace App\Integrations\Api\ViaCep\Services;

use App\Integrations\Api\ViaCep\Core\ViaCepClient;

class ViaCepService
{
    protected ViaCepClient $client;

    /**
     * Create a new instance.
     */
    public function __construct()
    {
        $this->client = new ViaCepClient();
    }

    /**
     * Get the full address based on a zipcode.
     *
     * @param  string $zipcode
     * @return mixed
     */
    public function getFullAddress(string $zipcode): mixed
    {
        $zipcode = get_numbers($zipcode);

        return $this->client->get("$zipcode/json");
    }
}
