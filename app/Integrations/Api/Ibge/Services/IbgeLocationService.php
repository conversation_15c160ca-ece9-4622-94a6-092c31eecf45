<?php

namespace App\Integrations\Api\Ibge\Services;

use App\Integrations\Api\Ibge\Core\IbgeApiV1Client;

class IbgeLocationService
{
    protected IbgeApiV1Client $ibgeApiV1Client;

    /**
     * Create a new instance.
     */
    public function __construct()
    {
        $this->ibgeApiV1Client = new IbgeApiV1Client();
    }

    /**
     * Get all regions from IBGE.
     *
     * @return mixed
     */
    public function getRegions(): mixed
    {
        return $this->ibgeApiV1Client->get('regioes');
    }

    /**
     * Get all states from IBGE.
     *
     * @return mixed
     */
    public function getStates(): mixed
    {
        return $this->ibgeApiV1Client->get('estados');
    }

    /**
     * Get all cities from IBGE.
     *
     * @return mixed
     */
    public function getCities(): mixed
    {
        return $this->ibgeApiV1Client->get('municipios');
    }
}
