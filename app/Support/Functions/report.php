<?php

use App\Core\Reports\ReportBuilder;

/**
 * Create a new report builder instance.
 *
 * @param  string|null $format
 * @param  string|null $reportName
 * @param  array|null $reportData
 * @param  string|null $moduleName
 * @param  string|null $excelFileName
 * @param  string|null $reportExportClassName
 * @param  string|null $notificationClassName
 * @param  string $pdfReportPaper
 * @param  string $pdfReportOrientation
 * @return \App\Core\Reports\ReportBuilder
 */
function app_report(
    ?string $format = null,
    ?string $reportName = null,
    ?array $reportData = null,
    ?string $moduleName = null,
    ?string $excelFileName = null,
    ?string $reportExportClassName = null,
    ?string $notificationClassName = null,
    string $pdfReportPaper = 'a4',
    string $pdfReportOrientation = 'landscape'
): ReportBuilder {
    return new ReportBuilder(
        $format,
        $reportName,
        $reportData,
        $moduleName,
        $excelFileName,
        $reportExportClassName,
        $notificationClassName,
        $pdfReportPaper,
        $pdfReportOrientation
    );
}
