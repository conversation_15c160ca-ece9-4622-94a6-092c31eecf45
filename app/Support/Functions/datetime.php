<?php

/**
 * Short helper for Carbon instances.
 *
 * @param  mixed $value
 * @return \Carbon\Carbon|null
 */
function carbon(mixed $value)
{
    return !is_null($value)
        ? \Carbon\Carbon::parse($value)
        : null;
}

/**
 * Format a given date to a given format.
 * (Default is d/m/Y - example: 01/01/1980).
 *
 * @param  mixed $value
 * @return string
 */
function format_date(mixed $value, string $format = 'd/m/Y'): string
{
    return !is_null($value)
        ? carbon($value)->format($format)
        : '';
}

/**
 * Format a given date/time to a given format.
 * (Default is d/m/Y H:i:s - example: 01/01/1980 12:00:00).
 *
 * @param  mixed $value
 * @param  string $timezone
 * @param  string $format
 * @return string
 */
function format_datetime(mixed $value, string $timezone = '-3:00', string $format = 'd/m/Y H:i:s'): string
{
    return !is_null($value)
        ? carbon($value)->setTimezone($timezone)->format($format)
        : '';
}

/**
 * Get the timezoned "issued at" date/time based on the current tenant's configuration.
 *
 * @return string
 */
function get_timezoned_issued_at(): string
{
    return format_datetime(now(), tenant('timezone') ?? '-3:00');
}

function get_br_weekday(mixed $value): string
{
    if (!$value) {
        return '';
    }

    return match (carbon($value)->dayOfWeek) {
        0 => 'Domingo',
        1 => 'Segunda-feira',
        2 => 'Terça-feira',
        3 => 'Quarta-feira',
        4 => 'Quinta-feira',
        5 => 'Sexta-feira',
        6 => 'Sábado',
        default => '',
    };
}
