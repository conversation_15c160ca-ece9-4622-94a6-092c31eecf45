<?php

/*
|--------------------------------------------------------------------------
| Error helpers
|--------------------------------------------------------------------------
|
*/

if (!function_exists('error')) {
    /**
     * Write some error to the log.
     *
     * @param  string $message
     * @param  array<string, string> $context
     * @return void
     */
    function error(string $message, array $context = [])
    {
        app('log')->error($message, $context);
    }
}

/**
 * Log and throw a specific (or generic, if specific not provided) unknown error.
 *
 * @param  \Throwable $th
 * @param  string|null $message
 * @param  bool $log
 * @return void
 */
function throw_error(Throwable $th, ?string $message = null, bool $log = true): void
{
    if ($log) {
        error($th);
    }

    throw new Exception($message ?? __('general.unknown_error'));
}

/**
 * Log the contents structure.
 *
 * @param  string $className
 * @param  mixed $logContents
 * @return void
 */
function structure_log(string $className, mixed $logContents): void
{
    error('[SL] ' . $className . "\n" . $logContents);
}
