<?php

use App\Enums\RoleEnum;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;

/**
 * Notify a default success message.
 *
 * @param  string $body
 * @param  bool $persistent
 * @return Notification|null
 */
function success_notification(string $body, bool $persistent = false): ?Notification
{
    $notification = Notification::make()
        ->title('Sucesso!')
        ->body($body)
        ->success();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

/**
 * Notify a default error message.
 *
 * @param  string|null $body
 * @param  bool $persistent
 * @return Notification|null
 */
function error_notification(?string $body = null, bool $persistent = true): ?Notification
{
    $notification = Notification::make()
        ->title('Ops!')
        ->body($body ?? __('core.errors.general'))
        ->danger();

    if ($persistent) {
        $notification->persistent();
    } else {
        $notification->seconds(5);
    }

    return $notification;
}

/**
 * Create a database notification.
 *
 * @param  int $userId
 * @param  string|null $body
 * @param  bool $onlyCreationUser
 * @return void
 */
function database_notification(?int $userId = null, ?string $body = null, bool $onlyCreationUser = false): void
{
    User::query()
        ->when($userId, fn (Builder $query): Builder => $query->where('id', $userId))
        ->when(!$onlyCreationUser, function (Builder $query): Builder {
            return $query->orWhere(fn (Builder $query): Builder => $query->role(RoleEnum::Administrator->value));
        })
        ->get()
        ->each(function (User $user) use ($body): void {
            $notification = error_notification($body)->toDatabase();
            $user->notify($notification);
        });
}

function success_database_notification(?int $userId = null, ?string $body = null, bool $onlyCreationUser = false): void
{
    User::query()
        ->when($userId, fn (Builder $query): Builder => $query->where('id', $userId))
        ->when(!$onlyCreationUser, function (Builder $query): Builder {
            return $query->orWhere(fn (Builder $query): Builder => $query->role(RoleEnum::Administrator->value));
        })
        ->get()
        ->each(function (User $user) use ($body): void {
            $notification = success_notification($body)->toDatabase();
            $user->notify($notification);
        });
}
