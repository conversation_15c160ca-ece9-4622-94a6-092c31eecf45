<?php

namespace App\Support\Helpers;

class P4MTenant
{
    /**
     * Get the tenant's company name.
     *
     * @return string
     */
    public function getCompanyName(): string
    {
        return tenant('company_name') ?? '';
    }

    /**
     * Get the tenant's company tax ID number.
     *
     * @return string
     */
    public function getCompanyTaxIdNumber(): string
    {
        return !is_null(tenant('company_tax_id_number'))
            ? mask_cnpj(tenant('company_tax_id_number'))
            : '';
    }

    /**
     * Get the tenant's company' city registration number.
     *
     * @return string
     */
    public function getCompanyCityRegistrationNo(): string
    {
        return tenant('company_city_registration_no');
    }

    /**
     * Get the tenant's main website url.
     *
     * @return string
     */
    public function getLogoHref(): string
    {
        return tenant('website_url');
    }

    /**
     * Get the tenant's logo.
     *
     * @return string
     */
    public static function getLogo(): string
    {
        return (new static())->getLogoSrc();
    }

    /**
     * Get the logo path used for the img "src" attribute.
     *
     * @return string
     */
    public function getLogoSrc(): string
    {
        return config('digital_ocean.spaces.endpoint') . tenant('id') . '/assets/img/logo-' . tenant('id') . '.png';
    }
}
