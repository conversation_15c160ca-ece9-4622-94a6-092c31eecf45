<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Customer;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class TopCustomersTable extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $heading = 'Top 10 Clientes por Faturamento';
    protected int | string | array $columnSpan = 12;

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('trading_name')
                    ->label('Nome Fantasia')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('total_amount')
                    ->label('Valor Total')
                    ->money('BRL')
                    ->sortable()
                    ->alignEnd(),
                TextColumn::make('receivables_count')
                    ->label('Qtd. Faturas')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),
            ])
            ->defaultSort('total_amount', 'desc')
            ->paginated(false);
    }

    protected function getTableQuery(): Builder
    {
        $startDate = session('48040ef7f2542b39b9ba9a72983b0d88_filters')['start_date'] ?? now()->startOfMonth()->format('Y-m-d');
        $endDate = session('48040ef7f2542b39b9ba9a72983b0d88_filters')['end_date'] ?? now()->endOfMonth()->format('Y-m-d');

        return Customer::query()
            ->select([
                'customers.id',
                'customers.name',
                'customers.trading_name',
                DB::raw('COALESCE(SUM(receivables.updated_amount), 0) as total_amount'),
                DB::raw('COUNT(receivables.id) as receivables_count')
            ])
            ->leftJoin('receivables', function ($join) use ($startDate, $endDate) {
                $join->on('customers.id', '=', 'receivables.customer_id')
                     ->whereBetween('receivables.issued_at', [$startDate, $endDate])
                     ->whereNull('receivables.deleted_at');
            })
            ->where('customers.active', true)
            ->whereNull('customers.deleted_at')
            ->groupBy('customers.id', 'customers.name', 'customers.trading_name')
            ->having('total_amount', '>', 0)
            ->orderByDesc('total_amount')
            ->limit(10);
    }
}
