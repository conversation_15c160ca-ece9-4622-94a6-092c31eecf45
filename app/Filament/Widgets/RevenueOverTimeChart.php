<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Receivable;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class RevenueOverTime<PERSON>hart extends ApexChartWidget
{
    protected static ?int $sort = 1;
    protected static ?string $chartId = 'revenueOverTimeChart';
    protected static ?string $heading = 'Faturamento por período';
    protected int | string | array $columnSpan = 12;

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    protected function getOptions(): array
    {
        $data = Receivable::query()
            ->select([
                DB::raw('sum(case when settled_at is not null then updated_amount else 0 end) as paid_amount'),
                DB::raw('sum(case when settled_at is null and expires_at >= \'' . session('Dashboard_filters')['end_date'] . '\' then updated_amount else 0 end) as open_amount'),
                DB::raw('sum(case when settled_at is null and expires_at < \'' . session('Dashboard_filters')['end_date'] . '\' then updated_amount else 0 end) as overdue_amount'),
                DB::raw('date_format(expires_at, "%m/%Y") as month_year'),
            ])
            ->where('expires_at', '>=', session('Dashboard_filters')['start_date'])
            ->where('expires_at', '<=', session('Dashboard_filters')['end_date'])
            ->groupBy('month_year')
            ->get()
            ->mapWithKeys(fn(Receivable $receivable): array => [
                $receivable->month_year => [
                    'paid_amount' => $receivable->paid_amount,
                    'open_amount' => $receivable->open_amount,
                    'overdue_amount' => $receivable->overdue_amount,
                ]
            ])
            ->toArray();

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'stacked' => true,
            ],
            'series' => [
                [
                    'name' => 'Pago',
                    'data' => array_column($data, 'paid_amount')
                ],
                [
                    'name' => 'Aberto',
                    'data' => array_column($data, 'open_amount')
                ],
                [
                    'name' => 'Atrasado',
                    'data' => array_column($data, 'overdue_amount')
                ]
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'min' => 0,
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            tooltip: {
                y: {
                    formatter: function (value) {
                        return value.toLocaleString('pt-BR',{ style: 'currency', currency: 'BRL' });
                    }
                }
            }
        }
        JS);
    }
}
