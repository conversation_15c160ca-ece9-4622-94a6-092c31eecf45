<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Delivery;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class DeliveriesPerPeriod<PERSON>hart extends ApexChartWidget
{
    protected static ?int $sort = 3;
    protected static ?string $chartId = 'deliveriesPerPeriodChart';
    protected static ?string $heading = 'Entregas por período (em quantidade)';

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        $start = Carbon::parse($this->filterFormData['Entregue em de'])->setHour(3);
        $end = Carbon::parse($this->filterFormData['Entregue em até'])->addDay()->setHour(2)->setMinute(59)->setSecond(59);

        $data = Delivery::query()
            ->select([
                'delivered_at',
                DB::raw('count(1) as count')
            ])
            ->where('delivered_at', '>=', $start->format('Y-m-d'))
            ->where('delivered_at', '<=', $end->format('Y-m-d'))
            ->groupBy('delivered_at')
            ->get()
            ->mapWithKeys(fn (Delivery $delivery): array => [
                format_date($delivery->delivered_at) => $delivery->count
            ])
            ->toArray();

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
            ],
            'series' => [
                [
                    'name' => 'Entregas por período',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'min' => 0,
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ],
        ];
    }

    protected function getFormSchema(): array
    {
        return [
            DatePicker::make('Entregue em de')
                ->default(now()->setTimezone('-3:00')->subDays(14)->format('Y-m-d')),
            DatePicker::make('Entregue em até')
                ->default(now()->setTimezone('-3:00')->format('Y-m-d')),
        ];
    }
}
