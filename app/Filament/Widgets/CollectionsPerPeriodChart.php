<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Collection;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CollectionsPerPeriod<PERSON>hart extends ApexChartWidget
{
    protected static ?int $sort = 4;
    protected static ?string $chartId = 'collectionsPerPeriodChart';
    protected static ?string $heading = 'Coletas por período (em quantidade)';

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        $start = Carbon::parse($this->filterFormData['Coletado em de'])->setHour(3);
        $end = Carbon::parse($this->filterFormData['Coletado em até'])->addDay()->setHour(2)->setMinute(59)->setSecond(59);

        $data = Collection::query()
            ->select([
                'collected_at',
                DB::raw('count(1) as count')
            ])
            ->where('collected_at', '>=', $start->format('Y-m-d'))
            ->where('collected_at', '<=', $end->format('Y-m-d'))
            ->groupBy('collected_at')
            ->get()
            ->mapWithKeys(fn (Collection $collection): array => [
                format_date($collection->collected_at) => $collection->count
            ])
            ->toArray();

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
            ],
            'series' => [
                [
                    'name' => 'Coletas por período',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'min' => 0,
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ],
        ];
    }

    protected function getFormSchema(): array
    {
        return [
            DatePicker::make('Coletado em de')
                ->default(now()->setTimezone('-3:00')->subDays(14)->format('Y-m-d')),
            DatePicker::make('Coletado em até')
                ->default(now()->setTimezone('-3:00')->format('Y-m-d')),
        ];
    }
}
