<?php

namespace App\Filament\Pages;

use App\Actions\Customer\ReadjustCustomerProduct;
use App\Models\Customer;
use App\Models\CustomerProduct;
use App\Models\Index;
use App\Models\Product;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;

class CreateCustomerProductsReadjustment extends Page
{
    use InteractsWithForms;

    protected static ?string $title = 'Reajuste de itens';
    protected static ?string $slug = 'create-customer-products-readjustment';
    protected static string $view = 'filament.pages.create-customer-products-readjustment';

    public string $minimum_billing_amount;
    public ?int $minimum_billing_amount_index_id;
    public string $readjustment_percentage;
    public string $readjustment_amount;
    public string $updated_amount;
    public string $next_readjustment_date;
    public Customer $customer;
    public array $items;
    public array $indices;

    protected function getActions(): array
    {
        return [
            Action::make('Voltar')
                ->color('gray')
                ->url(fn(): string => route('filament.app.resources.customers.edit', $this->customer->id)),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function mount(Customer $customer): void
    {
        $this->customer = $customer;
        $this->minimum_billing_amount = mask_money($customer->minimum_billing_amount ?? 0);
        $this->minimum_billing_amount_index_id = $customer->index_id ?? 0;

        /** @var \App\Models\CustomerProductsReadjustment $lastCustomerProductReadjusment */
        $lastCustomerProductReadjusment = $this->customer->customerProductsReadjustments()
            ->latest()
            ->first();

        $this->next_readjustment_date = !is_null($lastCustomerProductReadjusment)
            ? carbon($lastCustomerProductReadjusment->new_ended_at)->addMonths(12)->format('Y-m-d')
            : now()->addMonths(12)->format('Y-m-d');

        if ($customer->index_id && $customer->index_id > 0) {
            $this->readjustment_percentage = mask_percentage($customer->index->amount);
            $this->readjustment_amount = mask_money(unmask_money($this->minimum_billing_amount) * ($customer->index->amount / 100));
            $this->updated_amount = mask_money(unmask_money($this->minimum_billing_amount) * (1 + ($customer->index->amount / 100)));
        }

        $this->indices = Index::query()
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id')
            ->toArray();

        $this->items = $customer->customerProducts
            ->map(function (CustomerProduct $customerProduct) {
                return [
                    'customer_product_id' => $customerProduct->id,
                    'product_id' => $customerProduct->product_id,
                    'unit_amount' => mask_money($customerProduct->unit_amount),
                    'index_id' => $this->customer->index_id ?? '0',
                    'readjustment_percentage' => $this->customer->index_id && $this->customer->index_id > 0
                        ? mask_percentage($this->customer->index->amount)
                        : null,
                    'readjustment_amount' => $this->customer->index_id && $this->customer->index_id > 0
                        ? mask_money($customerProduct->unit_amount * ($this->customer->index->amount / 100))
                        : null,
                    'updated_amount' => $this->customer->index_id && $this->customer->index_id > 0
                        ? mask_money($customerProduct->unit_amount * (1 + ($this->customer->index->amount / 100)))
                        : mask_money($customerProduct->unit_amount),
                ];
            })
            ->toArray();
    }

    protected function getFormSchema(): array
    {
        $customerProductsCount = 0;
        $componentItemsCount = 0;

        return [
            Grid::make(6)->schema([
                TextInput::make('minimum_billing_amount')
                    ->label(__('customers.forms.fields.minimum_billing_amount'))
                    ->disabled()
                    ->mask(\Filament\Support\RawJs::make(<<<'JS'
                        'R$ ' + $money($input, ',')
                    JS)),
                Select::make('minimum_billing_amount_index_id')
                    ->label('Índice p/ reajuste')
                    ->options(array_merge([
                        0 => 'Reajuste manual',
                    ], Index::query()->orderBy('name')->get()->mapWithKeys(fn(Index $index): array => [$index->id => $index->name])->toArray()))
                    ->default($this->customer->index_id ?? '0')
                    ->selectablePlaceholder(false)
                    ->reactive()
                    ->afterStateUpdated(fn(?string $state, Get $get, Set $set) => $this->calculateMinimumBillingAmounts($state, $get, $set)),
                TextInput::make('readjustment_percentage')
                    ->label('% reajuste')
                    ->disabled(fn(Get $get): bool => $get('minimum_billing_amount_index_id') > 0)
                    ->mask(\Filament\Support\RawJs::make(<<<'JS'
                        $money($input, ',') + '%'
                    JS)),
                TextInput::make('readjustment_amount')
                    ->label('R$ reajuste')
                    ->disabled(fn(Get $get): bool => $get('minimum_billing_amount_index_id') > 0)
                    ->mask(\Filament\Support\RawJs::make(<<<'JS'
                        'R$ ' + $money($input, ',')
                    JS)),
                TextInput::make('updated_amount')
                    ->label('Valor atualizado')
                    ->disabled()
                    ->mask(\Filament\Support\RawJs::make(<<<'JS'
                        'R$ ' + $money($input, ',')
                    JS)),
                TextInput::make('next_readjustment_date')
                    ->label('Próx. reajuste em')
                    ->required()
                    ->type('date'),
            ]),
            Grid::make(1)->schema([
                TableRepeater::make('items')
                    ->hiddenLabel()
                    ->columns(5)
                    ->addActionLabel('Adicionar produto')
                    ->defaultItems(0)
                    ->afterStateHydrated(function (TableRepeater $component) use (&$customerProductsCount, &$componentItemsCount) {
                        if ($customerProductsCount === 0 && !is_null($this->customer)) {
                            if ($this->customer->relationLoaded('customerProducts')) {
                                $this->customer->load('customerProducts');
                            }

                            $customerProductsCount = $this->customer->customerProducts->count();
                        }

                        $componentItemsCount = $component->getItemsCount();
                    })
                    ->headers([
                        Header::make('Item')
                            ->width('40%'),
                        Header::make('Valor atual')
                            ->width('10%'),
                        Header::make('Índice a ser utilizado')
                            ->width('20%'),
                        Header::make('% reajuste')
                            ->width('10%'),
                        Header::make('R$ reajuste')
                            ->width('10%'),
                        Header::make('Valor atualizado')
                            ->width('10%'),
                    ])
                    ->schema([
                        Hidden::make('customer_product_id'),
                        Select::make('product_id')
                            ->required()
                            ->autofocus(function () use (&$customerProductsCount, &$componentItemsCount): bool {
                                return $componentItemsCount !== $customerProductsCount;
                            })
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Product::query()
                                    ->where('code', $search)
                                    ->orWhere('name', 'like', "%$search%")
                                    ->pluck('name', 'id');
                            })
                            ->afterStateHydrated(function (Select $component) use (&$componentItemsCount, &$customerProductsCount) {
                                $this->handleCustomerProduct($component, $this->customer, $customerProductsCount, $componentItemsCount);
                            })
                            ->afterStateUpdated(function (Select $component) use (&$componentItemsCount, &$customerProductsCount) {
                                $this->handleCustomerProduct($component, $this->customer, $customerProductsCount, $componentItemsCount);
                            })
                            ->getOptionLabelUsing(function ($value): ?string {
                                $customerProduct = $this->customer->customerProducts
                                    ->filter(fn(CustomerProduct $customerProduct): bool => $customerProduct->product_id === $value)
                                    ->first();

                                if (!is_null($customerProduct->product)) {
                                    return $customerProduct->product->name;
                                }

                                return Product::query()
                                    ->withTrashed()
                                    ->find($value)
                                    ->name;
                            })
                            ->columnSpan(2),
                        TextInput::make('unit_amount')
                            ->required()
                            ->disabled()
                            ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                'R$ ' + $money($input, ',')
                            JS)),
                        Select::make('index_id')
                            ->options(array_merge([
                                0 => 'Reajuste manual',
                            ], Index::query()->orderBy('name')->get()->mapWithKeys(fn(Index $index): array => [$index->id => $index->name])->toArray()))
                            ->default($this->customer->index_id ?? '0')
                            ->selectablePlaceholder(false)
                            ->reactive()
                            ->afterStateUpdated(function (?string $state, Get $get, Set $set) {
                                if ((int) $get('index_id') === 0) {
                                    return;
                                }

                                /** @var \App\Models\Index $index */
                                $index = Index::find($state);

                                $set('readjustment_percentage', mask_percentage($index->amount));
                                $set('readjustment_amount', mask_money(unmask_money($get('unit_amount')) * (unmask_percentage($index->amount) / 100)));
                                $set('updated_amount', mask_money(unmask_money($get('unit_amount')) * (1 + (unmask_percentage($index->amount) / 100))));
                            }),
                        TextInput::make('readjustment_percentage')
                            ->label('% reajuste')
                            ->disabled(fn(Get $get): bool => $get('index_id') > 0)
                            ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                $money($input, ',') + '%'
                            JS))
                            ->lazy()
                            ->afterStateUpdated(function (?string $state, Get $get, Set $set): void {
                                if ((int) $get('index_id') > 0) {
                                    return;
                                }

                                $set('readjustment_amount', mask_money(unmask_money($get('unit_amount')) * (unmask_percentage($state) / 100)));
                                $set('updated_amount', mask_money(unmask_money($get('unit_amount')) * (1 + (unmask_percentage($state) / 100))));
                            }),
                        TextInput::make('readjustment_amount')
                            ->label('R$ reajuste')
                            ->disabled(fn(Get $get): bool => $get('index_id') > 0)
                            ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                'R$ ' + $money($input, ',')
                            JS))
                            ->lazy()
                            ->afterStateUpdated(function (?string $state, Get $get, Set $set): void {
                                if ((int) $get('index_id') > 0) {
                                    return;
                                }

                                $set('readjustment_percentage', mask_percentage((unmask_money($state) / unmask_money($get('unit_amount')) * 100)));
                                $set('updated_amount', mask_money(unmask_money($get('unit_amount')) + unmask_money($state)));
                            }),
                        TextInput::make('updated_amount')
                            ->label('Valor atualizado')
                            ->disabled()
                            ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                'R$ ' + $money($input, ',')
                            JS)),
                    ]),
            ]),
        ];
    }

    /**
     * Handle the customer product post processing.
     *
     * @param  \App\Core\Filament\Form\Fields\Select $component
     * @param  \App\Models\Customer|null $customer
     * @param  int $componentItemsCount
     * @param  int $customerProductsCount
     * @return void
     */
    protected function handleCustomerProduct(
        Select $component,
        ?Customer $customer,
        int &$componentItemsCount,
        int &$customerProductsCount
    ): void {
        if (!is_null($customer)) {
            if (!$customer->relationLoaded('customerProducts')) {
                $customer->load('customerProducts');
            }

            $customerProductsCount = $customer->customerProducts->count();
        }

        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
    }

    protected function calculateMinimumBillingAmounts(?string $minimumBillingIndexId, Get $get, Set $set): void
    {
        if (!is_null($minimumBillingIndexId) && (int) $minimumBillingIndexId > 0) {
            /** @var \App\Models\Index $index */
            $index = Index::find($minimumBillingIndexId);

            $set('readjustment_percentage', mask_percentage($index->amount));
            $set('readjustment_amount', mask_money(unmask_money($this->minimum_billing_amount) * ($index->amount / 100)));
            $set('updated_amount', mask_money(unmask_money($this->minimum_billing_amount) * (1 + ($index->amount / 100))));
            return;
        }
    }

    public function readjust()
    {
        DB::transaction(function (): void {
            /** @var \App\Models\CustomerProductsReadjustment $lastCustomerProductsReadjustment */
            $lastCustomerProductsReadjustment = $this->customer->customerProductsReadjustments()
                ->latest()
                ->first();

            /** @var \App\Models\CustomerProductsReadjustment $customerProductsReadjustment */
            $customerProductsReadjustment = $this->customer->customerProductsReadjustments()->create([
                'operator_id' => auth()->user()?->operator->id,
                'batch_no' => $lastCustomerProductsReadjustment?->batch_no
                    ? ($lastCustomerProductsReadjustment?->batch_no + 1)
                    : 1,
                'referring_month' => carbon($this->customer->service_started_at)->month,
                'referring_year' => now()->year,
                'old_amount' => $lastCustomerProductsReadjustment->new_amount,
                'new_amount' => $lastCustomerProductsReadjustment->new_amount,
                'old_ended_at' => $lastCustomerProductsReadjustment->new_ended_at,
                'new_ended_at' => carbon($this->next_readjustment_date)->format('Y-m-d'),
            ]);

            foreach ($this->items as $item) {
                /** @var \App\Models\CustomerProduct $customerProduct */
                $customerProduct = CustomerProduct::find($item['customer_product_id']);

                /** @var \App\Models\CustomerProductsReadjustmentItem $customerProductsReadjustmentItem */
                $customerProductsReadjustmentItem = $customerProductsReadjustment->customerProductsReadjustmentItems()->create([
                    'customer_product_id' => $customerProduct->id,
                    'index_id' => $item['index_id'] > 0
                        ? $item['index_id']
                        : null,
                    'old_amount' => unmask_money($item['unit_amount']),
                    'new_amount' => unmask_money($item['updated_amount']),
                ]);

                $customerProduct->update([
                    'unit_amount' => $customerProductsReadjustmentItem->new_amount,
                    'total_amount' => $customerProduct->quantity * $customerProductsReadjustmentItem->new_amount,
                ]);
            }
        });

        success_notification('Os itens foram reajustados.')->send();

        return redirect()->route('filament.app.resources.customers.edit', $this->customer->id);
    }
}
