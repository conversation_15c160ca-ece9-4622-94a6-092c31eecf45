<?php

namespace App\Filament\Pages;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Dashboard as BasePage;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;

class Dashboard extends BasePage
{
    use HasFiltersForm;

    public static ?string $navigationLabel = 'Início';

    public function getColumns(): int | string | array
    {
        return 12;
    }

    public function filtersForm(Form $form): Form
    {
        return $form->schema([
            Section::make()->schema([
                DatePicker::make('start_date')
                    ->label('Início')
                    ->maxDate(fn(Get $get) => $get('end_date') ?: now())
                    ->default(now()->startOfMonth()->format('Y-m-d')),
                DatePicker::make('end_date')
                    ->label('Fim')
                    ->minDate(fn(Get $get) => $get('start_date') ?: now())
                    ->maxDate(now())
                    ->default(now()->endOfMonth()->format('Y-m-d')),
            ])->columns(2),
        ]);
    }
}
