<?php

namespace App\Filament\Pages\Receivable;

use App\Models\BankAccountWallet;
use App\Models\Receivable;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Throwable;

class SettleReceivable extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $title = 'Quitar fatura';
    protected static ?string $slug = 'settle-receivable';
    protected static string $view = 'filament.pages.receivable.settle-receivable';

    public int $receivableId;
    public int $bankAccountWalletId;
    public string $settlementAmount;
    public string $settledAt;

    public Receivable $receivable;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Mount the component.
     *
     * @param  string $record
     * @return void
     */
    public function mount(string $record): void
    {
        $this->receivableId = $record;
        $this->receivable = Receivable::find($this->receivableId);
        $this->settlementAmount = mask_money($this->receivable->original_amount);
        $this->settledAt = now()->format('Y-m-d');
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                Select::make('bankAccountWalletId')
                    ->label('Carteira')
                    ->lazy()
                    ->options(
                        BankAccountWallet::query()
                            ->with('bankAccount.bank')
                            ->get()
                            ->sortBy(['bank.name', 'bankAccount.branch_number'])
                            ->map(function (BankAccountWallet $bankAccountWallet): array {
                                return [
                                    'id' => $bankAccountWallet->id,
                                    'name' => "{$bankAccountWallet->bankAccount->bank->code} - {$bankAccountWallet->bankAccount->bank->name}"
                                        . " | ({$bankAccountWallet->bankAccount->branch_number}-{$bankAccountWallet->bankAccount->branch_digit} / "
                                        . "{$bankAccountWallet->bankAccount->number}-{$bankAccountWallet->bankAccount->digit})"
                                ];
                            })
                            ->pluck('name', 'id')
                    )
                    ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set): void {
                        $set('drawee_tax_id_number', BankAccountWallet::find($state)?->drawee_tax_id_number ?? '');
                    }),
                TextInput::make('drawee_tax_id_number')
                    ->label(__('bank_account_wallets.forms.fields.drawee_tax_id_number'))
                    ->disabled(),
            ]),
            Grid::make(2)->schema([
                TextInput::make('settlementAmount')
                    ->label('Valor pago')
                    ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                                'R$ ' + $money($input, ',')
                                            JS
                    )),
                TextInput::make('settledAt')
                    ->type('date')
                    ->label('Baixado em'),
            ])
        ];
    }

    /**
     * Settle the bank slip.
     *
     * @return mixed
     */
    public function settleReceivable(): mixed
    {
        try {
            \App\Actions\Receivable\SettleReceivable::run(
                $this->receivable,
                unmask_money($this->settlementAmount),
                carbon($this->settledAt),
                BankAccountWallet::find($this->bankAccountWalletId)
            );

            success_notification(__('receivables.responses.generate_bank_slip.success'))->send();
            return redirect()->route('filament.app.resources.receivables.index');
        } catch (Throwable $th) {
            error($th);
            error_notification()->send();
        }

        return null;
    }
}
