<?php

namespace App\Filament\Pages\Report;

use App\Core\Filament\Form\Sections\ReportFormatSection;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class LoadReceivablesTable extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Tabela de faturas';
    protected static ?string $slug = 'receivables-table';
    protected static string $view = 'filament.pages.report.load-receivables-table';

    public ?string $format;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            ReportFormatSection::build()
        ];
    }

    /**
     * Generate the report.
     *
     * @return mixed
     */
    public function generate(): mixed
    {
        return redirect()->route('reports.generate_receivables_table', [
            'token' => base64_encode('receivables-table;' . $this->format)
        ]);
    }

    /**
     * Go back to the reports page.
     *
     * @return mixed
     */
    public function cancel(): mixed
    {
        return redirect()->route('filament.pages.list-tables');
    }
}
