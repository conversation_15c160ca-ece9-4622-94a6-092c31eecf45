<?php

namespace App\Filament\Pages\Report;

use App\Core\Filament\Form\Sections\ReportFormatSection;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class LoadDeliveriesTable extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Tabela de entregas';
    protected static ?string $slug = 'deliveries-table';
    protected static string $view = 'filament.pages.report.load-deliveries-table';

    public ?string $format;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            ReportFormatSection::build()
        ];
    }

    /**
     * Generate the report.
     *
     * @return mixed
     */
    public function generate(): mixed
    {
        return redirect()->route('reports.generate_deliveries_table', [
            'token' => base64_encode('deliveries-table;' . $this->format)
        ]);
    }

    /**
     * Go back to the reports page.
     *
     * @return mixed
     */
    public function cancel(): mixed
    {
        return redirect()->route('filament.pages.list-tables');
    }
}
