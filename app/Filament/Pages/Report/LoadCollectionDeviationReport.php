<?php

namespace App\Filament\Pages\Report;

use App\Core\Filament\Form\Sections\ReportFormatSection;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class LoadCollectionDeviationReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Desvio de coletas';
    protected static ?string $slug = 'collection-deviation-report';
    protected static string $view = 'filament.pages.report.load-collection-deviation-report';

    public ?string $collectedAt;
    public int $minUnitDifference = 8;
    public string $minDifferencePercentage = '15%';
    public ?string $format;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Mount the component.
     *
     * @return void
     */
    public function mount(): void
    {
        $this->collectedAt = now();
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Filtros')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('collectedAt')
                            ->type('date')
                            ->label('Data da coleta')
                            ->required(),
                        TextInput::make('minUnitDifference')
                            ->label('Limite mínimo dif. unidades')
                            ->numeric()
                            ->required(),
                        TextInput::make('minDifferencePercentage')
                            ->label('Limite mínimo dif. percentual')
                            ->required()
                            ->mask(function () {
                                return \Filament\Support\RawJs::make(<<<'JS'
                                    $money($input, ',') + '%'
                                JS);
                            }),
                    ])
                ]),
            ReportFormatSection::build(),
        ];
    }

    /**
     * Generate the report.
     *
     * @return mixed
     */
    public function generate(): mixed
    {
        return redirect()->route('reports.generate_supply_chain_collection_deviation_report', [
            'token' => base64_encode(
                $this->collectedAt .
                    ';' . $this->minUnitDifference .
                    ';' . $this->minDifferencePercentage .
                    ';' . $this->format
            )
        ]);
    }

    /**
     * Go back to the reports page.
     *
     * @return mixed
     */
    public function cancel(): mixed
    {
        return redirect()->route('filament.pages.list-reports');
    }
}
