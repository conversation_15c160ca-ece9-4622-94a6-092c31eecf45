<?php

namespace App\Filament\Pages\Report;

use App\Core\Filament\Form\Sections\ReportFormatSection;
use App\Models\Customer;
use Closure;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Illuminate\Database\Eloquent\Builder;

class LoadStockStatementReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Extrato de estoque';
    protected static ?string $slug = 'stock-statement-report';
    protected static string $view = 'filament.pages.report.load-stock-statement-report';

    public ?string $customerId;
    public ?string $customerTradingName;
    public ?string $customerTaxIdNumber;
    public ?string $dateFrom;
    public ?string $dateTo;
    public ?string $format;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Mount the component.
     *
     * @return void
     */
    public function mount(): void
    {
        $this->dateFrom = now()->subMonth();
        $this->dateTo = now();
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Filtros')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        Select::make('customerId')
                            ->label(__('collections.forms.fields.customer_id'))
                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                            ->columnSpan(2)
                            ->reactive()
                            ->required()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                $taxIdNumber = get_numbers($search);

                                return Customer::query()
                                    ->where('name', 'like', "%$search%")
                                    ->orWhere('trading_name', 'like', "%$search%")
                                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                    })
                                    ->get()
                                    ->map(fn (Customer $customer) => [
                                        'id' => $customer->id,
                                        'name' => "$customer->name | $customer->trading_name"
                                    ])
                                    ->pluck('name', 'id');
                            })
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set): void {
                                $this->handleCustomerDependantFields($state, $set);
                            }),
                        TextInput::make('customerTradingName')
                            ->disabled()
                            ->label(__('collections.forms.fields.customer_trading_name')),
                        TextInput::make('customerTaxIdNumber')
                            ->disabled()
                            ->label(__('collections.forms.fields.customer_tax_id_number'))
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('dateFrom')
                            ->type('date')
                            ->label('Data de')
                            ->required(),
                        TextInput::make('dateTo')
                            ->type('date')
                            ->label('Data até')
                            ->required()
                    ])
                ]),
            ReportFormatSection::build()
        ];
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected function handleCustomerDependantFields(?string $customerId, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customerTradingName', $customer?->trading_name ?? '');
        $set('customerTaxIdNumber', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }

    /**
     * Generate the report.
     *
     * @return mixed
     */
    public function generate(): mixed
    {
        return redirect()->route('reports.generate_supply_chain_stock_statement_report', [
            'token' => base64_encode($this->customerId . ';' . $this->dateFrom . ';' . $this->dateTo . ';' . $this->format)
        ]);
    }

    /**
     * Go back to the reports page.
     *
     * @return mixed
     */
    public function cancel(): mixed
    {
        return redirect()->route('filament.pages.list-reports');
    }
}
