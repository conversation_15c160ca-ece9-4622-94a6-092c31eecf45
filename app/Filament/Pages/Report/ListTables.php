<?php

namespace App\Filament\Pages\Report;

use App\Enums\TableEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class ListTables extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Exportação de tabelas';
    protected static ?string $title = 'Exportação de tabelas';
    protected static ?string $slug = 'list-tables';
    protected static string $view = 'filament.pages.report.list-tables';

    public string $selectedTable;
    public array $tables;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Mount the component.
     *
     * @return void
     */
    public function mount(): void
    {
        $this->tables = [];

        collect(TableEnum::getTranslated())->each(function (string $tableName, string $tableKey) {
            // if (!auth()->user()->can("get_{$tableKey}_table")) {
            //     return;
            // }

            $this->tables[$tableKey] = $tableName;
        });

        $this->selectedTable = array_key_first($this->tables);
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Select::make('selectedTable')
                ->label('Escolha uma tabela a ser emitida')
                ->options($this->tables)
                ->selectablePlaceholder(false)
        ];
    }

    /**
     * Load the table page.
     *
     * @return mixed
     */
    public function loadTable(): mixed
    {
        return redirect()->route("tables.load_{$this->selectedTable}_table");
    }
}
