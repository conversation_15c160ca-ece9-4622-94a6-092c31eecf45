<?php

namespace App\Filament\Pages\Report;

use App\Enums\ReportEnum;
use App\Enums\RoleEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;

class ListReports extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Relatórios';
    protected static ?string $title = 'Relatórios';
    protected static ?string $slug = 'list-reports';
    protected static ?string $navigationGroup = 'Relatórios';
    protected static string $view = 'filament.pages.report.list-reports';

    public string $selectedReport;
    public array $reports;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    /**
     * Mount the component.
     *
     * @return void
     */
    public function mount(): void
    {
        $this->reports = [];

        collect(ReportEnum::getMapped())->each(function (array $groupName, string $groupKey) {
            collect($groupName['items'])->each(function (string $reportName, string $reportKey) use ($groupKey) {
                // if (!auth()->user()->can("get_{$reportKey}_report")) {
                //     return;
                // }

                $this->reports[$groupKey . '_' . $reportKey] = $reportName;
            });
        });

        $this->selectedReport = array_key_first($this->reports);
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Select::make('selectedReport')
                ->label('Escolha um relatório a ser emitido')
                ->options($this->reports)
                ->selectablePlaceholder(false)
        ];
    }

    /**
     * Load the report page.
     *
     * @return mixed
     */
    public function loadReport(): mixed
    {
        return redirect()->route("reports.load_{$this->selectedReport}_report");
    }
}
