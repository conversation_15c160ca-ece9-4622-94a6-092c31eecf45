<?php

namespace App\Filament\Pages;

use App\Enums\RoleEnum;
use App\Models\User;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Hash;

class UpdatePassword extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Alterar senha';
    protected static string $view = 'filament.pages.update-password';

    public string $current_password;
    public string $new_password;
    public string $new_password_confirmation;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected function getFormSchema(): array
    {
        return [
            Grid::make(3)->schema([
                TextInput::make('current_password')
                    ->label('Senha atual')
                    ->required()
                    ->rules(['required_with:new_password'])
                    ->currentPassword()
                    ->autocomplete('off'),
                TextInput::make('new_password')
                    ->label('Nova senha')
                    ->required()
                    ->rules(['confirmed'])
                    ->autocomplete('new-password'),
                TextInput::make('new_password_confirmation')
                    ->label('Confirme a nova senha')
                    ->required()
                    ->rules(['required_with:new_password'])
                    ->autocomplete('new-password'),
            ])
        ];
    }

    public function updatePassword()
    {
        /** @var \App\Models\User */
        $user = User::findOrFail(auth()->id());

        // Verifies if the old password was typed correctly.
        if (!Hash::check($this->current_password, $user->password)) {
            Notification::make('update_password_notification')
                ->title('Ops!')
                ->icon('heroicon-s-check')
                ->body('A sua senha atual não confere.')
                ->send();

            return;
        }

        $user->update(['password' => Hash::make($this->new_password)]);

        // Necessary so the user won't be logged out automatically.
        request()->session()->put([
            'password_hash_' . auth()->getDefaultDriver() => $user->getAuthPassword(),
        ]);

        Notification::make('update_password_notification')
            ->title('Sucesso!')
            ->icon('heroicon-s-check')
            ->body('A sua senha foi alterada.')
            ->send();

        return redirect()->route('filament.app.pages.dashboard');
    }
}
