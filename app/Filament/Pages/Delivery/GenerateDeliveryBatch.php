<?php

namespace App\Filament\Pages\Delivery;

use App\Actions\Delivery\CreateDelivery;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\Customer;
use App\Models\DeliveryAdjustment;
use App\Models\DeliveryBatch;
use App\Models\Receivable;
use App\Models\User;
use App\Notifications\DeliveryBatch\NewDeliveryBatchGeneratedNotification;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Throwable;

class GenerateDeliveryBatch extends Page
{
    use InteractsWithForms;

    protected static ?string $title = 'Criar lote de entrega';
    protected static ?string $slug = 'create-delivery-batch';
    protected static string $view = 'filament.pages.delivery.generate-delivery-batch';

    public array $deliveries;
    public bool $disabled = false;
    public string $customer_ids;
    public string $delivered_at;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Section::make('Buscar clientes')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        Textarea::make('customer_ids')
                            ->label('Clientes (um ID por linha)')
                            ->rows(10)
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                if (is_null($get('delivered_at'))) {
                                    return;
                                }

                                $mappedCustomers = collect(explode("\n", $get('customer_ids')))
                                    ->filter(function (string $item): bool {
                                        $explodedCustomer = explode(';', $item);
                                        return !is_null($explodedCustomer[0]) && $explodedCustomer[0] !== '';
                                    })
                                    ->mapWithKeys(function (string $item): array {
                                        $explodedCustomer = explode(';', $item);

                                        return [
                                            $explodedCustomer[0] => [
                                                'date' => Carbon::createFromFormat('d/m/Y', $explodedCustomer[1])->format('Y-m-d'),
                                                'percentage' => $explodedCustomer[2],
                                                'itinerary' => $explodedCustomer[3],
                                            ],
                                        ];
                                    })
                                    ->toArray();

                                $set('deliveries', Customer::query()
                                    ->whereIn('id', explode("\n", $get('customer_ids')))
                                    ->get()
                                    ->map(function (Customer $customer) use ($mappedCustomers): array {
                                        return [
                                            'customer_id' => $customer->id,
                                            'customer_trading_name' => $customer->trading_name,
                                            'customer_tax_id_number' => $customer->friendly_tax_id_number,
                                            'delivery_reference_date' => $mappedCustomers[$customer->id]['date'],
                                            'itinerary' => $mappedCustomers[$customer->id]['itinerary'],
                                            'percentage' => !is_null($mappedCustomers[$customer->id]['percentage']) && $mappedCustomers[$customer->id]['percentage'] !== ''
                                                ? $mappedCustomers[$customer->id]['percentage'] . '%'
                                                : '100%',
                                        ];
                                    })
                                    ->toArray());
                            }),
                    ]),
                    Grid::make(1)->schema([
                        TextInput::make('delivered_at')
                            ->label('Data da entrega')
                            ->type('date')
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                if (is_null($get('customer_ids'))) {
                                    return;
                                }

                                $mappedCustomers = collect(explode("\n", $get('customer_ids')))
                                    ->filter(function (string $item): bool {
                                        $explodedCustomer = explode(';', $item);
                                        return !is_null($explodedCustomer[0]) && $explodedCustomer[0] !== '';
                                    })
                                    ->mapWithKeys(function (string $item): array {
                                        $explodedCustomer = explode(';', $item);

                                        return [
                                            $explodedCustomer[0] => [
                                                'date' => Carbon::createFromFormat('d/m/Y', $explodedCustomer[1])->format('Y-m-d'),
                                                'percentage' => $explodedCustomer[2],
                                                'itinerary' => $explodedCustomer[3],
                                            ],
                                        ];
                                    })
                                    ->toArray();

                                $set('deliveries', Customer::query()
                                    ->whereIn('id', explode("\n", $get('customer_ids')))
                                    ->get()
                                    ->map(function (Customer $customer) use ($mappedCustomers): array {
                                        return [
                                            'customer_id' => $customer->id,
                                            'customer_trading_name' => $customer->trading_name,
                                            'customer_tax_id_number' => $customer->friendly_tax_id_number,
                                            'delivery_reference_date' => $mappedCustomers[$customer->id]['date'],
                                            'itinerary' => $mappedCustomers[$customer->id]['itinerary'],
                                            'percentage' => !is_null($mappedCustomers[$customer->id]['percentage']) && $mappedCustomers[$customer->id]['percentage'] !== ''
                                                ? $mappedCustomers[$customer->id]['percentage'] . '%'
                                                : '100%',
                                        ];
                                    })
                                    ->toArray());
                            })
                    ])
                ]),
            Section::make('Clientes disponíveis')
                ->compact()
                ->schema([
                    TableRepeater::make('deliveries')
                        ->hiddenLabel()
                        ->addable(false)
                        ->reorderable(false)
                        ->deletable(false)
                        ->columns(5)
                        ->headers([
                            Header::make('Cliente')
                                ->width('10%'),
                            Header::make('Nome fantasia'),
                            Header::make('Data da referência programada'),
                            Header::make('Roteiro'),
                            Header::make('Percentual de entrega')
                                ->width('10%'),
                        ])
                        ->schema([
                            TextInput::make('customer_id'),
                            TextInput::make('customer_trading_name')
                                ->disabled(),
                            TextInput::make('delivery_reference_date')
                                ->type('date'),
                            TextInput::make('itinerary'),
                            TextInput::make('percentage')
                                ->mask(function () {
                                    return \Filament\Support\RawJs::make(<<<'JS'
                                        $money($input, ',') + '%'
                                    JS);
                                }),
                        ])
                ])
        ];
    }

    public function createBatch()
    {
        $this->disabled = true;

        $deliveryCustomerIdErrors = [];

        /** @var \App\Models\DeliveryBatch $deliveryBatch */
        $deliveryBatch = DB::transaction(function () use (&$deliveryCustomerIdErrors) {
            /** @var \App\Models\DeliveryBatch $deliveryBatch */
            $deliveryBatch = DeliveryBatch::create([
                'contract_ids' => $this->customer_ids,
                'delivered_at' => $this->delivered_at,
            ]);

            collect($this->deliveries)->each(function (array $deliveryData) use (&$deliveryBatch, &$deliveryCustomerIdErrors): void {
                $percentage = unmask_percentage($deliveryData['percentage']);

                /** @var \App\Models\Customer $customer */
                $customer = Customer::find($deliveryData['customer_id']);

                try {
                    $openReceivableExists = Receivable::query()
                        ->where('customer_id', $deliveryData['customer_id'])
                        ->whereNull('settled_at')
                        ->where('expires_at', '<', now()->subDays($customer->payment_recovery_day_count)->format('Y-m-d'))
                        ->exists();

                    if ($openReceivableExists) {
                        $deliveryCustomerIdErrors['overdue_receivable'][] = $deliveryData['customer_trading_name'];
                        return;
                    }

                    /** @var \App\Models\Collection|null $collection */
                    $collection = Collection::query()
                        ->with('collectionItems')
                        ->where('customer_id', $deliveryData['customer_id'])
                        ->where('collected_at', $deliveryData['delivery_reference_date'])
                        ->whereRelation('customer', 'active', true)
                        ->first();

                    if (is_null($collection)) {
                        $deliveryCustomerIdErrors['reference_date'][] = $deliveryData['customer_trading_name'];
                        return;
                    }

                    /** @var \Illuminate\Support\Collection $nextDeliveryAdjustments */
                    $nextDeliveryAdjustments = DeliveryAdjustment::query()
                        ->where('customer_id', $deliveryData['customer_id'])
                        ->whereNull('delivery_id')
                        ->get();

                    $deliveryAdjustments = [];

                    $products = $collection->collectionItems
                        ->map(function (CollectionItem $collectionItem) use ($nextDeliveryAdjustments, &$deliveryAdjustments) {
                            $quantity = $collectionItem->quantity;

                            /** @var \App\Models\DeliveryAdjustment $nextDeliveryAdjustmentItem */
                            $nextDeliveryAdjustmentItem = $nextDeliveryAdjustments
                                ->filter(fn(DeliveryAdjustment $deliveryAdjustment): bool => (int) $deliveryAdjustment->product_id === (int) $collectionItem->product_id)
                                ->first();

                            if (!$nextDeliveryAdjustmentItem) {
                                return [
                                    'product_id' => $collectionItem->product_id,
                                    'quantity' => number_format($quantity, 0)
                                ];
                            }

                            if (((int) $nextDeliveryAdjustmentItem->desired_withdraw_quantity) > ((int) $collectionItem->quantity) * ($nextDeliveryAdjustmentItem->max_withdraw_percentage / 100)) {
                                $percentageResult = round(((int) $collectionItem->quantity) * ($nextDeliveryAdjustmentItem->max_withdraw_percentage / 100), 0);

                                $quantity -= $percentageResult;
                                $desiredWithdrawQuantityBalance = ((int) $nextDeliveryAdjustmentItem->desired_withdraw_quantity) - $percentageResult;

                                DeliveryAdjustment::create([
                                    'delivery_id' => null,
                                    'customer_id' => $nextDeliveryAdjustmentItem->customer_id,
                                    'product_id' => $nextDeliveryAdjustmentItem->product_id,
                                    'desired_withdraw_quantity' => $desiredWithdrawQuantityBalance,
                                    'max_withdraw_percentage' => $nextDeliveryAdjustmentItem->max_withdraw_percentage,
                                    'parent_delivery_adjustment_id' => $nextDeliveryAdjustmentItem->id,
                                    'additional_info' => $nextDeliveryAdjustmentItem->additional_info,
                                ]);
                            } else {
                                $quantity -= $nextDeliveryAdjustmentItem->desired_withdraw_quantity;
                            }

                            $deliveryAdjustments[] = $nextDeliveryAdjustmentItem;

                            return [
                                'product_id' => $collectionItem->product_id,
                                'quantity' => number_format($quantity, 0)
                            ];
                        })
                        ->toArray();

                    $additionalInfo = $nextDeliveryAdjustments
                        ->pluck('additional_info')
                        ->unique()
                        ->implode(' / ');

                    /** @var \App\Models\Delivery $delivery */
                    $delivery = CreateDelivery::run([
                        'customer_id' => $deliveryData['customer_id'],
                        'delivered_at' => $this->delivered_at,
                        'products' => $products,
                        'additional_info' => substr($additionalInfo, 0, 1000),
                        'send_email' => true,
                    ], auth()->id(), true, $percentage);

                    foreach ($deliveryAdjustments as $deliveryAdjustment) {
                        $deliveryAdjustment->update(['delivery_id' => $delivery->id]);
                    }

                    $deliveryBatch->deliveryBatchDeliveries()->create([
                        'customer_id' => $deliveryData['customer_id'],
                        'collection_id' => $collection->id,
                        'delivery_reference_date' => $deliveryData['delivery_reference_date'],
                        'delivery_id' => $delivery->id,
                        'itinerary' => $deliveryData['itinerary'],
                    ]);
                } catch (Throwable $th) {
                    error($th);
                }
            });

            return $deliveryBatch;
        });

        if (!empty($deliveryCustomerIdErrors)) {
            if (isset($deliveryCustomerIdErrors['reference_date']) && !empty($deliveryCustomerIdErrors['reference_date'])) {
                $message = 'As entregas referentes aos clientes '
                    . implode(', ', $deliveryCustomerIdErrors['reference_date'])
                    . ' não puderam ser geradas pois não foram encontradas coletas para as datas de referência programadas.';

                database_notification(auth()->id(), $message, true);
            }

            if (isset($deliveryCustomerIdErrors['overdue_receivable']) && !empty($deliveryCustomerIdErrors['overdue_receivable'])) {
                $message = 'As entregas referentes aos clientes '
                    . implode(', ', $deliveryCustomerIdErrors['overdue_receivable'])
                    . ' não puderam ser geradas pois existem faturas em aberto.';

                database_notification(auth()->id(), $message, true);
            }
        } else {
            success_notification(__('deliveries.responses.generate_batch.success'))->send();
        }

        $this->sendAdministratorNotification($deliveryBatch);

        return redirect()->route('filament.app.resources.deliveries.index');
    }

    /**
     * Send the administrator notification.
     *
     * @param  \App\Models\DeliveryBatch $deliveryBatch
     * @return void
     */
    protected function sendAdministratorNotification(DeliveryBatch $deliveryBatch): void
    {
        User::query()
            ->where('email', '<EMAIL>')
            ->first()
            ->notify(new NewDeliveryBatchGeneratedNotification($deliveryBatch));
    }
}
