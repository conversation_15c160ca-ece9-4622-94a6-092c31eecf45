<?php

namespace App\Filament\Pages\Delivery;

use App\Models\Customer;
use Carbon\Carbon;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Page;

class GenerateDeliveryMap extends Page
{
    use InteractsWithForms;

    protected static ?string $title = 'Criar mapa de entrega';
    protected static ?string $slug = 'create-delivery-map';
    protected static string $view = 'filament.pages.delivery.generate-delivery-map';

    public array $deliveries = [];
    public array $mapData = [];
    public bool $disabled = false;
    public string $customer_ids = '';
    public string $delivered_at = '';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Buscar clientes')
                ->compact()
                ->schema([
                    Grid::make(2)->schema([
                        Textarea::make('customer_ids')
                            ->label('Clientes (um ID por linha)')
                            ->rows(10)
                            ->columnSpan(1)
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get): void {
                                $this->updateMapData($get);
                            }),
                        TextInput::make('delivered_at')
                            ->label('Data de entrega')
                            ->type('date')
                            ->columnSpan(1)
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get): void {
                                $this->updateMapData($get);
                            }),
                    ])
                ]),
        ];
    }

    public function updateMapData(\Filament\Forms\Get $get): void
    {
        if (is_null($get('delivered_at')) || is_null($get('customer_ids')) || $get('customer_ids') === '') {
            $this->mapData = [];
            return;
        }

        $mappedCustomers = collect(explode("\n", $get('customer_ids')))
            ->filter(function (string $item): bool {
                $explodedCustomer = explode(';', $item);
                return !is_null($explodedCustomer[0]) && $explodedCustomer[0] !== '';
            })
            ->mapWithKeys(function (string $item): array {
                $explodedCustomer = explode(';', $item);

                return [
                    $explodedCustomer[0] => [
                        'date' => Carbon::createFromFormat('d/m/Y', $explodedCustomer[1])->format('Y-m-d'),
                        'percentage' => $explodedCustomer[2],
                        'itinerary' => $explodedCustomer[3],
                    ],
                ];
            })
            ->toArray();

        $customerIds = array_keys($mappedCustomers);

        /** @var \Illuminate\Support\Collection $customers */
        $customers = Customer::query()
            ->whereIn('id', $customerIds)
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->get();

        /** @var \Illuminate\Support\Collection $groupedMapData */
        $groupedMapData = array_values(
            $customers
                ->map(fn(Customer $customer): array => [
                    'id' => $customer->id,
                    'name' => $customer->trading_name,
                    'lat' => (float) $customer->latitude,
                    'lng' => (float) $customer->longitude,
                    'roteiro' => $mappedCustomers[$customer->id]['itinerary'],
                ])
                ->groupBy('roteiro')
                ->toArray()
        );

        foreach ($groupedMapData as $key => $mapDataGroup) {
            $currentMapDataGroup = collect($mapDataGroup)->map(fn(array $mapData): array => [
                'id' => $mapData['id'],
                'name' => $mapData['name'],
                'lat' => $mapData['lat'],
                'lng' => $mapData['lng'],
                'roteiro' => $this->getItineraryColor((int) $key),
            ])->toArray();

            $this->mapData = array_merge($this->mapData, $currentMapDataGroup);
        }

        $this->dispatch('update-map-data', $this->mapData);
    }

    public function getItineraryColor(int $itinerary): string
    {
        switch ($itinerary) {
            case 0:
                return "blue";
            case 1:
                return "red";
            case 2:
                return "green";
            case 3:
                return "purple";
            case 4:
                return "yellow";
            case 5:
                return "orange";
            case 6:
                return "pink";
            case 7:
                return "silver";
            case 8:
                return "gold";
            case 9:
                return "brown";
            default:
                return "grey";
        }
    }
}
