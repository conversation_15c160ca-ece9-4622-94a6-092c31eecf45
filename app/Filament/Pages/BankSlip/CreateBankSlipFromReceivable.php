<?php

namespace App\Filament\Pages\BankSlip;

use App\Actions\BankSlip\GenerateBankSlipFromReceivable;
use App\Actions\BankSlip\Tecnospeed\Plugboleto\AutofixBankSlipsFromTecnospeedPlugboleto;
use App\Core\Http\Requests\Tokenizer;
use App\Enums\CustomerPreferredChargingMethodEnum;
use App\Models\BankAccountWallet;
use App\Models\Receivable;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Throwable;

class CreateBankSlipFromReceivable extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $title = 'Criar boleto de fatura';
    protected static ?string $slug = 'create-bank-slip';
    protected static string $view = 'filament.pages.bank-slip.create-bank-slip-from-receivable';

    public array $receivableIds;
    public int $bankAccountWalletId;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Mount the component.
     *
     * @param  \App\Models\Receivable $records
     * @return void
     */
    public function mount(string $records): void
    {
        $this->receivableIds = Tokenizer::decrypt($records);
    }

    /**
     * @inheritDoc
     */
    protected function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                Select::make('bankAccountWalletId')
                    ->label('Carteira')
                    ->lazy()
                    ->options(
                        BankAccountWallet::query()
                            ->with('bankAccount.bank')
                            ->get()
                            ->sortBy(['bank.name', 'bankAccount.branch_number'])
                            ->map(function (BankAccountWallet $bankAccountWallet): array {
                                return [
                                    'id' => $bankAccountWallet->id,
                                    'name' => "{$bankAccountWallet->bankAccount->bank->code} - {$bankAccountWallet->bankAccount->bank->name}"
                                        . " | ({$bankAccountWallet->bankAccount->branch_number}-{$bankAccountWallet->bankAccount->branch_digit} / "
                                        . "{$bankAccountWallet->bankAccount->number}-{$bankAccountWallet->bankAccount->digit})"
                                ];
                            })
                            ->pluck('name', 'id')
                    )
                    ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set): void {
                        $set('drawee_tax_id_number', BankAccountWallet::find($state)?->drawee_tax_id_number ?? '');
                    }),
                TextInput::make('drawee_tax_id_number')
                    ->label(__('bank_account_wallets.forms.fields.drawee_tax_id_number'))
                    ->disabled()
            ])
        ];
    }

    /**
     * Create the bank slip.
     *
     * @return void
     */
    public function createBankSlip()
    {
        if (collect($this->receivableIds)->count() === 0) {
            $this->receivableIds = Receivable::query()
                ->whereDoesntHave('bankSlips')
                ->whereRelation('customer', 'preferred_charging_method', CustomerPreferredChargingMethodEnum::BankSlip->value)
                ->get()
                ->pluck('id')
                ->toArray();
        }

        try {
            collect($this->receivableIds)->each(function (string $id): void {
                /** @var \App\Models\Receivable $receivable */
                $receivable = Receivable::find($id);

                if (count($this->receivableIds) > 1 && $receivable->bankSlips->count() > 0) {
                    return;
                }

                GenerateBankSlipFromReceivable::dispatch(
                    $receivable,
                    BankAccountWallet::find($this->bankAccountWalletId),
                    auth()->id()
                );
            });

            AutofixBankSlipsFromTecnospeedPlugboleto::dispatch(tenant('id'))
                ->onQueue(config('queue.default_names.integrations.tecnospeed.plugboleto.bank_slips.data_send'));

            success_notification(__('receivables.responses.generate_bank_slip.success'))->send();

            return redirect()->route('filament.app.resources.receivables.index');
        } catch (Throwable $th) {
            error($th);
            error_notification()->send();
        }
    }
}
