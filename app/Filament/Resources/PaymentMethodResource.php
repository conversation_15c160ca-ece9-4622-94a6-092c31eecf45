<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentMethodResource\Concerns\HandlesPaymentMethodResourceForm;
use App\Filament\Resources\PaymentMethodResource\Concerns\HandlesPaymentMethodResourceTable;
use App\Filament\Resources\PaymentMethodResource\Pages\ManagePaymentMethods;
use App\Models\PaymentMethod;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class PaymentMethodResource extends Resource
{
    use HandlesPaymentMethodResourceForm;
    use HandlesPaymentMethodResourceTable;

    protected static ?string $model = PaymentMethod::class;
    protected static ?string $modelLabel = 'forma de pagamento';
    protected static ?string $pluralModelLabel = 'formas de pagamento';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 5;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManagePaymentMethods::route('/')
        ];
    }
}
