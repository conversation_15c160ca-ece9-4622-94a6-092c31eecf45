<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Actions\Customer\DeleteCustomer;
use App\Filament\Resources\CustomerResource;
use App\Models\CustomerContact;
use App\Models\CustomerBasicStockStats;
use App\Models\CustomerCoupon;
use App\Models\CustomerPaymentRecoveryLog;
use App\Models\CustomerProduct;
use App\Models\CustomerTag;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Throwable;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make()->using(function () {
                try {
                    DeleteCustomer::run($this->record);
                    success_notification(__('customers.responses.delete.success'))->send();
                    return redirect()->route('filament.app.resources.customers.index');
                } catch (Throwable $th) {
                    error($th);
                    error_notification($th->getMessage())->send();
                }
            })
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $this->record->load([
            'city',
            'state',
            'deliveryCity',
            'deliveryState',
            'customerCustomerTags',
            'customerContacts.customerContactTypes',
            'customerBasicStockStats.product:id,name'
        ]);

        $data['address_city'] = $this->record?->city?->name;
        $data['address_state'] = $this->record?->state?->abbreviation;

        $data['items'] = $this->record->customerProducts->map(function (CustomerProduct $customerProduct) {
            return [
                'product_id' => $customerProduct->product_id,
                'quantity' => $customerProduct->quantity,
                'unit_amount' => $customerProduct->unit_amount,
                'total_amount' => $customerProduct->total_amount,
                'visible_in_collections' => $customerProduct->visible_in_collections,
                'print_product_id' => $customerProduct->print_product_id,
            ];
        })->toArray();

        $data['tags'] = array_keys(
            CustomerTag::query()
                ->whereIn('id', $this->record->customerCustomerTags->pluck('customer_tag_id')->toArray())
                ->get()
                ->pluck('name', 'id')
                ->toArray()
        );

        $data['customer_coupons'] = $this->record->customerCoupons
            ->map(fn(CustomerCoupon $customerCoupon): array => $customerCoupon->toArray())
            ->toArray();

        $data['customer_contacts'] = $this->record->customerContacts
            ->map(function (CustomerContact $customerContact): array {
                $contactArray = $customerContact->toArray();
                $contactArray['types'] = $customerContact->customerContactTypes->pluck('type')->toArray();
                return $contactArray;
            })
            ->toArray();

        $data['customer_payment_recovery_logs'] = $this->record->customerPaymentRecoveryLogs
            ->filter(fn(CustomerPaymentRecoveryLog $customerPaymentRecoveryLog): bool => !is_null($customerPaymentRecoveryLog->receivable))
            ->map(function (CustomerPaymentRecoveryLog $customerPaymentRecoveryLog): array {
                return [
                    'receivable_id' => $customerPaymentRecoveryLog?->receivable?->document_id ?? '',
                    'type' => $customerPaymentRecoveryLog?->friendly_type ?? '',
                    'created_at' => $customerPaymentRecoveryLog ? format_datetime($customerPaymentRecoveryLog?->created_at) : ''
                ];
            })
            ->toArray();

        $stats = $this->record->customerBasicStockStats
            ->map(fn(CustomerBasicStockStats $customerBasicStockStats): array => [
                'weekday' => $customerBasicStockStats->weekday,
                'amount' => (float) $customerBasicStockStats->amount,
                'stats_type' => $customerBasicStockStats->stats_type,
                'product_name' => $customerBasicStockStats->product->name,
            ])
            ->sortBy('weekday')
            ->groupBy(['weekday', 'product_name'])
            ->toArray();

        $data['stats_day_1'] = !isset($stats[1])
            ? null
            : collect($stats[1])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        $data['stats_day_2'] = !isset($stats[2])
            ? null
            : collect($stats[2])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        $data['stats_day_3'] = !isset($stats[3])
            ? null
            : collect($stats[3])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        $data['stats_day_4'] = !isset($stats[4])
            ? null
            : collect($stats[4])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        $data['stats_day_5'] = !isset($stats[5])
            ? null
            : collect($stats[5])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        $data['stats_day_6'] = !isset($stats[6])
            ? null
            : collect($stats[6])->map(function (array $statsItem): array {
                return array_values(collect($statsItem)->sortBy('stats_type')->toArray());
            });

        return $data;
    }
}
