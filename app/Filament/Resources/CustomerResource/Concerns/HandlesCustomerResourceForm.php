<?php

namespace App\Filament\Resources\CustomerResource\Concerns;

use App\Enums\CustomerContactTypeEnum;
use App\Enums\CustomerDefaultBillingPeriodEnum;
use App\Enums\CustomerDefaultDeliveryModelEnum;
use App\Enums\CustomerDefaultTaxConditionEnum;
use App\Enums\CustomerPreferredChargingMethodEnum;
use App\Integrations\Api\Receita\Services\ReceitaService;
use App\Integrations\Api\ViaCep\Services\ViaCepService;
use App\Models\BankAccountWallet;
use App\Models\City;
use App\Models\Collection;
use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Models\CustomerProduct;
use App\Models\CustomerTag;
use App\Models\Delivery;
use App\Models\Index;
use App\Models\LendingStockAdjustment;
use App\Models\Product;
use App\Models\State;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Support\RawJs;
use Illuminate\Support\HtmlString;
use Throwable;

trait HandlesCustomerResourceForm
{
    public static function getFormSchema(): array
    {
        $customerProductsCount = 0;
        $componentItemsCount = 0;

        return [
            Hidden::make('replicate_main_address_to_delivery_address'),
            Grid::make(1)->schema([
                Tabs::make('customer_tabs')->schema([
                    Tab::make('Identificação')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('tax_id_number')
                                ->required()
                                ->label(__('customers.forms.fields.tax_id_number'))
                                ->columnSpan(1)
                                ->maxLength(18)
                                ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                    $input.length >= 14 ? '99.999.999/9999-99' : '999.999.999-99'
                                JS))
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set) {
                                    try {
                                        $companyDetails = (new ReceitaService())->getCompanyDetails($state);
                                    } catch (Throwable $th) {
                                        error($th);
                                        error_notification('Não foi possível obter os dados da receita.')->send();
                                        return;
                                    }

                                    if (isset($companyDetails->status) && $companyDetails->status !== 'OK') {
                                        return;
                                    }

                                    /** @var \App\Models\City $city */
                                    $city = City::query()
                                        ->where('name', $companyDetails->municipio)
                                        ->first();

                                    /** @var \App\Models\State $addressState */
                                    $addressState = State::query()
                                        ->where('abbreviation', $companyDetails->uf)
                                        ->first();

                                    $set('name', $companyDetails->nome);
                                    $set('address_zipcode', mask_zipcode(get_numbers($companyDetails->cep)));
                                    $set('address_address', $companyDetails->logradouro);
                                    $set('address_number', $companyDetails->numero);
                                    $set('address_additional_info', $companyDetails->complemento);
                                    $set('address_district', $companyDetails->bairro);
                                    $set('address_city_id', $city->id);
                                    $set('address_city', $city->name);
                                    $set('address_state_id', $addressState->id);
                                    $set('address_state', $addressState->abbreviation);
                                }),
                        ]),
                        Grid::make(2)->schema([
                            TextInput::make('name')
                                ->required()
                                ->label(__('customers.forms.fields.name')),
                            TextInput::make('trading_name')
                                ->required()
                                ->label(__('customers.forms.fields.trading_name')),
                        ]),
                        Grid::make(4)->schema([
                            Select::make('exempt_from_state_registration')
                                ->required()
                                ->label(__('customers.forms.fields.exempt_from_state_registration'))
                                ->selectablePlaceholder(false)
                                ->options([
                                    false => 'Não',
                                    true => 'Sim'
                                ])
                                ->reactive()
                                ->afterStateUpdated(
                                    fn(?string $state, \Filament\Forms\Set $set) => $state
                                        ? $set('state_registration', 'ISENTO')
                                        : $set('state_registration', null)
                                )
                                ->columnSpan(1),
                            TextInput::make('state_registration')
                                ->required(fn(\Filament\Forms\Get $get) => !$get('exempt_from_state_registration'))
                                ->disabled(fn(\Filament\Forms\Get $get) => $get('exempt_from_state_registration'))
                                ->label(__('customers.forms.fields.state_registration'))
                                ->columnSpan(1),
                            TextInput::make('city_registration')
                                ->label(__('customers.forms.fields.city_registration'))
                                ->columnSpan(2)
                        ]),
                        Fieldset::make('addresses_fieldset')
                            ->label('Endereços')
                            ->schema([
                                Section::make('Endereço principal')
                                    ->compact()
                                    ->collapsible()
                                    ->schema([
                                        Grid::make(4)->schema([
                                            TextInput::make('address_zipcode')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_zipcode'))
                                                ->mask('99999-999')
                                                ->columnSpan(1)
                                                ->lazy()
                                                ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set) {
                                                    if (strlen($state) !== 9) {
                                                        return;
                                                    }

                                                    $fullAddress = (new ViaCepService())->getFullAddress($state);

                                                    $city = City::find($fullAddress->ibge);

                                                    $addressState = State::query()
                                                        ->where('abbreviation', $fullAddress->uf)
                                                        ->first();

                                                    $set('address_address', $fullAddress->logradouro);
                                                    $set('address_district', $fullAddress->bairro);
                                                    $set('address_city_id', $city->id);
                                                    $set('address_city', $city->name);
                                                    $set('address_state_id', $addressState->id);
                                                    $set('address_state', $addressState->abbreviation);
                                                })
                                        ]),
                                        Grid::make(4)->schema([
                                            TextInput::make('address_address')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_address'))
                                                ->columnSpan(2),
                                            TextInput::make('address_number')
                                                ->label(__('customers.forms.fields.address_number'))
                                                ->columnSpan(1),
                                            TextInput::make('address_additional_info')
                                                ->label(__('customers.forms.fields.address_additional_info'))
                                                ->columnSpan(1),
                                        ]),
                                        Grid::make(4)->schema([
                                            TextInput::make('address_district')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_district'))
                                                ->columnSpan(2),
                                            Hidden::make('address_city_id'),
                                            TextInput::make('address_city')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_city'))
                                                ->disabled()
                                                ->columnSpan(1),
                                            Hidden::make('address_state_id'),
                                            TextInput::make('address_state')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_state'))
                                                ->disabled()
                                                ->columnSpan(1),
                                        ]),
                                    ]),
                                Section::make('Endereço de entrega')
                                    ->compact()
                                    ->collapsible()
                                    ->schema([
                                        Grid::make(4)->schema([
                                            TextInput::make('delivery_address_zipcode')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_zipcode'))
                                                ->mask('99999-999')
                                                ->columnSpan(1)
                                                ->lazy()
                                                ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set) {
                                                    if (strlen($state) !== 9) {
                                                        return;
                                                    }

                                                    $fullAddress = (new ViaCepService())->getFullAddress($state);

                                                    $city = City::find($fullAddress->ibge);

                                                    $addressState = State::query()
                                                        ->where('abbreviation', $fullAddress->uf)
                                                        ->first();

                                                    $set('delivery_address_address', $fullAddress->logradouro);
                                                    $set('delivery_address_district', $fullAddress->bairro);
                                                    $set('delivery_address_city_id', $city->id);
                                                    $set('delivery_address_city', $city->name);
                                                    $set('delivery_address_state_id', $addressState->id);
                                                    $set('delivery_address_state', $addressState->abbreviation);
                                                })
                                        ]),
                                        Grid::make(4)->schema([
                                            TextInput::make('delivery_address_address')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_address'))
                                                ->columnSpan(2),
                                            TextInput::make('delivery_address_number')
                                                ->label(__('customers.forms.fields.address_number'))
                                                ->columnSpan(1),
                                            TextInput::make('delivery_address_additional_info')
                                                ->label(__('customers.forms.fields.address_additional_info'))
                                                ->columnSpan(1),
                                        ]),
                                        Grid::make(4)->schema([
                                            TextInput::make('delivery_address_district')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_district'))
                                                ->columnSpan(2),
                                            Hidden::make('delivery_address_city_id'),
                                            TextInput::make('delivery_address_city')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_city'))
                                                ->disabled()
                                                ->columnSpan(1),
                                            Hidden::make('delivery_address_state_id'),
                                            TextInput::make('delivery_address_state')
                                                ->required()
                                                ->label(__('customers.forms.fields.address_state'))
                                                ->disabled()
                                                ->columnSpan(1),
                                        ]),
                                    ]),

                            ]),
                    ]),
                    Tab::make('Contatos')->schema([
                        Grid::make(1)->schema([
                            TextInput::make('email')
                                ->required()
                                ->label(__('customers.forms.fields.email'))
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('phone_1')
                                ->label(__('customers.forms.fields.phone_1'))
                                ->mask('(99) 99999-9999')
                                ->required()
                                ->rule('min:10')
                                ->columnSpan(1),
                            TextInput::make('in_charge_person_1')
                                ->label(__('customers.forms.fields.in_charge_person_1'))
                                ->required()
                                ->columnSpan(3)
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('phone_2')
                                ->label(__('customers.forms.fields.phone_2'))
                                ->mask('(99) 99999-9999')
                                ->rule('min:10')
                                ->columnSpan(1),
                            TextInput::make('in_charge_person_2')
                                ->label(__('customers.forms.fields.in_charge_person_2'))
                                ->columnSpan(3)
                        ]),
                        TableRepeater::make('customer_contacts')
                            ->label('Contatos')
                            ->columns(5)
                            ->addActionLabel('Adicionar contato')
                            ->defaultItems(0)
                            ->headers([
                                Header::make(__('customers.forms.fields.customer_contacts.types')),
                                Header::make(__('customers.forms.fields.customer_contacts.name')),
                                Header::make(__('customers.forms.fields.customer_contacts.email')),
                                Header::make(__('customers.forms.fields.customer_contacts.phone')),
                            ])
                            ->schema([
                                Hidden::make('id'),
                                Select::make('types')
                                    ->options(CustomerContactTypeEnum::getTranslated())
                                    ->multiple()
                                    ->required()
                                    ->placeholder('Selecione os tipos de contato'),
                                TextInput::make('name')
                                    ->required(),
                                TextInput::make('email')
                                    ->required(),
                                TextInput::make('phone')
                                    ->required(),
                            ])
                    ]),
                    Tab::make('Itens')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('service_started_at')
                                ->label(__('customers.forms.fields.service_started_at'))
                                ->type('date')
                        ]),
                        Grid::make(1)->schema([
                            TableRepeater::make('items')
                                ->hiddenLabel()
                                ->columns(5)
                                ->addActionLabel('Adicionar produto')
                                ->defaultItems(0)
                                ->deletable(false)
                                ->afterStateHydrated(function (TableRepeater $component, ?Customer $record) use (&$customerProductsCount, &$componentItemsCount) {
                                    if ($customerProductsCount === 0 && !is_null($record)) {
                                        if ($record->relationLoaded('customerProducts')) {
                                            $record->load('customerProducts');
                                        }

                                        $customerProductsCount = $record->customerProducts->count();
                                    }

                                    $componentItemsCount = $component->getItemsCount();
                                })
                                ->headers([
                                    Header::make(__('contracts.forms.fields.contract_item_id'))
                                        ->width('35%'),
                                    Header::make(__('contracts.forms.fields.contract_item_quantity'))
                                        ->width('10%'),
                                    Header::make(__('contracts.forms.fields.contract_item_amount'))
                                        ->width('10%'),
                                    Header::make(__('contracts.forms.fields.contract_item_visible_in_collections'))
                                        ->width('10%'),
                                    Header::make(__('contracts.forms.fields.contract_item_print_product_id'))
                                        ->width('35%'),
                                ])
                                ->schema([
                                    Select::make('product_id')
                                        ->required()
                                        ->autofocus(function () use (&$customerProductsCount, &$componentItemsCount): bool {
                                            return $componentItemsCount !== $customerProductsCount;
                                        })
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search) {
                                            return Product::query()
                                                ->where('code', $search)
                                                ->orWhere('name', 'like', "%$search%")
                                                ->pluck('name', 'id');
                                        })
                                        ->afterStateHydrated(function (Select $component, ?Customer $record) use (&$componentItemsCount, &$customerProductsCount) {
                                            self::handleCustomerProduct($component, $record, $customerProductsCount, $componentItemsCount);
                                        })
                                        ->afterStateUpdated(function (Select $component, ?Customer $record) use (&$componentItemsCount, &$customerProductsCount) {
                                            self::handleCustomerProduct($component, $record, $customerProductsCount, $componentItemsCount);
                                        })
                                        ->getOptionLabelUsing(function ($value, Customer $record): ?string {
                                            $customerProduct = $record->customerProducts
                                                ->filter(fn(CustomerProduct $customerProduct): bool => $customerProduct->product_id === $value)
                                                ->first();

                                            if (!is_null($customerProduct->product)) {
                                                return $customerProduct->product->name;
                                            }

                                            return Product::query()
                                                ->withTrashed()
                                                ->find($value)
                                                ->name;
                                        })
                                        ->columnSpan(2),
                                    TextInput::make('quantity')
                                        ->required()
                                        ->numeric(),
                                    TextInput::make('unit_amount')
                                        ->required()
                                        ->formatStateUsing(fn(?string $state): string => mask_money($state))
                                        ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                            'R$ ' + $money($input, ',')
                                        JS)),
                                    Select::make('visible_in_collections')
                                        ->selectablePlaceholder(false)
                                        ->default(true)
                                        ->options([
                                            true => 'Sim',
                                            false => 'Não'
                                        ]),
                                    Select::make('print_product_id')
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search) {
                                            return Product::query()
                                                ->where('code', $search)
                                                ->orWhere('name', 'like', "%$search%")
                                                ->pluck('name', 'id');
                                        })
                                        ->getOptionLabelUsing(function ($value, Customer $record): ?string {
                                            return $record->customerProducts
                                                ->filter(fn(CustomerProduct $customerProduct): bool => $customerProduct->print_product_id === $value)
                                                ->first()
                                                ->printProduct
                                                ->name;
                                        }),
                                ]),
                        ]),
                    ]),
                    Tab::make('Informações de faturamento')->schema([
                        Grid::make(1)->schema([
                            Select::make('customer_group_id')
                                ->label(__('customers.forms.fields.customer_group_id'))
                                ->searchable()
                                ->reactive()
                                ->getSearchResultsUsing(function (?string $search): \Illuminate\Support\Collection {
                                    return CustomerGroup::query()
                                        ->where('name', 'like', "%$search%")
                                        ->get()
                                        ->map(fn(CustomerGroup $customerGroup) => [
                                            'id' => $customerGroup->id,
                                            'name' => $customerGroup->name
                                        ])
                                        ->pluck('name', 'id');
                                })
                                ->getOptionLabelUsing(function (Customer $record): ?string {
                                    return $record->customerGroup->name;
                                })
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('billing_email')
                                ->required()
                                ->label(__('customers.forms.fields.billing_email'))
                                ->columnSpan(3),
                            TextInput::make('billing_phone')
                                ->label(__('customers.forms.fields.billing_phone'))
                                ->mask('(99) 99999-9999')
                                ->required()
                                ->rule('min:10')
                                ->columnSpan(1),
                        ]),
                        Grid::make(4)->schema([
                            Select::make('default_billing_period')
                                ->label(__('customers.forms.fields.default_billing_period'))
                                ->required()
                                ->options(CustomerDefaultBillingPeriodEnum::getTranslated()),
                            TextInput::make('minimum_billing_amount')
                                ->label(__('customers.forms.fields.minimum_billing_amount'))
                                ->formatStateUsing(fn(?Customer $record): string => mask_money($record?->minimum_billing_amount ?? 0))
                                ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                    'R$ ' + $money($input, ',')
                                JS)),
                            Select::make('index_id')
                                ->label(__('customers.forms.fields.index_id'))
                                ->options(Index::query()->orderBy('name')->get()->mapWithKeys(fn(Index $index): array => [$index->id => $index->name])->toArray())
                                ->required(),
                            Select::make('preferred_charging_method')
                                ->label(__('customers.forms.fields.preferred_charging_method'))
                                ->required()
                                ->options(CustomerPreferredChargingMethodEnum::getTranslated()),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('payment_recovery_day_count')
                                ->label(__('customers.forms.fields.payment_recovery_day_count'))
                                ->required()
                                ->default(31),
                            Select::make('default_tax_condition')
                                ->label(__('customers.forms.fields.default_tax_condition'))
                                ->placeholder('Escolha uma opção')
                                ->options(CustomerDefaultTaxConditionEnum::getTranslated())
                                ->columnSpan(1),
                            TextInput::make('default_due_day')
                                ->label(__('customers.forms.fields.default_due_day'))
                                ->numeric()
                                ->required(),
                            Select::make('default_bank_account_wallet_id')
                                ->label(__('customers.forms.fields.default_bank_account_wallet_id'))
                                ->options(
                                    BankAccountWallet::query()
                                        ->get()
                                        ->mapWithKeys(fn(BankAccountWallet $bankAccountWallet): array => [$bankAccountWallet->id => $bankAccountWallet->full_number])
                                        ->toArray()
                                )
                                ->required(),
                        ])
                    ]),
                    Tab::make('Configurações operacionais')->schema([
                        Grid::make(1)->schema([
                            TextInput::make('operation_email')
                                ->required()
                                ->label(__('customers.forms.fields.operation_email'))
                        ]),
                        Grid::make(4)->schema([
                            Select::make('default_delivery_model')
                                ->label(__('customers.forms.fields.default_delivery_model'))
                                ->required()
                                ->options(CustomerDefaultDeliveryModelEnum::getTranslated()),
                            TextInput::make('previous_collection_count')
                                ->label(__('customers.forms.fields.previous_collection_count'))
                                ->required()
                                ->numeric()
                                ->default(0),
                            TextInput::make('stock_safety_percentage')
                                ->label(__('customers.forms.fields.stock_safety_percentage'))
                                ->formatStateUsing(fn(?Customer $record): string => $record->friendly_stock_safety_percentage ?? 0)
                                ->mask(function () {
                                    return \Filament\Support\RawJs::make(<<<'JS'
                                        $money($input, ',') + '%'
                                    JS);
                                })
                        ])
                    ]),
                    Tab::make('Tags')->schema([
                        Select::make('tags')
                            ->label(__('customers.forms.fields.tags'))
                            ->multiple()
                            ->options(CustomerTag::orderBy('name')->get()->pluck('name', 'id')->toArray())
                    ]),
                    Tab::make('Cupons')
                        ->hiddenOn('create')
                        ->schema([
                            Grid::make(1)->schema([
                                TableRepeater::make('customer_coupons')
                                    ->hiddenLabel()
                                    ->columns(7)
                                    ->addActionLabel('Adicionar cupom')
                                    ->defaultItems(0)
                                    ->headers([
                                        Header::make(__('customers.forms.fields.customer_coupons.type')),
                                        Header::make(__('customers.forms.fields.customer_coupons.amount')),
                                        Header::make(__('customers.forms.fields.customer_coupons.starting_at')),
                                        Header::make(__('customers.forms.fields.customer_coupons.expires_at')),
                                        Header::make(__('customers.forms.fields.customer_coupons.apply_to_minimum_amount')),
                                        Header::make(__('customers.forms.fields.customer_coupons.apply_to_regular_amount')),
                                        Header::make(__('customers.forms.fields.customer_coupons.additional_info')),
                                    ])
                                    ->schema([
                                        Hidden::make('id'),
                                        Select::make('type')
                                            ->options([
                                                'amount' => 'Valor',
                                                'percentage' => 'Percentual',
                                            ])
                                            ->required()
                                            ->reactive(),
                                        TextInput::make('amount')
                                            ->required()
                                            ->mask(function (Get $get): RawJs {
                                                if ($get('type') === 'percentage') {
                                                    return RawJs::make(<<<'JS'
                                                        $money($input) + '%'
                                                    JS);
                                                }

                                                return RawJs::make(<<<'JS'
                                                    'R$ ' + $money($input, ',')
                                                JS);
                                            }),
                                        TextInput::make('starting_at')
                                            ->required()
                                            ->type('date')
                                            ->default(now()->format('Y-m-d')),
                                        TextInput::make('expires_at')
                                            ->required()
                                            ->type('date'),
                                        Toggle::make('apply_to_minimum_amount')
                                            ->default(false),
                                        Toggle::make('apply_to_regular_amount')
                                            ->default(false),
                                        TextInput::make('additional_info'),
                                    ]),
                            ]),
                        ]),
                    Tab::make('Histórico de cobrança')
                        ->hiddenOn('create')
                        ->schema([
                            Grid::make(1)->schema([
                                TableRepeater::make('customer_payment_recovery_logs')
                                    ->label('')
                                    ->addable(false)
                                    ->deletable(false)
                                    ->reorderable(false)
                                    ->columns(3)
                                    ->headers([
                                        Header::make('Fatura'),
                                        Header::make('Via'),
                                        Header::make('Enviado em'),
                                    ])
                                    ->schema([
                                        TextInput::make('receivable_id')
                                            ->disabled(),
                                        TextInput::make('type')
                                            ->disabled(),
                                        TextInput::make('created_at')
                                            ->disabled(),
                                    ])
                            ]),
                        ]),
                    Tab::make('Histórico')
                        ->hiddenOn('create')
                        ->schema([
                            Grid::make(1)->schema([
                                Repeater::make('customer_histories')
                                    ->relationship('customerHistories')
                                    ->label('')
                                    ->addable(false)
                                    ->deletable(false)
                                    ->reorderable(false)
                                    ->columns(2)
                                    ->schema([
                                        Grid::make(2)->schema([
                                            TextInput::make('operation_id')
                                                ->label(__('customers.forms.fields.customer_histories.operation_id'))
                                                ->disabled(),
                                            TextInput::make('operation_type')
                                                ->label(__('customers.forms.fields.customer_histories.operation_type'))
                                                ->disabled()
                                                ->formatStateUsing(function (?string $state): string {
                                                    return match ($state) {
                                                        Collection::class => 'Coleta',
                                                        Delivery::class => 'Entrega',
                                                        LendingStockAdjustment::class => 'Ajuste de estoque',
                                                        default => ''
                                                    };
                                                })
                                        ]),
                                        Grid::make(1)->schema([
                                            Textarea::make('history')
                                                ->label(__('customers.forms.fields.customer_histories.history'))
                                                ->disabled()
                                        ]),
                                    ]),
                            ]),
                        ]),
                    Tab::make('Estatísticas')
                        ->hiddenOn('create')
                        ->schema([
                            Grid::make(1)->schema([
                                Section::make('Segunda-feira')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_1')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_1'))),
                                        ]),
                                    ]),
                                Section::make('Terça-feira')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_2')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_2'))),
                                        ]),
                                    ]),
                                Section::make('Quarta-feira')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_3')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_3'))),
                                        ]),
                                    ]),
                                Section::make('Quinta-feira')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_4')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_4'))),
                                        ]),
                                    ]),
                                Section::make('Sexta-feira')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_5')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_5'))),
                                        ]),
                                    ]),
                                Section::make('Sábado')
                                    ->collapsible()
                                    ->compact()
                                    ->schema([
                                        Grid::make(1)->schema([
                                            Placeholder::make('stats_day_6')
                                                ->label('')
                                                ->content(fn(\Filament\Forms\Get $get): HtmlString => self::getStatsContent($get('stats_day_6'))),
                                        ]),
                                    ]),
                            ]),
                        ]),
                ]),
            ]),
        ];
    }

    /**
     * Handle the customer product post processing.
     *
     * @param  \App\Core\Filament\Form\Fields\Select $component
     * @param  \App\Models\Customer|null $customer
     * @param  int $componentItemsCount
     * @param  int $customerProductsCount
     * @return void
     */
    protected static function handleCustomerProduct(
        Select $component,
        ?Customer $customer,
        int &$componentItemsCount,
        int &$customerProductsCount
    ): void {
        if (!is_null($customer)) {
            if (!$customer->relationLoaded('customerProducts')) {
                $customer->load('customerProducts');
            }

            $customerProductsCount = $customer->customerProducts->count();
        }

        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
    }

    /**
     * Get the stats content.
     *
     * @param  array|null $data
     * @return \Illuminate\Support\HtmlString
     */
    public static function getStatsContent(?array $data): HtmlString
    {
        if (is_null($data)) {
            return new HtmlString();
        }

        $lines = collect($data)->implode(function (array $stats, string $key): string {
            $formattedStatsItem = collect($stats)->implode(function (array $statsItem): string {
                return "<td style='border: 1px solid black; text-align: center'>" . (int) $statsItem['amount'] . '</td>';
            });

            return "
            <tr>
                <td style='border: 1px solid black; text-align: center'>$key</td>
                $formattedStatsItem
            </tr>
        ";
        });

        $table = "
        <table style='width: 100%'>
            <thead>
                <th style='border: 1px solid black;'>Produto</th>
                <th style='border: 1px solid black; width: 120px'>Média</th>
                <th style='border: 1px solid black; width: 120px'>Máximo</th>
                <th style='border: 1px solid black; width: 120px'>Mínimo</th>
                <th style='border: 1px solid black; width: 120px'>Desvio padrão</th>
            </thead>
            <tbody>
                $lines
            </tbody>
        </table>
    ";

        return new HtmlString($table);
    }
}
