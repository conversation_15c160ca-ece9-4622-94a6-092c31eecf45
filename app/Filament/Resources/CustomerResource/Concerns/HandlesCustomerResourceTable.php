<?php

namespace App\Filament\Resources\CustomerResource\Concerns;

use App\Actions\Customer\ActivateCustomer;
use App\Actions\Customer\DeleteCustomer;
use App\Actions\Customer\InactivateCustomer;
use App\Actions\Customer\ReadjustCustomerProducts;
use App\Models\Customer;
use App\Models\Index;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Throwable;

trait HandlesCustomerResourceTable
{
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')
                ->label(__('customers.forms.fields.id'))
                ->sortable(),
            TextColumn::make('name')
                ->label(__('customers.forms.fields.name'))
                ->sortable(),
            TextColumn::make('trading_name')->label(__('customers.forms.fields.trading_name'))
                ->sortable(),
            TextColumn::make('tax_id_number')
                ->label(__('customers.forms.fields.tax_id_number'))
                ->sortable()
                ->formatStateUsing(function (string $state): string {
                    return strlen($state) > 11
                        ? mask_cnpj($state)
                        : mask_cpf($state);
                }),
            IconColumn::make('active')
                ->label(__('customers.forms.fields.active'))
                ->boolean()
        ];
    }

    public static function getTableFilters(): array
    {
        return [
            Filter::make('name')
                ->form([TextInput::make('name')->label(__('customers.forms.fields.name'))])
                ->query(fn(Builder $query, array $data): Builder => $query->where('name', 'like', "%{$data['name']}%")),
            Filter::make('trading_name')
                ->form([TextInput::make('trading_name')->label(__('customers.forms.fields.trading_name'))])
                ->query(fn(Builder $query, array $data): Builder => $query->where('trading_name', 'like', "%{$data['trading_name']}%")),
            Filter::make('tax_id_number')
                ->form([TextInput::make('tax_id_number')->label(__('customers.forms.fields.tax_id_number'))])
                ->query(fn(Builder $query, array $data): Builder => $query->where('tax_id_number', 'like', '%' . get_numbers($data['tax_id_number']) . '%')),
            SelectFilter::make('active')
                ->label(__('customers.forms.fields.active'))
                ->options([
                    true => 'Sim',
                    false => 'Não',
                ]),
            TernaryFilter::make('Lançamentos excluídos')
                ->placeholder('Retirar lançamentos excluídos')
                ->trueLabel('Incluir lançamentos excluídos')
                ->falseLabel('Somente lançamentos excluídos')
                ->queries(
                    true: fn(Builder $query) => $query->withTrashed(),
                    false: fn(Builder $query) => $query->onlyTrashed(),
                    blank: fn(Builder $query) => $query->withoutTrashed(),
                ),
            SelectFilter::make('readjustment_month')
                ->form([
                    Select::make('readjustment_month')
                        ->label('Mês reajuste')
                        ->options([
                            1 => 'Janeiro',
                            2 => 'Fevereiro',
                            3 => 'Março',
                            4 => 'Abril',
                            5 => 'Maio',
                            6 => 'Junho',
                            7 => 'Julho',
                            8 => 'Agosto',
                            9 => 'Setembro',
                            10 => 'Outubro',
                            11 => 'Novembro',
                            12 => 'Dezembro',
                        ]),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->when(isset($data['readjustment_month']) && !is_null($data['readjustment_month']), function (Builder $query) use ($data): Builder {
                        return $query->whereHas('customerProductsReadjustments', fn(Builder $query): Builder => $query->whereMonth('new_ended_at', $data['readjustment_month']));
                    });
                }),
            SelectFilter::make('readjustment_year')
                ->form([
                    Select::make('readjustment_year')
                        ->label('Ano reajuste')
                        ->options(collect(range(2023, now()->year))->mapWithKeys(fn(int $year): array => [$year => $year]))
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->when(isset($data['readjustment_year']) && !is_null($data['readjustment_year']), function (Builder $query) use ($data): Builder {
                        return $query->whereHas('customerProductsReadjustments', fn(Builder $query): Builder => $query->whereYear('new_ended_at', $data['readjustment_year']));
                    });
                }),
        ];
    }

    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                EditAction::make(),
                Action::make('Ativar')
                    ->action(function (Customer $record) {
                        try {
                            ActivateCustomer::run($record);
                            success_notification(__('customers.responses.activate.success'))->send();
                        } catch (Throwable) {
                            error_notification()->send();
                        }
                    })
                    ->visible(fn(Customer $record): bool => !$record->active)
                    ->icon('heroicon-o-play')
                    ->requiresConfirmation(),
                Action::make('Inativar')
                    ->action(function (Customer $record) {
                        try {
                            InactivateCustomer::run($record);
                            success_notification(__('customers.responses.inactivate.success'))->send();
                        } catch (Throwable $th) {
                            error($th);
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->visible(fn(Customer $record): bool => $record->active)
                    ->icon('heroicon-o-pause')
                    ->requiresConfirmation(),
                DeleteAction::make()
                    ->using(function (Customer $record) {
                        try {
                            DeleteCustomer::run($record);
                            success_notification(__('customers.responses.delete.success'))->send();
                            return redirect()->route('filament.app.resources.customers.index');
                        } catch (Throwable $th) {
                            error($th);
                            error_notification($th->getMessage())->send();
                        }
                    })
            ])
        ];
    }

    public static function getTableBulkActions(): array
    {
        return [
            BulkAction::make('readjust_selected')
                ->label('Reajustar em lote')
                ->form([
                    Grid::make(2)->schema([
                        Select::make('index_id')
                            ->label('Índice a ser utilizado')
                            ->options(array_merge([
                                0 => 'Índice de cada cliente',
                            ], Index::query()->orderBy('name')->get()->mapWithKeys(fn(Index $index): array => [$index->id => $index->name])->toArray()))
                            ->default(0)
                            ->selectablePlaceholder(false),
                        TextInput::make('readjustment_month_count')
                            ->label('Quantidade de meses')
                            ->default(12)
                            ->numeric()
                            ->required(),
                    ]),
                    Grid::make(1)->schema([
                        Toggle::make('readjust_amounts')
                            ->label('Reajustar valores?')
                            ->default(true),
                    ]),
                ])
                ->action(function (Collection $records, array $data) {
                    try {
                        ReadjustCustomerProducts::dispatch($records->pluck('id')->toArray(), $data['index_id'], $data['readjustment_month_count'], $data['readjust_amounts'], auth()->id());
                        success_notification('Os preços estão sendo reajustados em segundo plano e você receberá uma notificação ao término do processo.')->send();
                    } catch (Throwable $th) {
                        error($th);
                        error_notification('Não foi possível executar o reajuste de preços neste momento. Tente novamente mais tarde.')->send();
                    }
                })
                ->deselectRecordsAfterCompletion(),
            DeleteBulkAction::make()
        ];
    }
}
