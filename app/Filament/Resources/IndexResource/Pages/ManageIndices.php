<?php

namespace App\Filament\Resources\IndexResource\Pages;

use App\Filament\Resources\IndexResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageIndices extends ManageRecords
{
    protected static string $resource = IndexResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
