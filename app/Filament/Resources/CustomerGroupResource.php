<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerGroupResource\Concerns\HandlesCustomerGroupResourceForm;
use App\Filament\Resources\CustomerGroupResource\Concerns\HandlesCustomerGroupResourceTable;
use App\Filament\Resources\CustomerGroupResource\Pages\ManageCustomerGroups;
use App\Models\CustomerGroup;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class CustomerGroupResource extends Resource
{
    use HandlesCustomerGroupResourceForm;
    use HandlesCustomerGroupResourceTable;

    protected static ?string $model = CustomerGroup::class;
    protected static ?string $modelLabel = 'grupo de clientes';
    protected static ?string $pluralModelLabel = 'grupos de clientes';
    protected static ?string $navigationGroup = 'Clientes';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageCustomerGroups::route('/'),
        ];
    }
}
