<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerTagResource\Concerns\HandlesCustomerTagResourceForm;
use App\Filament\Resources\CustomerTagResource\Concerns\HandlesCustomerTagResourceTable;
use App\Filament\Resources\CustomerTagResource\Pages\ManageCustomerTags;
use App\Models\CustomerTag;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class CustomerTagResource extends Resource
{
    use HandlesCustomerTagResourceForm;
    use HandlesCustomerTagResourceTable;

    protected static ?string $model = CustomerTag::class;
    protected static ?string $modelLabel = 'tag de cliente';
    protected static ?string $pluralModelLabel = 'tags de cliente';
    protected static ?string $navigationGroup = 'Clientes';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageCustomerTags::route('/'),
        ];
    }
}
