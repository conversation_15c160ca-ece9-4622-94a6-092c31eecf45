<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReceivableResource\Concerns\HandlesReceivableResourceForm;
use App\Filament\Resources\ReceivableResource\Concerns\HandlesReceivableResourceTable;
use App\Filament\Resources\ReceivableResource\Pages;
use App\Filament\Resources\ReceivableResource\Widgets\ReceivablesOverview;
use App\Models\Receivable;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;

class ReceivableResource extends Resource
{
    use HandlesReceivableResourceForm;
    use HandlesReceivableResourceTable;

    protected static ?string $model = Receivable::class;
    protected static ?string $modelLabel = 'fatura';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function getWidgets(): array
    {
        return [
            ReceivablesOverview::class,
        ];
    }

    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters(), layout: FiltersLayout::AboveContent)
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions())
            ->persistFiltersInSession();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReceivables::route('/'),
            'edit' => Pages\EditReceivable::route('/{record}/edit'),
            'view' => Pages\ViewReceivable::route('/{record}'),
        ];
    }
}
