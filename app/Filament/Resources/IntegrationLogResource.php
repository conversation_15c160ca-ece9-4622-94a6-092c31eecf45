<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IntegrationLogResource\Concerns\HandlesIntegrationLogResourceForm;
use App\Filament\Resources\IntegrationLogResource\Concerns\HandlesIntegrationLogResourceTable;
use App\Filament\Resources\IntegrationLogResource\Pages\ManageIntegrationLogs;
use App\Models\IntegrationLog;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class IntegrationLogResource extends Resource
{
    use HandlesIntegrationLogResourceForm;
    use HandlesIntegrationLogResourceTable;

    protected static ?string $model = IntegrationLog::class;
    protected static ?string $modelLabel = 'Log de integração';
    protected static ?string $pluralModelLabel = 'Logs de integração';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageIntegrationLogs::route('/')
        ];
    }
}
