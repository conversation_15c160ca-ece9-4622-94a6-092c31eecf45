<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Concerns\HandlesTecnospeedPlugboletoBankReturnFileResourceForm;
use App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Concerns\HandlesTecnospeedPlugboletoBankReturnFileResourceTable;
use App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Pages;
use App\Models\TecnospeedPlugboletoBankReturnFile;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TecnospeedPlugboletoBankReturnFileResource extends Resource
{
    use HandlesTecnospeedPlugboletoBankReturnFileResourceForm;
    use HandlesTecnospeedPlugboletoBankReturnFileResourceTable;

    protected static ?string $model = TecnospeedPlugboletoBankReturnFile::class;
    protected static ?string $modelLabel = 'arquivo de retorno';
    protected static ?string $pluralModelLabel = 'arquivos de retorno';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationIcon = 'heroicon-s-document-text';

    public static function getEloquentQuery(): Builder
    {
        return TecnospeedPlugboletoBankReturnFile::query()
            ->orderByDesc('id');
    }

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTecnospeedPlugboletoBankReturnFiles::route('/'),
        ];
    }
}
