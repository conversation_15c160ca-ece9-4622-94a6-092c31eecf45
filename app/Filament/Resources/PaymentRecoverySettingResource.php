<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentRecoverySettingResource\Concerns\HandlesPaymentRecoverySettingResourceForm;
use App\Filament\Resources\PaymentRecoverySettingResource\Concerns\HandlesPaymentRecoverySettingResourceTable;
use App\Filament\Resources\PaymentRecoverySettingResource\Pages;
use App\Models\PaymentRecoverySetting;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class PaymentRecoverySettingResource extends Resource
{
    use HandlesPaymentRecoverySettingResourceForm;
    use HandlesPaymentRecoverySettingResourceTable;

    protected static ?string $model = PaymentRecoverySetting::class;
    protected static ?string $modelLabel = 'configuração de cobrança';
    protected static ?string $pluralModelLabel = 'configurações de cobrança';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentRecoverySettings::route('/'),
            'create' => Pages\CreatePaymentRecoverySetting::route('/create'),
            'edit' => Pages\EditPaymentRecoverySetting::route('/{record}/edit'),
            'view' => Pages\ViewPaymentRecoverySetting::route('/{record}'),
        ];
    }
}
