<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankSlipResource\Concerns\HandlesBankSlipResourceForm;
use App\Filament\Resources\BankSlipResource\Concerns\HandlesBankSlipResourceTable;
use App\Filament\Resources\BankSlipResource\Pages\ManageBankSlips;
use App\Models\BankSlip;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BankSlipResource extends Resource
{
    use HandlesBankSlipResourceForm;
    use HandlesBankSlipResourceTable;

    protected static ?string $model = BankSlip::class;
    protected static ?string $modelLabel = 'boleto';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function getEloquentQuery(): Builder
    {
        return BankSlip::query()
            ->orderByDesc('id');
    }

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageBankSlips::route('/'),
        ];
    }
}
