<?php

namespace App\Filament\Resources\BankSlipResource\Concerns;

use App\Actions\BankSlip\CancelBankSlip;
use App\Actions\BankSlip\UpdateBankSlip;
use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Http\Integrations\Tecnospeed\PlugBoleto\Services\TecnospeedPlugBoletoBankSlipService;
use App\Models\BankSlip;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

trait HandlesBankSlipResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')
                ->label(__('bank_slips.forms.fields.id')),
            TextColumn::make('customer.name')
                ->label(__('bank_slips.forms.fields.customer_name')),
            TextColumn::make('customer.trading_name')
                ->label(__('bank_slips.forms.fields.customer_trading_name')),
            TextColumn::make('amount')
                ->label(__('bank_slips.forms.fields.amount'))
                ->money('brl'),
            TextColumn::make('issued_at')
                ->label(__('bank_slips.forms.fields.issued_at'))
                ->date('d/m/Y'),
            TextColumn::make('expires_at')
                ->label(__('bank_slips.forms.fields.expires_at'))
                ->date('d/m/Y'),
            IconColumn::make('status')
                ->label(__('bank_slips.forms.fields.status'))
                ->icons([
                    'heroicon-s-clock' => BankSlipStatusEnum::Open->value,
                    'heroicon-s-check' => BankSlipStatusEnum::Settled->value,
                    'heroicon-s-x-circle' => BankSlipStatusEnum::Cancelled->value,
                ])
                ->colors([
                    'warning' => BankSlipStatusEnum::Open->value,
                    'success' => BankSlipStatusEnum::Settled->value,
                    'danger' => BankSlipStatusEnum::Cancelled->value,
                ])
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('id')
                ->form([TextInput::make('id')->label(__('bank_slips.forms.fields.id'))])
                ->query(fn (Builder $query, array $data): Builder => $query->where('id', 'like', "%{$data['id']}%")),
            Filter::make('customer_name')
                ->form([TextInput::make('customer_name')->label(__('bank_slips.forms.fields.customer_name'))])
                ->query(fn (Builder $query, array $data): Builder => $query->whereRelation('customer', 'name', 'like', "%{$data['customer_name']}%")),
            Filter::make('customer_trading_name')
                ->form([TextInput::make('customer_trading_name')->label(__('bank_slips.forms.fields.customer_trading_name'))])
                ->query(fn (Builder $query, array $data): Builder => $query->whereRelation('customer', 'trading_name', 'like', "%{$data['customer_trading_name']}%")),
            Filter::make('amount')
                ->form([TextInput::make('amount')->label(__('bank_slips.forms.fields.amount'))])
                ->query(fn (Builder $query, array $data): Builder => $query->where('amount', 'like', "%{$data['amount']}%")),
            Filter::make('issued_at')
                ->form([
                    Grid::make(2)->schema([
                        TextInput::make('issued_at_from')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.issued_at_from')),
                        TextInput::make('issued_at_to')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.issued_at_to')),
                    ])
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when($data['issued_at_from'], fn (Builder $query) => $query->where('issued_at', '>=', $data['issued_at_from']))
                        ->when($data['issued_at_to'], fn (Builder $query) => $query->where('issued_at', '<=', $data['issued_at_to']));
                }),
            Filter::make('expires_at')
                ->form([
                    Grid::make(2)->schema([
                        TextInput::make('expires_at_from')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.expires_at_from')),
                        TextInput::make('expires_at_to')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.expires_at_to')),
                    ])
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when($data['expires_at_from'], fn (Builder $query) => $query->where('expires_at', '>=', $data['expires_at_from']))
                        ->when($data['expires_at_to'], fn (Builder $query) => $query->where('expires_at', '<=', $data['expires_at_to']));
                }),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make()
                    ->using(function (BankSlip $record, array $data): BankSlip {
                        return UpdateBankSlip::run($record, $data);
                    })
                    ->hidden(fn (BankSlip $record): bool => $record->status !== BankSlipStatusEnum::Open->value),
                Action::make('Ver Pdf')
                    ->url(function (BankSlip $record): string {
                        if (!is_null($record->omie_data)) {
                            $record->update(['omie_data' => OmieBankSlipService::make()->get($record->receivable, new OmieGetBankSlipDto($record->receivable->omie_id))]);
                            return $record->omie_data['cLinkBoleto'];
                        }

                        if (!$record->tecnospeed_id) {
                            return '';
                        }

                        return 'https://plugboleto.com.br/api/v1/boletos/impressao/' . $record->tecnospeed_id;
                    })
                    ->hidden(fn (BankSlip $record): bool => !$record->omie_data && !$record->tecnospeed_id)
                    ->icon('heroicon-s-document')
                    ->openUrlInNewTab(),
                Action::make('Cancelar')
                    ->action(function (BankSlip $record): void {
                        try {
                            CancelBankSlip::run($record);
                            success_notification(__('bank_slips.responses.cancel.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->icon('heroicon-s-x-circle')
                    ->color('danger')
                    ->hidden(fn (BankSlip $record): bool => $record->status !== BankSlipStatusEnum::Open->value)
                    ->requiresConfirmation()
            ])
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
