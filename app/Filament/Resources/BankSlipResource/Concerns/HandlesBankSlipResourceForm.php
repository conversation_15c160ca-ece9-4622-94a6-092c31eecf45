<?php

namespace App\Filament\Resources\BankSlipResource\Concerns;

use App\Enums\BankSlipStatusEnum;
use App\Models\BankSlip;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TextInput\Mask;

trait HandlesBankSlipResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Section::make('Geral')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('friendly_status')
                            ->label(__('bank_slips.forms.fields.status'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->friendly_status ?? '')
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('customer_name')
                            ->label(__('bank_slips.forms.fields.customer_name'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->customer->name ?? '')
                            ->columnSpan(2),
                        TextInput::make('customer_trading_name')
                            ->label(__('bank_slips.forms.fields.customer_trading_name'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->customer->trading_name ?? ''),
                        TextInput::make('customer_tax_id_number')
                            ->label(__('bank_slips.forms.fields.customer_tax_id_number'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->customer->friendly_tax_id_number ?? ''),
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('bank_code')
                            ->label(__('bank_slips.forms.fields.bank_code'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bank->code ?? ''),
                        TextInput::make('bank_name')
                            ->label(__('bank_slips.forms.fields.bank_name'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bank->name ?? '')
                            ->columnSpan(3),
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('bank_account_branch_number')
                            ->label(__('bank_slips.forms.fields.bank_account_branch_number'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bankAccount->branch_number ?? ''),
                        TextInput::make('bank_account_branch_digit')
                            ->label(__('bank_slips.forms.fields.bank_account_branch_digit'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bankAccount->branch_digit ?? ''),
                        TextInput::make('bank_account_number')
                            ->label(__('bank_slips.forms.fields.bank_account_number'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bankAccount->number ?? ''),
                        TextInput::make('bank_account_digit')
                            ->label(__('bank_slips.forms.fields.bank_account_digit'))
                            ->disabled()
                            ->formatStateUsing(fn (BankSlip $record): string => $record->bankAccount->digit ?? ''),
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('amount')
                            ->label(__('bank_slips.forms.fields.amount'))
                            ->formatStateUsing(fn (string $state): string => mask_money($state))
                            ->mask(\Filament\Support\RawJs::make(<<<'JS'
                                                'R$ ' + $money($input, ',')
                                            JS))
                    ]),
                ]),
            Section::make('Datas/prazos')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('issued_at')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.issued_at'))
                            ->disabled(),
                        TextInput::make('expires_at')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.expires_at'))
                            ->disabled(fn (BankSlip $record): bool => $record->status !== BankSlipStatusEnum::Open->value),
                        TextInput::make('settled_at')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.settled_at'))
                            ->disabled(),
                        TextInput::make('cancelled_at')
                            ->type('date')
                            ->label(__('bank_slips.forms.fields.cancelled_at'))
                            ->disabled(),
                    ])
                ])
        ];
    }
}
