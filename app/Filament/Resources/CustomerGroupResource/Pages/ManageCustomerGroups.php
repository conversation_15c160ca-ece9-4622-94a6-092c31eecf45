<?php

namespace App\Filament\Resources\CustomerGroupResource\Pages;

use App\Filament\Resources\CustomerGroupResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;

class ManageCustomerGroups extends ManageRecords
{
    protected static string $resource = CustomerGroupResource::class;

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
