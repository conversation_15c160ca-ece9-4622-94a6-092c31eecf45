<?php

namespace App\Filament\Resources\CustomerGroupResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;

trait HandlesCustomerGroupResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label('Nome')
                    ->required()
            ])
        ];
    }
}
