<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockLocationResource\Concerns\HandlesStockLocationResourceForm;
use App\Filament\Resources\StockLocationResource\Concerns\HandlesStockLocationResourceTable;
use App\Filament\Resources\StockLocationResource\Pages\CreateStockLocation;
use App\Filament\Resources\StockLocationResource\Pages\ListStockLocations;
use App\Filament\Resources\StockLocationResource\Pages\ViewStockLocation;
use App\Models\StockLocation;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockLocationResource extends Resource
{
    use HandlesStockLocationResourceForm;
    use HandlesStockLocationResourceTable;

    protected static ?string $model = StockLocation::class;
    protected static ?string $modelLabel = 'localização de estoque';
    protected static ?string $pluralModelLabel = 'localizações de estoque';
    protected static ?string $navigationGroup = 'Produtos';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    /**
     * Configure the resource's main form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array<string, array<string, string>>
     */
    public static function getPages(): array
    {
        return [
            'index' => ListStockLocations::route('/'),
            'create' => CreateStockLocation::route('/create'),
            'view' => ViewStockLocation::route('/{record}'),
        ];
    }
}
