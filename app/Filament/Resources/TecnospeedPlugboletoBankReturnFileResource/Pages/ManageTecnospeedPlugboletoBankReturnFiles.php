<?php

namespace App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Pages;

use App\Actions\TecnospeedPlugboletoBankReturnFile\CreateTecnospeedPlugboletoBankReturnFile;
use App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource;
use App\Models\TecnospeedPlugboletoBankReturnFile;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;

class ManageTecnospeedPlugboletoBankReturnFiles extends ManageRecords
{
    protected static string $resource = TecnospeedPlugboletoBankReturnFileResource::class;

    /**
     * Configure the resource's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make()->using(function (array $data): TecnospeedPlugboletoBankReturnFile {
                return CreateTecnospeedPlugboletoBankReturnFile::run($data);
            }),
        ];
    }
}
