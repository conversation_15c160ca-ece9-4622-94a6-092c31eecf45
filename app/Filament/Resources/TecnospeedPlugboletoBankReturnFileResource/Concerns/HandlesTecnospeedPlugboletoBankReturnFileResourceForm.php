<?php

namespace App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Concerns;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;

trait HandlesTecnospeedPlugboletoBankReturnFileResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label('Nome')
                    ->required()
            ]),
            Grid::make(1)->schema([
                FileUpload::make('path')
                    ->label('Arquivo')
                    ->disk('digitalocean')
                    ->directory('tecnospeed_plugboleto_bank_return_files')
                    ->downloadable()
            ])
        ];
    }
}
