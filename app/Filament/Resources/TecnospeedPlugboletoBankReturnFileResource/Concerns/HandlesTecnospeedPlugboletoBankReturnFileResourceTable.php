<?php

namespace App\Filament\Resources\TecnospeedPlugboletoBankReturnFileResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesTecnospeedPlugboletoBankReturnFileResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('name')
                ->label('Nome do arquivo'),
            TextColumn::make('created_at')
                ->label('Enviado em')
                ->date('d/m/Y H:i:s')
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('name')
                ->form([TextInput::make('name')->label('Nome do arquivo')])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('name', 'like', "%{$data['name']}%");
                }),
            Filter::make('created_at')
                ->form([
                    Grid::make(2)->schema([
                        TextInput::make('created_at_from')
                            ->type('date')
                            ->label('Criado de em'),
                        TextInput::make('created_at_to')
                            ->type('date')
                            ->label('Criado de até'),
                    ])
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when($data['created_at_from'], fn (Builder $query) => $query->whereDate('created_at', '>=', $data['created_at_from']))
                        ->when($data['created_at_to'], fn (Builder $query) => $query->whereDate('created_at', '<=', $data['created_at_to']));
                }),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ViewAction::make()
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
