<?php

namespace App\Filament\Resources\DeliveryAdjustmentResource\Pages;

use App\Filament\Resources\DeliveryAdjustmentResource;
use App\Models\DeliveryAdjustment;
use Filament\Resources\Pages\CreateRecord;

class CreateDeliveryAdjustment extends CreateRecord
{
    protected static string $resource = DeliveryAdjustmentResource::class;

    public function afterValidate(): void
    {
        $openDeliveryAdjustmentExists = DeliveryAdjustment::query()
            ->where('customer_id', $this->data['customer_id'])
            ->where('product_id', $this->data['product_id'])
            ->whereNull('delivery_id')
            ->exists();

        if ($openDeliveryAdjustmentExists) {
            error_notification('Já existe um ajuste de entrega para este cliente/produto pendente de consumo.')->send();
            $this->halt();
        }
    }
}
