<?php

namespace App\Filament\Resources\DeliveryAdjustmentResource\Pages;

use App\Filament\Resources\DeliveryAdjustmentResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDeliveryAdjustments extends ListRecords
{
    protected static string $resource = DeliveryAdjustmentResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
