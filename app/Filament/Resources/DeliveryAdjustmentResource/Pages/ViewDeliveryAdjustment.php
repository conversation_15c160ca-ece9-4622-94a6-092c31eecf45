<?php

namespace App\Filament\Resources\DeliveryAdjustmentResource\Pages;

use App\Filament\Resources\DeliveryAdjustmentResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDeliveryAdjustment extends ViewRecord
{
    protected static string $resource = DeliveryAdjustmentResource::class;

    protected function getActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
