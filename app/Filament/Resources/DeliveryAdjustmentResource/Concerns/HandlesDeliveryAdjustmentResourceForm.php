<?php

namespace App\Filament\Resources\DeliveryAdjustmentResource\Concerns;

use App\Models\Customer;
use App\Models\DeliveryAdjustment;
use App\Models\Product;
use Closure;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

trait HandlesDeliveryAdjustmentResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(4)->schema([
                TextInput::make('delivery_id')
                    ->label(__('delivery_adjustments.forms.fields.delivery_id'))
                    ->disabled(),
            ]),
            Grid::make(4)->schema([
                Select::make('customer_id')
                    ->label(__('delivery_adjustments.forms.fields.customer_id'))
                    ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                    ->columnSpan(2)
                    ->reactive()
                    ->required()
                    ->searchable()
                    ->getSearchResultsUsing(function (string $search) {
                        return Customer::query()
                            ->where('active', true)
                            ->where(function (Builder $query) use ($search): Builder {
                                $taxIdNumber = Str::remove(['.', '-', '/'], $search);

                                return $query
                                    ->where('name', 'like', "%$search%")
                                    ->orWhere('trading_name', 'like', "%$search%")
                                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                    });
                            })
                            ->get()
                            ->map(fn (Customer $customer) => [
                                'id' => $customer->id,
                                'name' => "$customer->name | $customer->trading_name"
                            ])
                            ->pluck('name', 'id');
                    })
                    ->getOptionLabelUsing(function (DeliveryAdjustment $record): ?string {
                        return $record->customer->name;
                    })
                    ->afterStateHydrated(function (?string $state, \Filament\Forms\Set $set): void {
                        self::handleCustomerDependantFields($state, $set);
                    })
                    ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set): void {
                        self::handleCustomerDependantFields($state, $set);
                    }),
                TextInput::make('customer_trading_name')
                    ->disabled()
                    ->label(__('delivery_adjustments.forms.fields.customer_trading_name')),
                TextInput::make('customer_tax_id_number')
                    ->disabled()
                    ->label(__('delivery_adjustments.forms.fields.customer_tax_id_number'))
            ]),
            Grid::make(4)->schema([
                Select::make('product_id')
                    ->label(__('delivery_adjustments.forms.fields.product_id'))
                    ->required()
                    ->searchable()
                    ->getSearchResultsUsing(fn (string $search) => Product::pluckForSearchableSelect($search))
                    ->getOptionLabelUsing(fn ($value) => Product::find($value)?->name ?? '')
                    ->columnSpan(2),
                TextInput::make('desired_withdraw_quantity')
                    ->label(__('delivery_adjustments.forms.fields.desired_withdraw_quantity'))
                    ->required()
                    ->numeric(),
                TextInput::make('max_withdraw_percentage')
                    ->label(__('delivery_adjustments.forms.fields.max_withdraw_percentage'))
                    ->required()
                    ->numeric(),
            ]),
            Grid::make(4)->schema([
                TextInput::make('parent_delivery_adjustment_id')
                    ->label(__('delivery_adjustments.forms.fields.parent_delivery_adjustment_id'))
                    ->numeric(),
            ]),
            Grid::make(1)->schema([
                Textarea::make('additional_info')
                    ->label(__('delivery_adjustments.forms.fields.additional_info'))
                    ->rows(3)
                    ->maxLength(500)
            ]),
        ];
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?string $customerId, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_trading_name', $customer?->trading_name ?? '');
        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
