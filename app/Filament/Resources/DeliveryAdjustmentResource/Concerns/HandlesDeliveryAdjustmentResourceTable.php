<?php

namespace App\Filament\Resources\DeliveryAdjustmentResource\Concerns;

use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesDeliveryAdjustmentResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')
                ->label(__('delivery_adjustments.forms.fields.id')),
            TextColumn::make('customer.trading_name')
                ->label(__('delivery_adjustments.forms.fields.customer_id')),
            TextColumn::make('product.name')
                ->label(__('delivery_adjustments.forms.fields.product_id')),
            TextColumn::make('desired_withdraw_quantity')
                ->label(__('delivery_adjustments.forms.fields.desired_withdraw_quantity')),
            TextColumn::make('max_withdraw_percentage')
                ->label(__('delivery_adjustments.forms.fields.max_withdraw_percentage')),
            TextColumn::make('parent_delivery_adjustment_id')
                ->label(__('delivery_adjustments.forms.fields.parent_delivery_adjustment_id')),
            IconColumn::make('delivery_id')
                ->label('Consumido?')
                ->options([
                    'heroicon-o-check-circle' => fn (?int $state): bool => !is_null($state),
                    'heroicon-o-x-circle' => fn (?int $state): bool => is_null($state),
                ])
                ->boolean()
                ->trueIcon('heroicon-o-check-circle')
                ->falseIcon('heroicon-o-x-circle')
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('id')
                ->form([TextInput::make('id')->label(__('delivery_adjustments.forms.fields.id'))])
                ->query(fn (Builder $query, array $data): Builder => $query->where('id', 'like', "%{$data['id']}%")),
            Filter::make('customer_trading_name')
                ->form([TextInput::make('customer_trading_name')->label(__('delivery_adjustments.forms.fields.customer_trading_name'))])
                ->query(fn (Builder $query, array $data): Builder => $query->whereRelation('customer', 'trading_name', 'like', "%{$data['customer_trading_name']}%")),
            Filter::make('product_id')
                ->form([TextInput::make('product_id')->label(__('delivery_adjustments.forms.fields.product_id'))])
                ->query(fn (Builder $query, array $data): Builder => $query->whereRelation('product', 'name', 'like', "%{$data['product_id']}%")),
            TernaryFilter::make('Consumido?')
                ->placeholder('Todos')
                ->trueLabel('Consumidos')
                ->falseLabel('Não consumidos')
                ->queries(
                    true: fn (Builder $query) => $query->whereNotNull('delivery_id'),
                    false: fn (Builder $query) => $query->whereNull('delivery_id'),
                    blank: fn (Builder $query) => $query,
                ),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
