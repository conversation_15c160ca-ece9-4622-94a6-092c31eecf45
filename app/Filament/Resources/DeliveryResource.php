<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DeliveryResource\Concerns\HandlesDeliveryResourceForm;
use App\Filament\Resources\DeliveryResource\Concerns\HandlesDeliveryResourceTable;
use App\Filament\Resources\DeliveryResource\Pages\CreateDelivery;
use App\Filament\Resources\DeliveryResource\Pages\EditDelivery;
use App\Filament\Resources\DeliveryResource\Pages\ListDeliveries;
use App\Filament\Resources\DeliveryResource\Pages\ViewDelivery;
use App\Models\Delivery;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DeliveryResource extends Resource
{
    use HandlesDeliveryResourceForm;
    use HandlesDeliveryResourceTable;

    protected static ?string $model = Delivery::class;
    protected static ?string $modelLabel = 'entrega';
    protected static ?string $navigationGroup = 'Movimentação';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-truck';

    /**
     * @inheritDoc
     */
    public static function getEloquentQuery(): Builder
    {
        return Delivery::query()
            ->orderByDesc('id')
            ->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    /**
     * Build the resource's main form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Build the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ListDeliveries::route('/'),
            'create' => CreateDelivery::route('/create'),
            'edit' => EditDelivery::route('/{record}/edit'),
            'view' => ViewDelivery::route('/{record}'),
        ];
    }
}
