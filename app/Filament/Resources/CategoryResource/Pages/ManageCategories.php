<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;

class ManageCategories extends ManageRecords
{
    protected static string $resource = CategoryResource::class;

    /**
     * Get the resource actions.
     *
     * @return array<int, string>
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
