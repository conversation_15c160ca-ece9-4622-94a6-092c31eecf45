<?php

namespace App\Filament\Resources\PaymentMethodResource\Concerns;

use App\Core\Filament\Form\Fields\TextInput;
use App\Models\PaymentMethod;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesPaymentMethodResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')
                ->label(__('payment_methods.forms.fields.id')),
            TextColumn::make('name')
                ->label(__('payment_methods.forms.fields.name')),
            TextColumn::make('bankAccountWallet.number')
                ->label(__('payment_methods.forms.fields.bank_account_wallet_id'))
                ->formatStateUsing(function (PaymentMethod $record): string {
                    return $record?->bankAccountWallet->full_number ?? '';
                }),
            IconColumn::make('generates_installments')
                ->label(__('payment_methods.forms.fields.generates_installments'))
                ->boolean(),
            TextColumn::make('installment_interval')
                ->label(__('payment_methods.forms.fields.installment_interval')),
            IconColumn::make('active')
                ->label(__('payment_methods.forms.fields.active'))
                ->boolean(),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('id')
                ->form([
                    TextInput::make('id')->label(__('payment_methods.forms.fields.id')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('id', 'like', "%{$data['id']}%");
                }),
            Filter::make('name')
                ->form([
                    TextInput::make('name')->label(__('payment_methods.forms.fields.name')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('name', 'like', "%{$data['name']}%");
                }),
            Filter::make('bank_account_wallet_id')
                ->form([
                    TextInput::make('bank_account_wallet_id')->label(__('payment_methods.forms.fields.bank_account_wallet_id')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->whereDoesntHave('bankAccountWallet')
                        ->orWhereRelation('bankAccountWallet', 'number', 'like', "%{$data['bank_account_wallet_id']}%");
                }),
            SelectFilter::make('generates_installments')
                ->label(__('payment_methods.forms.fields.generates_installments'))
                ->options([
                    true => 'Sim',
                    false => 'Não',
                ]),
            Filter::make('installment_interval')
                ->form([
                    TextInput::make('installment_interval')->label(__('payment_methods.forms.fields.installment_interval')),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('installment_interval', 'like', "%{$data['installment_interval']}%");
                }),
            SelectFilter::make('active')
                ->label(__('payment_methods.forms.fields.active'))
                ->options([
                    true => 'Sim',
                    false => 'Não',
                ]),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            DeleteBulkAction::make(),
        ];
    }
}
