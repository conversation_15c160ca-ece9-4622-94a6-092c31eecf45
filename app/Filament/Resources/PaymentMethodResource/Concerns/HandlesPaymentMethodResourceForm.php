<?php

namespace App\Filament\Resources\PaymentMethodResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

trait HandlesPaymentMethodResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('payment_methods.forms.fields.name'))
                    ->required()
            ]),
            Grid::make(4)->schema([
                TextInput::make('discount_fee')
                    ->label(__('payment_methods.forms.fields.discount_fee'))
                    ->numeric()
                    ->postfix('%'),
                Select::make('generates_installments')
                    ->label(__('payment_methods.forms.fields.generates_installments'))
                    ->required()
                    ->options([
                        true => 'Sim',
                        false => 'Não'
                    ]),
                TextInput::make('installment_interval')
                    ->label(__('payment_methods.forms.fields.installment_interval'))
                    ->helperText('Digite os valores desejados, separando-os por vírgulas, e sem utilizar espaços. (Ex.: 10,10,10)')
                    ->columnSpan(2)
            ]),
            Grid::make(1)
                ->hiddenOn('create')
                ->schema([
                    Toggle::make('active')->label(__('payment_methods.forms.fields.active'))
                ])
        ];
    }
}
