<?php

namespace App\Filament\Resources\DeliveryResource\Pages;

use App\Actions\Delivery\DeleteDelivery;
use App\Actions\Delivery\SendDeliveryReceiptEmail;
use App\Filament\Resources\DeliveryResource;
use App\Models\Customer;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Throwable;

class EditDelivery extends EditRecord
{
    protected static string $resource = DeliveryResource::class;

    /**
     * Get the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('Gerar Pdf')
                    ->url(fn (): string => route('deliveries.generate_pdf', $this->record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-document')
                    ->requiresConfirmation(),
                Action::make('Imprimir')
                    ->url(fn (): string => route('deliveries.generate_receipt', $this->record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-s-printer')
                    ->requiresConfirmation(),
                Action::make('Enviar email de recibo')
                    ->action(function () {
                        SendDeliveryReceiptEmail::dispatch($this->record, false, false, auth()->id());
                    })
                    ->icon('heroicon-s-envelope')
                    ->requiresConfirmation(),
                DeleteAction::make()
                    ->form([
                        Toggle::make('send_email')
                            ->label('Enviar email de confirmação')
                            ->default(true)
                    ])
                    ->using(function (Delivery $record, array $data) {
                        try {
                            DeleteDelivery::run($record, auth()->id(), (bool) $data['send_email']);
                            success_notification(__('deliveries.responses.delete.success'))->send();
                            return redirect()->route('filament.app.resources.deliveries.index');
                        } catch (Throwable $th) {
                            error($th);
                            error_notification()->send();
                        }
                    }),
            ])
        ];
    }

    /**
     * Mutate the data array before filling the form.
     *
     * @param  array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Delivery $delivery */
        $delivery = Delivery::find($data['id']);

        $data['products'] = $delivery->deliveryItems->map(function (DeliveryItem $deliveryItem) {
            return [
                'product_id' => $deliveryItem->product_id,
                'quantity' => (float) $deliveryItem->quantity
            ];
        })->toArray();

        $data['customer_basic_stock_stats'] = $delivery->customer->customerBasicStockStats;

        return $data;
    }

    /**
     * Runs before the form fields are validated when the form is submitted.
     *
     * @return void
     */
    protected function beforeValidate(): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($this->data['customer_id']);

        if (carbon($customer->service_started_at)->startOfDay()->gt(carbon($this->data['delivered_at'])->startOfDay())) {
            error_notification('A data de início do serviço é posterior à data de entrega.')->send();
            $this->halt();
        }
    }

    /**
     * Perform record update action.
     *
     * @param  \Illuminate\Database\Eloquent\Model $record
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return \App\Actions\Delivery\EditDelivery::run($record, $data, auth()->id());
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('deliveries.responses.update.success'))->send();
    }
}
