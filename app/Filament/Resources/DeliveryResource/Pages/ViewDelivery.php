<?php

namespace App\Filament\Resources\DeliveryResource\Pages;

use App\Filament\Resources\DeliveryResource;
use App\Models\Delivery;
use App\Models\DeliveryItem;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewDelivery extends ViewRecord
{
    protected static string $resource = DeliveryResource::class;

    /**
     * Get the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            EditAction::make(),
        ];
    }

    /**
     * Mutate the data array before filling the form.
     *
     * @param  array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Delivery $delivery */
        $delivery = Delivery::find($data['id']);

        $data['products'] = $delivery->deliveryItems->map(function (DeliveryItem $deliveryItem) {
            return [
                'product_id' => $deliveryItem->product_id,
                'quantity' => (float) $deliveryItem->quantity
            ];
        })->toArray();

        $data['customer_basic_stock_stats'] = $delivery->customer->customerBasicStockStats;

        return $data;
    }
}
