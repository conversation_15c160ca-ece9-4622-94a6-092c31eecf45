<?php

namespace App\Filament\Resources\DeliveryResource\Pages;

use App\Filament\Resources\DeliveryResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListDeliveries extends ListRecords
{
    protected static string $resource = DeliveryResource::class;

    protected function getActions(): array
    {
        return [
            Action::make('Gerar mapa')
                ->url(fn (): string => route('deliveries.generate_map')),
            Action::make('Criar lote')
                ->url(fn (): string => route('deliveries.generate_batch')),
            CreateAction::make(),
        ];
    }
}
