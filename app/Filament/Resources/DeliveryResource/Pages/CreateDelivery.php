<?php

namespace App\Filament\Resources\DeliveryResource\Pages;

use App\Filament\Resources\DeliveryResource;
use App\Models\Customer;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateDelivery extends CreateRecord
{
    protected static string $resource = DeliveryResource::class;

    /**
     * Runs before the form fields are validated when the form is submitted.
     *
     * @return void
     */
    protected function beforeValidate(): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($this->data['customer_id']);

        if (carbon($customer->service_started_at)->startOfDay()->gt(carbon($this->data['delivered_at'])->startOfDay())) {
            error_notification('A data de início do serviço é posterior à data de entrega.')->send();
            $this->halt();
        }
    }

    /**
     * Handle the record creation.
     *
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordCreation(array $data): Model
    {
        return \App\Actions\Delivery\CreateDelivery::run($data, auth()->id());
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('deliveries.responses.create.success'))->send();
    }
}
