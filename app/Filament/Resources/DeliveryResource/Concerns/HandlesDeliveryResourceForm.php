<?php

namespace App\Filament\Resources\DeliveryResource\Concerns;

use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\CustomerProduct;
use App\Models\Customer;
use App\Models\Delivery;
use App\Models\Product;
use App\Models\StockMovementSummary;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

trait HandlesDeliveryResourceForm
{
    /**
     * Get the form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        $deliveryItemsCount = 0;
        $componentItemsCount = 0;

        return [
            Section::make('Geral')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('delivered_at')
                            ->type('date')
                            ->label(__('deliveries.forms.fields.delivered_at'))
                            ->required()
                            ->default(now()->format('Y-m-d'))
                            ->lazy()
                            ->afterStateUpdated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                $set('delivered_at_weekday', get_br_weekday($get('delivered_at')));
                            })
                            ->afterStateHydrated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                $set('delivered_at_weekday', get_br_weekday($get('delivered_at')));
                            }),
                        TextInput::make('delivered_at_weekday')
                            ->label(__('deliveries.forms.fields.delivered_at_weekday'))
                            ->disabled()
                            ->default(get_br_weekday(now())),
                    ]),
                    Grid::make(4)->schema([
                        Select::make('customer_id')
                            ->label(__('deliveries.forms.fields.customer_id'))
                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                            ->columnSpan(2)
                            ->reactive()
                            ->required()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Customer::query()
                                    ->where('active', true)
                                    ->where(function (Builder $query) use ($search): Builder {
                                        $taxIdNumber = Str::remove(['.', '-', '/'], $search);

                                        return $query
                                            ->where('name', 'like', "%$search%")
                                            ->orWhere('trading_name', 'like', "%$search%")
                                            ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                                return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                            });
                                    })
                                    ->get()
                                    ->map(fn (Customer $customer) => [
                                        'id' => $customer->id,
                                        'name' => "$customer->name | $customer->trading_name"
                                    ])
                                    ->pluck('name', 'id');
                            })
                            ->getOptionLabelUsing(function (Delivery $record): ?string {
                                return $record?->customer->name ?? '';
                            })
                            ->afterStateHydrated(function (?string $state, \Filament\Forms\Set $set, string $context): void {
                                self::postProcessEntityIdSelectDataLoad($state, $set, $context);
                            })
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set, string $context): void {
                                self::postProcessEntityIdSelectDataLoad($state, $set, $context);
                            }),
                        TextInput::make('customer_trading_name')
                            ->disabled()
                            ->label(__('deliveries.forms.fields.customer_trading_name')),
                        TextInput::make('customer_tax_id_number')
                            ->disabled()
                            ->label(__('deliveries.forms.fields.customer_tax_id_number'))
                        ]),
                        Grid::make(1)->schema([
                            Textarea::make('additional_info')
                                ->label(__('deliveries.forms.fields.additional_info'))
                                ->rows(3)
                                ->maxLength(500)
                        ]),
                ]),
            Section::make('Itens')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('reference_date')
                            ->type('date')
                            ->label(__('deliveries.forms.fields.reference_date'))
                            ->lazy()
                            ->default(now()->format('Y-m-d'))
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Get $get, \Filament\Forms\Set $set) {
                                $set('reference_date_weekday', get_br_weekday($get('reference_date')));

                                if (is_null($get('customer_id'))) {
                                    return;
                                }

                                /** @var \App\Models\Collection|null $collection */
                                $collection = Collection::query()
                                    ->with('collectionItems')
                                    ->where('customer_id', $get('customer_id'))
                                    ->where('collected_at', carbon($state)->format('Y-m-d'))
                                    ->first();

                                if (is_null($collection)) {
                                    /** @var \App\Models\Customer|null $customer */
                                    $customer = Customer::find($get('customer_id'));

                                    $set('products', $customer->customerProducts->map(fn (CustomerProduct $customerProduct) => [
                                        'product_id' => $customerProduct->product_id,
                                        'quantity' => number_format($customerProduct->quantity, 0),
                                        'current_stock_quantity' => $customerProduct->product?->stockLocationProducts()
                                            ->whereRelation('stockLocation', 'customer_id', $customerProduct->customer_id)
                                            ->first()
                                            ?->current_quantity ?? 0
                                    ])->toArray());

                                    return;
                                }

                                $products = $collection->collectionItems
                                    ->map(fn (CollectionItem $item) => [
                                        'product_id' => $item->product_id,
                                        'quantity' => number_format($item->quantity, 0),
                                        'current_stock_quantity' => $item->product?->stockLocationProducts()
                                            ->whereRelation('stockLocation', 'customer_id', $item->collection->customer_id)
                                            ->first()
                                            ?->current_quantity ?? 0
                                    ])
                                    ->toArray();

                                $set('products', $products);
                            }),
                        TextInput::make('reference_date_weekday')
                            ->label(__('deliveries.forms.fields.reference_date_weekday'))
                            ->disabled()
                            ->default(get_br_weekday(now())),
                    ]),
                    Grid::make(1)->schema([
                        TableRepeater::make('products')
                            ->label('')
                            ->columns(4)
                            ->addActionLabel('Adicionar produto')
                            ->defaultItems(0)
                            ->afterStateHydrated(function (Repeater $component, ?Delivery $record) use (&$deliveryItemsCount, &$componentItemsCount) {
                                if ($deliveryItemsCount === 0 && !is_null($record)) {
                                    if ($record->relationLoaded('deliveryItems')) {
                                        $record->load('deliveryItems');
                                    }

                                    $deliveryItemsCount = $record->deliveryItems->count();
                                }

                                $componentItemsCount = $component->getItemsCount();
                            })
                            ->headers([
                                Header::make(__('deliveries.forms.fields.products.product_id')),
                                Header::make(__('deliveries.forms.fields.products.quantity')),
                                Header::make(__('deliveries.forms.fields.products.current_stock_quantity')),
                            ])
                            ->schema([
                                Select::make('product_id')
                                    ->required()
                                    ->autofocus(function () use (&$deliveryItemsCount, &$componentItemsCount) {
                                        return $componentItemsCount !== $deliveryItemsCount;
                                    })
                                    ->searchable()
                                    ->getSearchResultsUsing(fn (string $search) => Product::pluckForSearchableSelect($search))
                                    ->getOptionLabelUsing(fn ($value) => Product::find($value)?->name ?? '')
                                    ->afterStateHydrated(function (Select $component, ?Delivery $record) use (&$componentItemsCount, &$deliveryItemsCount) {
                                        if (!is_null($record)) {
                                            if (!$record->relationLoaded('deliveryItems')) {
                                                $record->load('deliveryItems');
                                            }

                                            $deliveryItemsCount = $record->deliveryItems->count();
                                        }

                                        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
                                    })
                                    ->afterStateUpdated(function (Select $component, ?Delivery $record) use (&$componentItemsCount, &$deliveryItemsCount) {
                                        if (!is_null($record)) {
                                            if (!$record->relationLoaded('deliveryItems')) {
                                                $record->load('deliveryItems');
                                            }

                                            $deliveryItemsCount = $record->deliveryItems->count();
                                        }

                                        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
                                    })
                                    ->columnSpan(3),
                                TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1)
                                    ->lazy()
                                    ->suffix(function (?int $state, \Filament\Forms\Get $get) {
                                        if (is_null($state)) {
                                            return '';
                                        }

                                        $stats = is_array($get('../../customer_basic_stock_stats'))
                                            ? collect($get('../../customer_basic_stock_stats'))
                                            : $get('../../customer_basic_stock_stats');

                                        $weekdayGroupedStats = $stats
                                            ->groupBy('weekday')
                                            ->toArray();

                                        if (!isset($weekdayGroupedStats[carbon($get('../../delivered_at'))->dayOfWeek])) {
                                            return '';
                                        }

                                        $weekdayStats = $weekdayGroupedStats[carbon($get('../../delivered_at'))->dayOfWeek];

                                        $weekdayStats = collect($weekdayStats)->groupBy('product_id');

                                        if (!isset($weekdayStats[$get('product_id')])) {
                                            return '';
                                        }

                                        $indexedWeekdayStats = $weekdayStats[$get('product_id')]->mapWithKeys(fn (array $statsItem): array => [
                                            $statsItem['stats_type'] => $statsItem
                                        ])->toArray();

                                        if (
                                            $state > ((int)$indexedWeekdayStats['basic-mean']['amount'] + (int) $indexedWeekdayStats['standard-deviation']['amount'])
                                            || $state < ((int)$indexedWeekdayStats['basic-mean']['amount'] - (int) $indexedWeekdayStats['standard-deviation']['amount'])
                                        ) {
                                            return new HtmlString('<span style="color: red;"><strong>Atenção! (perf. de consumo)</strong></span>');
                                        }

                                        return '';
                                    }),
                                TextInput::make('current_stock_quantity')
                                    ->disabled()
                                    ->numeric(),
                            ])
                    ])
                ]),
            Grid::make(1)
                ->hiddenOn('view')
                ->schema([
                    Toggle::make('send_email')
                        ->label('Enviar e-mail de recibo?')
                        ->default(true)
                ]),
        ];
    }

    /**
     * Handle the customer ID select data load post processing.
     *
     * @param  string|null $state
     * @param  \Filament\Forms\Set $set
     * @param  string $context
     * @return void
     */
    protected static function postProcessEntityIdSelectDataLoad(?string $state, \Filament\Forms\Set $set, string $context): void
    {
        /** @var \App\Models\Customer|null $customer */
        $customer = self::handleCustomerDependantFields($state, $set);

        if (is_null($customer) || $context === 'edit') {
            return;
        }

        $customer->load('collections', 'customerBasicStockStats');

        $set('customer_basic_stock_stats', $customer->customerBasicStockStats);

        if ($customer->collections->count() > 0) {
            /** @var \App\Models\Collection $collection */
            $collection = $customer->collections()
                ->orderByDesc('id')
                ->take($customer->previous_collection_count + 1)
                ->get()
                ->last();

            $products = $collection->collectionItems
                ->map(function (CollectionItem $item) use ($customer): array {
                    $quantity = StockMovementSummary::query()
                        ->where('customer_id', $customer->id)
                        ->where('product_id', $item->product_id)
                        ->orderByDesc('movement_date')
                        ->first()
                        ?->stock_quantity;

                    return [
                        'product_id' => $item->product_id,
                        'quantity' => number_format($item->quantity, 0),
                        'current_stock_quantity' => $quantity
                            ? (int) $quantity
                            : 0,
                    ];
                })
                ->toArray();

            $set('products', $products);
            $set('reference_date', carbon($collection->collected_at)->format('Y-m-d'));

            return;
        }

        $products = $customer->customerProducts
            ->load('product:id,name')
            ->sortBy('product.name')
            ->map(function (CustomerProduct $customerProduct) use ($customer): array {
                $quantity = StockMovementSummary::query()
                    ->where('customer_id', $customer->id)
                    ->where('product_id', $customerProduct->product_id)
                    ->orderByDesc('movement_date')
                    ->first()
                    ?->stock_quantity;

                return [
                    'product_id' => $customerProduct->product_id,
                    'quantity' => 0,
                    'current_stock_quantity' => $quantity
                        ? (int) $quantity
                        : 0,
                ];
            })
            ->toArray();

        $orderedItems = [];

        foreach ($products as $product) {
            $orderedItems[] = $product;
        }

        $set('products', $orderedItems);
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?string $customerId, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_trading_name', $customer?->trading_name ?? '');
        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
