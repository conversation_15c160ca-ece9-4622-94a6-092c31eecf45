<?php

namespace App\Filament\Resources\LendingStockAdjustmentResource\Concerns;

use App\Actions\LendingStockAdjustment\DeleteLendingStockAdjustment;
use App\Models\LendingStockAdjustment;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

trait HandlesLendingStockAdjustmentResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')
                ->label(__('lending_stock_adjustments.forms.fields.id')),
            TextColumn::make('customer.name')
                ->label(__('lending_stock_adjustments.forms.fields.customer_id')),
            TextColumn::make('customer.trading_name')
                ->label(__('customers.forms.fields.trading_name')),
            TextColumn::make('customer.tax_id_number')
                ->label(__('customers.forms.fields.tax_id_number'))
                ->formatStateUsing(function (?string $state): ?string {
                    return strlen($state) > 11
                        ? mask_cnpj($state)
                        : mask_cpf($state);
                }),
            TextColumn::make('adjusted_at')
                ->label(__('lending_stock_adjustments.forms.fields.adjusted_at'))
                ->date('d/m/Y')
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('customer_name')
                ->form([
                    TextInput::make('customer_name')->label(__('lending_stock_adjustments.forms.fields.customer_id'))
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->whereHas('customer', function (Builder $query) use ($data): Builder {
                        return $query->where('name', 'like', "%{$data['customer_name']}%");
                    });
                }),
            Filter::make('customer_trading_name')
                ->form([
                    TextInput::make('customer_trading_name')->label(__('customers.forms.fields.trading_name'))
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->whereHas('customer', function (Builder $query) use ($data): Builder {
                        return $query->where('trading_name', 'like', "%{$data['customer_trading_name']}%");
                    });
                }),
            Filter::make('customer_tax_id_number')
                ->form([
                    TextInput::make('customer_tax_id_number')->label(__('customers.forms.fields.tax_id_number'))
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->whereHas('customer', function (Builder $query) use ($data): Builder {
                        return $query->where('tax_id_number', 'like', '%' . get_numbers($data['customer_tax_id_number']) . '%');
                    });
                }),
            Filter::make('adjusted_at')
                ->form([
                    TextInput::make('adjusted_at_from')
                        ->type('date')
                        ->label(__('lending_stock_adjustments.forms.fields.adjusted_at_from')),
                    TextInput::make('adjusted_at_to')
                        ->type('date')
                        ->label(__('lending_stock_adjustments.forms.fields.adjusted_at_to'))
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(!is_null($data['adjusted_at_from']), function (Builder $query) use ($data) {
                            return $query->where('adjusted_at', '>=', $data['adjusted_at_from']);
                        })
                        ->when(!is_null($data['adjusted_at_to']), function (Builder $query) use ($data) {
                            return $query->where('adjusted_at', '<=', $data['adjusted_at_to']);
                        });
                })
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                DeleteAction::make()->using(function (LendingStockAdjustment $record): void {
                    try {
                        DeleteLendingStockAdjustment::run($record);
                        success_notification(__('lending_stock_adjustments.responses.delete.success'))->send();
                    } catch (Throwable) {
                        error_notification()->send();
                    }
                }),
            ])
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
