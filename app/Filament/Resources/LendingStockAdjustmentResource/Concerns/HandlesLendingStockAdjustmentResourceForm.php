<?php

namespace App\Filament\Resources\LendingStockAdjustmentResource\Concerns;

use App\Models\Customer;
use App\Models\LendingStockAdjustment;
use App\Models\Product;
use App\Models\StockMovementSummary;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

trait HandlesLendingStockAdjustmentResourceForm
{
    /**
     * Get the form's schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Section::make('Geral')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        Select::make('customer_id')
                            ->label(__('lending_stock_adjustments.forms.fields.customer_id'))
                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                            ->columnSpan(2)
                            ->reactive()
                            ->required()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Customer::query()
                                    ->where('active', true)
                                    ->where(function (Builder $query) use ($search): Builder {
                                        $taxIdNumber = Str::remove(['.', '-', '/'], $search);

                                        return $query
                                            ->where('name', 'like', "%$search%")
                                            ->orWhere('trading_name', 'like', "%$search%")
                                            ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                                return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                            });
                                    })
                                    ->get()
                                    ->map(fn (Customer $customer) => [
                                        'id' => $customer->id,
                                        'name' => "$customer->name | $customer->trading_name"
                                    ])
                                    ->pluck('name', 'id');
                            })
                            ->getOptionLabelUsing(function (LendingStockAdjustment $record): ?string {
                                return $record->customer->name;
                            })
                            ->afterStateHydrated(function (?string $state, \Filament\Forms\Set $set, \Filament\Forms\Get $get, ?LendingStockAdjustment $record): void {
                                self::loadProductsForStockAdjustment($state, $get('adjusted_at'), $set, $record);
                            })
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set, \Filament\Forms\Get $get, ?LendingStockAdjustment $record): void {
                                self::loadProductsForStockAdjustment($state, $get('adjusted_at'), $set, $record);
                            }),
                        TextInput::make('customer_trading_name')
                            ->disabled()
                            ->label(__('customers.forms.fields.trading_name')),
                        TextInput::make('customer_tax_id_number')
                            ->disabled()
                            ->label(__('customers.forms.fields.tax_id_number'))
                    ]),
                    Grid::make(4)->schema([
                        TextInput::make('adjusted_at')
                            ->label(__('lending_stock_adjustments.forms.fields.adjusted_at'))
                            ->required()
                            ->type('date')
                            ->lazy()
                            ->afterStateHydrated(function (?string $state, \Filament\Forms\Set $set, \Filament\Forms\Get $get, ?LendingStockAdjustment $record): void {
                                self::loadProductsForStockAdjustment($get('customer_id'), $state, $set, $record);
                                $set('adjusted_at_weekday', get_br_weekday($get('adjusted_at')));
                            })
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set, \Filament\Forms\Get $get, ?LendingStockAdjustment $record): void {
                                self::loadProductsForStockAdjustment($get('customer_id'), $state, $set, $record);
                                $set('adjusted_at_weekday', get_br_weekday($get('adjusted_at')));
                            }),
                        TextInput::make('adjusted_at_weekday')
                            ->label(__('lending_stock_adjustments.forms.fields.adjusted_at_weekday'))
                            ->disabled()
                            ->default(get_br_weekday(now())),
                    ]),
                    Grid::make(1)->schema([
                        Textarea::make('additional_info')
                            ->label(__('collections.forms.fields.additional_info'))
                            ->rows(3)
                            ->maxLength(500)
                    ])
                ]),
            Section::make('Produtos')
                ->compact()
                ->schema([
                    TableRepeater::make('products')
                        ->hiddenLabel()
                        ->columns(9)
                        ->addable(false)
                        ->reorderable(false)
                        ->defaultItems(0)
                        ->reactive()
                        ->headers([
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.product_id')),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.old_stock_quantity'))
                                ->width('10%'),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.collected_quantity'))
                                ->width('10%'),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.delivered_quantity'))
                                ->width('10%'),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.out_of_movement_quantity'))
                                ->width('10%'),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.adjustment_quantity'))
                                ->width('10%'),
                            Header::make(__('lending_stock_adjustments.forms.fields.lending_stock_adjustment_items.current_stock_quantity'))
                                ->width('10%'),
                        ])
                        ->schema([
                            Select::make('product_id')
                                ->columnSpan(2)
                                ->searchable()
                                ->getSearchResultsUsing(function (?string $search): ?\Illuminate\Support\Collection {
                                    return Product::pluckForSearchableSelect($search);
                                })
                                ->getOptionLabelUsing(function (?string $value): ?string {
                                    return Product::find($value)?->name ?? '';
                                }),
                            TextInput::make('old_stock_quantity')
                                ->readOnly()
                                ->numeric(),
                            TextInput::make('collected_quantity')
                                ->readOnly()
                                ->numeric(),
                            TextInput::make('delivered_quantity')
                                ->readOnly()
                                ->numeric(),
                            TextInput::make('out_of_movement_quantity')
                                ->required()
                                ->lazy()
                                ->numeric()
                                ->afterStateUpdated(function (TextInput $component, \Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                    $id = explode('.', $component->getId());
                                    self::calculateFieldValues($get, $set, 'out_of_movement_quantity', $id[2]);
                                }),
                            TextInput::make('adjustment_quantity')
                                ->required()
                                ->lazy()
                                ->numeric()
                                ->afterStateUpdated(function (TextInput $component, \Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                    $id = explode('.', $component->getId());
                                    self::calculateFieldValues($get, $set, 'adjustment_quantity', $id[2]);
                                }),
                            TextInput::make('current_stock_quantity')
                                ->required()
                                ->lazy()
                                ->numeric()
                                ->afterStateUpdated(function (TextInput $component, \Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                    $id = explode('.', $component->getId());
                                    self::calculateFieldValues($get, $set, 'current_stock_quantity', $id[2]);
                                })
                        ])
                ])
        ];
    }

    /**
     * Handle the customer ID select data load post processing.
     *
     * @param  string|null $customerId
     * @param  string|null $referenceDate
     * @param  \Filament\Forms\Set $set
     * @param  \App\Models\LendingStockAdjustment|null $lendingStockAdjustment
     * @return void
     */
    protected static function loadProductsForStockAdjustment(
        ?string $customerId,
        ?string $referenceDate,
        \Filament\Forms\Set $set,
        ?LendingStockAdjustment $lendingStockAdjustment
    ): void {
        /** @var \App\Models\Customer|null $customer */
        $customer = self::handleCustomerDependantFields($customerId, $set);

        if (is_null($customer) || is_null($referenceDate)) {
            return;
        }

        if ($lendingStockAdjustment) {
            return;
        }

        $stockMovementSummary = StockMovementSummary::query()
            ->where('customer_id', $customerId)
            ->where('movement_date', carbon($referenceDate)->format('Y-m-d'))
            ->get()
            ->map(function (StockMovementSummary $stockMovementSummary): array {
                return [
                    'product_id' => $stockMovementSummary->product_id,
                    'old_stock_quantity' => (int) $stockMovementSummary->previous_stock_quantity,
                    'collected_quantity' => (int) $stockMovementSummary->collected_quantity,
                    'delivered_quantity' => (int) $stockMovementSummary->delivered_quantity,
                    'out_of_movement_quantity' => (int) $stockMovementSummary->out_of_movement_quantity,
                    'adjustment_quantity' => (int) $stockMovementSummary->adjustment_quantity,
                    'current_stock_quantity' => (int) $stockMovementSummary->stock_quantity,
                ];
            })
            ->toArray();

        $set('products', array_values($stockMovementSummary));

        session()->put('products_reference', array_values($stockMovementSummary));
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?string $customerId, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_trading_name', $customer?->trading_name ?? '');
        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }

    /**
     * Calculate the row fields based on input.
     *
     * @param  \Filament\Forms\Get $get
     * @param  \Filament\Forms\Set $set
     * @param  string $originField
     * @param  int $rowId
     * @return void
     */
    protected static function calculateFieldValues(
        \Filament\Forms\Get $get,
        \Filament\Forms\Set $set,
        string $originField,
        int $rowId
    ): void {
        $fields = [
            'out_of_movement_quantity',
            'adjustment_quantity',
            'current_stock_quantity'
        ];

        $products = $get('../../products');
        $productReference = session('products_reference')[$rowId][$originField];

        $differenceFromOriginField = ((float) $productReference) - ((float) $products[$rowId][$originField]);

        foreach ($fields as $fieldName) {
            $fieldProductReference = session('products_reference')[$rowId][$fieldName];
            $products[$rowId][$fieldName] = (float) $fieldProductReference - (float) $differenceFromOriginField;
        }

        $set('../../products', $products);
    }
}
