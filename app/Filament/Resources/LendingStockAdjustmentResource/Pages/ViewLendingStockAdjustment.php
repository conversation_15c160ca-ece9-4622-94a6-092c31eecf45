<?php

namespace App\Filament\Resources\LendingStockAdjustmentResource\Pages;

use App\Actions\LendingStockAdjustment\DeleteLendingStockAdjustment;
use App\Filament\Resources\LendingStockAdjustmentResource;
use App\Models\LendingStockAdjustmentItem;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\ViewRecord;
use Throwable;

class ViewLendingStockAdjustment extends ViewRecord
{
    protected static string $resource = LendingStockAdjustmentResource::class;

    /**
     * Configure the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            ActionGroup::make([
                DeleteAction::make()->using(function () {
                    try {
                        DeleteLendingStockAdjustment::run($this->record);
                        success_notification(__('lending_stock_adjustments.responses.delete.success'))->send();
                        return redirect()->route('filament.app.resources.lending-stock-adjustments.index');
                    } catch (Throwable $th) {
                        error($th);
                        error_notification()->send();
                    }
                })
            ])
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['products'] = $this->record->lendingStockAdjustmentItems
            ->map(fn (LendingStockAdjustmentItem $item): array => $item->toArray())
            ->toArray();

        return $data;
    }
}
