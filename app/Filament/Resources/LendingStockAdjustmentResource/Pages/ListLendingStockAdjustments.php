<?php

namespace App\Filament\Resources\LendingStockAdjustmentResource\Pages;

use App\Filament\Resources\LendingStockAdjustmentResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListLendingStockAdjustments extends ListRecords
{
    protected static string $resource = LendingStockAdjustmentResource::class;

    /**
     * Configure the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make()
        ];
    }
}
