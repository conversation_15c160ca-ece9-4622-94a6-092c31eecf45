<?php

namespace App\Filament\Resources\LendingStockAdjustmentResource\Pages;

use App\Filament\Resources\LendingStockAdjustmentResource;
use App\Models\Customer;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateLendingStockAdjustment extends CreateRecord
{
    protected static string $resource = LendingStockAdjustmentResource::class;

    /**
     * Runs before the form fields are validated when the form is submitted.
     *
     * @return void
     */
    protected function beforeValidate(): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($this->data['customer_id']);

        if (carbon($customer->service_started_at)->startOfDay()->gt(carbon($this->data['adjusted_at'])->startOfDay())) {
            error_notification('A data de assinatura do contrato é posterior à data de ajuste.')->send();
            $this->halt();
        }
    }

    /**
     * @inheritDoc
     */
    protected function handleRecordCreation(array $data): Model
    {
        return \App\Actions\LendingStockAdjustment\CreateLendingStockAdjustment::run($data);
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('lending_stock_adjustments.responses.create.success'))->send();
    }
}
