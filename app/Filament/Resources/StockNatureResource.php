<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockNatureResource\Concerns\HandlesStockNatureResourceForm;
use App\Filament\Resources\StockNatureResource\Concerns\HandlesStockNatureResourceTable;
use App\Filament\Resources\StockNatureResource\Pages\ManageStockNatures;
use App\Models\StockNature;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class StockNatureResource extends Resource
{
    use HandlesStockNatureResourceForm;
    use HandlesStockNatureResourceTable;

    protected static ?string $model = StockNature::class;
    protected static ?string $modelLabel = 'natureza de estoque';
    protected static ?string $pluralModelLabel = 'naturezas de estoque';
    protected static ?string $navigationGroup = 'Produtos';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    public static function getPages(): array
    {
        return [
            'index' => ManageStockNatures::route('/'),
        ];
    }
}
