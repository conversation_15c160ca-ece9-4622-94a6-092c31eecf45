<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OperatorResource\Concerns\HandlesOperatorResourceForm;
use App\Filament\Resources\OperatorResource\Concerns\HandlesOperatorResourceTable;
use App\Filament\Resources\OperatorResource\Pages\ManageOperators;
use App\Models\Operator;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class OperatorResource extends Resource
{
    use HandlesOperatorResourceForm;
    use HandlesOperatorResourceTable;

    protected static ?string $model = Operator::class;
    protected static ?string $modelLabel = 'operador';
    protected static ?string $pluralModelLabel = 'operadores';
    protected static ?string $navigationGroup = 'Acesso';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Configure the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageOperators::route('/'),
        ];
    }
}
