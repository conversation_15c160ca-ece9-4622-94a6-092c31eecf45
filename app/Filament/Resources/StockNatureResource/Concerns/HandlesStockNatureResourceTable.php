<?php

namespace App\Filament\Resources\StockNatureResource\Concerns;

use App\Enums\StockNatureLocationTypeEnum;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesStockNatureResourceTable
{
    /**
     * Get the table columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label(__('stock_natures.forms.fields.id')),
            TextColumn::make('name')->label(__('stock_natures.forms.fields.name')),
            TextColumn::make('location_type')
                ->label(__('stock_natures.forms.fields.location_type'))
                ->formatStateUsing(function (?string $state) {
                    return StockNatureLocationTypeEnum::getTranslated()[$state];
                }),
        ];
    }

    /**
     * Get the table filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('name')
                ->form([
                    TextInput::make('name')->label(__('deliveries.forms.fields.name')),
                ])
                ->query(function (Builder $query, array $data) {
                    return $query->where('name', 'like', "%{$data['name']}%");
                }),
            SelectFilter::make('location_type')
                ->label(__('categories.forms.fields.location_type'))
                ->options(StockNatureLocationTypeEnum::getTranslated()),
        ];
    }

    /**
     * Get the table actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ]),
        ];
    }

    /**
     * Get the table bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            DeleteBulkAction::make(),
        ];
    }
}
