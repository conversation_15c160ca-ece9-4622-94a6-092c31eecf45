<?php

namespace App\Filament\Resources\StockNatureResource\Concerns;

use App\Enums\StockNatureLocationTypeEnum;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

trait HandlesStockNatureResourceForm
{
    /**
     * Get the form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                TextInput::make('name')
                    ->label(__('stock_natures.forms.fields.name'))
                    ->required(),
                Select::make('location_type')
                    ->label(__('stock_natures.forms.fields.location_type'))
                    ->placeholder('Escolha uma opção')
                    ->required()
                    ->options(StockNatureLocationTypeEnum::getTranslated())
            ])
        ];
    }
}
