<?php

namespace App\Filament\Resources\StockNatureResource\Pages;

use App\Filament\Resources\StockNatureResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageStockNatures extends ManageRecords
{
    protected static string $resource = StockNatureResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
