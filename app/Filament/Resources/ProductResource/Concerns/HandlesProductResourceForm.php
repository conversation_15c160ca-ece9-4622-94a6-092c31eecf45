<?php

namespace App\Filament\Resources\ProductResource\Concerns;

use App\Models\Subcategory;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Support\RawJs;

trait HandlesProductResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Section::make('Geral')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('code')
                            ->label(__('products.forms.fields.code'))
                            ->required(),
                        TextInput::make('name')
                            ->label(__('products.forms.fields.name'))
                            ->required()
                            ->columnSpan(3),
                    ]),
                    Grid::make(1)->schema([
                        Textarea::make('description')
                            ->label(__('products.forms.fields.description'))
                            ->required(),
                    ]),
                    Grid::make(4)->schema([
                        Select::make('subcategory_id')
                            ->label(__('products.forms.fields.subcategory_name'))
                            ->required()
                            ->options(Subcategory::query()->pluck('name', 'id'))
                            ->columnSpan(3),
                        TextInput::make('default_price')
                            ->label(__('products.forms.fields.default_price'))
                            ->required()
                            ->mask(RawJs::make(<<<'JS'
                                'R$ ' + $money($input, ',')
                            JS,
                            )),
                    ]),
                ]),
            Section::make('Características')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('gross_weight')
                            ->label(__('products.forms.fields.gross_weight'))
                            ->numeric()
                            ->postfix('g'),
                        TextInput::make('net_weight')
                            ->label(__('products.forms.fields.net_weight'))
                            ->numeric()
                            ->postfix('g'),
                    ]),
                ]),
        ];
    }
}
