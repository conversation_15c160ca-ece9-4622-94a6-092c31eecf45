<?php

namespace App\Filament\Resources\ProductResource\Concerns;

use App\Models\Product;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesProductResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label(__('products.forms.fields.id')),
            TextColumn::make('code')->label(__('products.forms.fields.code')),
            TextColumn::make('name')->label(__('products.forms.fields.name')),
            TextColumn::make('subcategory.name')->label(__('products.forms.fields.subcategory_name')),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('id')
                ->form([TextInput::make('id')->label(__('products.forms.fields.id'))])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('id', 'like', "%{$data['id']}%");
                }),
            Filter::make('code')
                ->form([TextInput::make('code')->label(__('products.forms.fields.code'))])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('code', 'like', "%{$data['code']}%");
                }),
            Filter::make('name')
                ->form([TextInput::make('name')->label(__('products.forms.fields.name'))])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->where('name', 'like', "%{$data['name']}%");
                }),
            Filter::make('subcategory_name')
                ->form([TextInput::make('subcategory_name')->label(__('products.forms.fields.subcategory_name'))])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->whereHas('subcategory', function (Builder $query) use ($data): Builder {
                        return $query->where('name', 'like', "%{$data['subcategory_name']}%");
                    });
                }),
            TernaryFilter::make('Lançamentos excluídos')
                ->placeholder('Retirar lançamentos excluídos')
                ->trueLabel('Incluir lançamentos excluídos')
                ->falseLabel('Somente lançamentos excluídos')
                ->queries(
                    true: fn (Builder $query) => $query->withTrashed(),
                    false: fn (Builder $query) => $query->onlyTrashed(),
                    blank: fn (Builder $query) => $query->withoutTrashed(),
                ),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make()->mutateRecordDataUsing(fn (Product $product, array $data): array => array_merge($data, [
                    'default_price' => $product->friendly_default_price,
                ])),
                EditAction::make()->mutateRecordDataUsing(fn (Product $product, array $data): array => array_merge($data, [
                    'default_price' => $product->friendly_default_price,
                ])),
                DeleteAction::make(),
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            DeleteBulkAction::make(),
        ];
    }
}
