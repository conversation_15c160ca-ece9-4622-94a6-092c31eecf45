<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Actions\Product\CreateProduct;
use App\Filament\Resources\ProductResource;
use App\Models\Product;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;

class ManageProducts extends ManageRecords
{
    protected static string $resource = ProductResource::class;

    /**
     * Get the resource actions.
     *
     * @return array<int, string>
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make()->using(function (array $data): Product {
                return CreateProduct::run($data);
            }),
        ];
    }
}
