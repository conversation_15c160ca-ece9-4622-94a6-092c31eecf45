<?php

namespace App\Filament\Resources\OperatorResource\Pages;

use App\Actions\Operator\CreateOperator;
use App\Filament\Resources\OperatorResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageOperators extends ManageRecords
{
    protected static string $resource = OperatorResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data) {
                    try {
                        CreateOperator::run($data);
                        success_notification('O operador foi criado.')->send();
                    } catch (Throwable $th) {
                        error_notification($th)->send();
                    }
                }),
        ];
    }
}
