<?php

namespace App\Filament\Resources\OperatorResource\Concerns;

use App\Enums\RoleEnum;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Spatie\Permission\Models\Role;

trait HandlesOperatorResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                TextInput::make('name')
                    ->label(__('operators.forms.fields.name'))
                    ->required()
                    ->columnSpan(1),
                TextInput::make('email')
                    ->label(__('operators.forms.fields.email'))
                    ->required()
                    ->email()
                    ->columnSpan(1)
            ]),
            Grid::make(2)->schema([
                Select::make('role_id')
                    ->label(__('operators.forms.fields.role_id'))
                    ->required()
                    ->reactive()
                    ->options(
                        Role::query()
                            ->orderBy('name')
                            ->get()
                            ->mapWithKeys(fn(Role $role): array => [$role->id => RoleEnum::getTranslated()[$role->name]])
                            ->toArray()
                    ),
                TextInput::make('password')
                    ->label(__('operators.forms.fields.password')),
            ]),
            Grid::make(1)->schema([
                Toggle::make('active')
                    ->label(__('operators.forms.fields.active'))
                    ->default(true)
            ]),
            Section::make('Permissões')
                ->compact()
                ->visible(fn(Get $get): bool => (int) $get('role_id') === 2)
                ->schema([
                    Grid::make(4)->schema([
                        Toggle::make('get_roles')->label(__('permissions.get_roles')),
                        Toggle::make('create_roles')->label(__('permissions.create_roles')),
                        Toggle::make('update_roles')->label(__('permissions.update_roles')),
                        Toggle::make('delete_roles')->label(__('permissions.delete_roles')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_operators')->label(__('permissions.get_operators')),
                        Toggle::make('create_operators')->label(__('permissions.create_operators')),
                        Toggle::make('update_operators')->label(__('permissions.update_operators')),
                        Toggle::make('delete_operators')->label(__('permissions.delete_operators')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_banks')->label(__('permissions.get_banks')),
                        Toggle::make('create_banks')->label(__('permissions.create_banks')),
                        Toggle::make('update_banks')->label(__('permissions.update_banks')),
                        Toggle::make('delete_banks')->label(__('permissions.delete_banks')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_bank_slips')->label(__('permissions.get_bank_slips')),
                        Toggle::make('create_bank_slips')->label(__('permissions.create_bank_slips')),
                        Toggle::make('update_bank_slips')->label(__('permissions.update_bank_slips')),
                        Toggle::make('delete_bank_slips')->label(__('permissions.delete_bank_slips')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_categories')->label(__('permissions.get_categories')),
                        Toggle::make('create_categories')->label(__('permissions.create_categories')),
                        Toggle::make('update_categories')->label(__('permissions.update_categories')),
                        Toggle::make('delete_categories')->label(__('permissions.delete_categories')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_collections')->label(__('permissions.get_collections')),
                        Toggle::make('create_collections')->label(__('permissions.create_collections')),
                        Toggle::make('update_collections')->label(__('permissions.update_collections')),
                        Toggle::make('delete_collections')->label(__('permissions.delete_collections')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_contracts')->label(__('permissions.get_contracts')),
                        Toggle::make('create_contracts')->label(__('permissions.create_contracts')),
                        Toggle::make('update_contracts')->label(__('permissions.update_contracts')),
                        Toggle::make('delete_contracts')->label(__('permissions.delete_contracts')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_customers')->label(__('permissions.get_customers')),
                        Toggle::make('create_customers')->label(__('permissions.create_customers')),
                        Toggle::make('update_customers')->label(__('permissions.update_customers')),
                        Toggle::make('delete_customers')->label(__('permissions.delete_customers')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_customer_tags')->label(__('permissions.get_customer_tags')),
                        Toggle::make('create_customer_tags')->label(__('permissions.create_customer_tags')),
                        Toggle::make('update_customer_tags')->label(__('permissions.update_customer_tags')),
                        Toggle::make('delete_customer_tags')->label(__('permissions.delete_customer_tags')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_customer_groups')->label(__('permissions.get_customer_groups')),
                        Toggle::make('create_customer_groups')->label(__('permissions.create_customer_groups')),
                        Toggle::make('update_customer_groups')->label(__('permissions.update_customer_groups')),
                        Toggle::make('delete_customer_groups')->label(__('permissions.delete_customer_groups')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_deliveries')->label(__('permissions.get_deliveries')),
                        Toggle::make('create_deliveries')->label(__('permissions.create_deliveries')),
                        Toggle::make('update_deliveries')->label(__('permissions.update_deliveries')),
                        Toggle::make('delete_deliveries')->label(__('permissions.delete_deliveries')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_delivery_adjustments')->label(__('permissions.get_delivery_adjustments')),
                        Toggle::make('create_delivery_adjustments')->label(__('permissions.create_delivery_adjustments')),
                        Toggle::make('update_delivery_adjustments')->label(__('permissions.update_delivery_adjustments')),
                        Toggle::make('delete_delivery_adjustments')->label(__('permissions.delete_delivery_adjustments')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('create_delivery_batches')->label(__('permissions.create_delivery_batches')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_lending_stock_adjustments')->label(__('permissions.get_lending_stock_adjustments')),
                        Toggle::make('create_lending_stock_adjustments')->label(__('permissions.create_lending_stock_adjustments')),
                        Toggle::make('update_lending_stock_adjustments')->label(__('permissions.update_lending_stock_adjustments')),
                        Toggle::make('delete_lending_stock_adjustments')->label(__('permissions.delete_lending_stock_adjustments')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_payment_methods')->label(__('permissions.get_payment_methods')),
                        Toggle::make('create_payment_methods')->label(__('permissions.create_payment_methods')),
                        Toggle::make('update_payment_methods')->label(__('permissions.update_payment_methods')),
                        Toggle::make('delete_payment_methods')->label(__('permissions.delete_payment_methods')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_indices')->label(__('permissions.get_indices')),
                        Toggle::make('create_indices')->label(__('permissions.create_indices')),
                        Toggle::make('update_indices')->label(__('permissions.update_indices')),
                        Toggle::make('delete_indices')->label(__('permissions.delete_indices')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_products')->label(__('permissions.get_products')),
                        Toggle::make('create_products')->label(__('permissions.create_products')),
                        Toggle::make('update_products')->label(__('permissions.update_products')),
                        Toggle::make('delete_products')->label(__('permissions.delete_products')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_receivables')->label(__('permissions.get_receivables')),
                        Toggle::make('create_receivables')->label(__('permissions.create_receivables')),
                        Toggle::make('update_receivables')->label(__('permissions.update_receivables')),
                        Toggle::make('delete_receivables')->label(__('permissions.delete_receivables')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_tecnospeed_plugboleto_bank_return_files')->label(__('permissions.get_tecnospeed_plugboleto_bank_return_files')),
                        Toggle::make('create_tecnospeed_plugboleto_bank_return_files')->label(__('permissions.create_tecnospeed_plugboleto_bank_return_files')),
                        Toggle::make('update_tecnospeed_plugboleto_bank_return_files')->label(__('permissions.update_tecnospeed_plugboleto_bank_return_files')),
                        Toggle::make('delete_tecnospeed_plugboleto_bank_return_files')->label(__('permissions.delete_tecnospeed_plugboleto_bank_return_files')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_stock_locations')->label(__('permissions.get_stock_locations')),
                        Toggle::make('create_stock_locations')->label(__('permissions.create_stock_locations')),
                        Toggle::make('update_stock_locations')->label(__('permissions.update_stock_locations')),
                        Toggle::make('delete_stock_locations')->label(__('permissions.delete_stock_locations')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_stock_natures')->label(__('permissions.get_stock_natures')),
                        Toggle::make('create_stock_natures')->label(__('permissions.create_stock_natures')),
                        Toggle::make('update_stock_natures')->label(__('permissions.update_stock_natures')),
                        Toggle::make('delete_stock_natures')->label(__('permissions.delete_stock_natures')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_suppliers')->label(__('permissions.get_suppliers')),
                        Toggle::make('create_suppliers')->label(__('permissions.create_suppliers')),
                        Toggle::make('update_suppliers')->label(__('permissions.update_suppliers')),
                        Toggle::make('delete_suppliers')->label(__('permissions.delete_suppliers')),
                    ]),
                    Grid::make(4)->schema([
                        Toggle::make('get_payment_recovery_settings')->label(__('permissions.get_payment_recovery_settings')),
                        Toggle::make('create_payment_recovery_settings')->label(__('permissions.create_payment_recovery_settings')),
                        Toggle::make('update_payment_recovery_settings')->label(__('permissions.update_payment_recovery_settings')),
                        Toggle::make('delete_payment_recovery_settings')->label(__('permissions.delete_payment_recovery_settings')),
                    ]),
                ]),
        ];
    }
}
