<?php

namespace App\Filament\Resources\OperatorResource\Concerns;

use App\Actions\Operator\DeleteOperator;
use App\Actions\Operator\UpdateOperator;
use App\Models\Operator;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Spatie\Permission\Models\Permission;
use Throwable;

trait HandlesOperatorResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label(__('operators.forms.fields.id')),
            TextColumn::make('name')->label(__('operators.forms.fields.name')),
            TextColumn::make('email')->label(__('operators.forms.fields.email')),
            IconColumn::make('user.active')
                ->label(__('operators.forms.fields.active'))
                ->boolean()
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                EditAction::make()
                    ->mutateRecordDataUsing(function (Operator $record, array $data): array {
                        $record->load('user:id,role_id,active');

                        $data['role_id'] = $record->user->role_id;
                        $data['active'] = $record->user->active;

                        $data = array_merge(
                            $data,
                            $record->user->permissions
                                ->mapWithKeys(fn (Permission $permission): array => [$permission->name => true])
                                ->toArray()
                        );

                        return $data;
                    })
                    ->using(function (Operator $operator, array $data) {
                        try {
                            UpdateOperator::run($operator, $data);
                            success_notification('O operador foi atualizado.')->send();
                        } catch (Throwable $th) {
                            error_notification($th)->send();
                        }
                    }),
                DeleteAction::make()
                    ->using(function (Operator $operator) {
                        try {
                            DeleteOperator::run($operator);
                            success_notification('O operador foi excluído.')->send();
                        } catch (Throwable $th) {
                            error_notification($th)->send();
                        }
                    }),
            ])
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            DeleteBulkAction::make(),
        ];
    }
}
