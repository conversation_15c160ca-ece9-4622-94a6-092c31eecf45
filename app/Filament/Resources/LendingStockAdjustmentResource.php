<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LendingStockAdjustmentResource\Concerns\HandlesLendingStockAdjustmentResourceForm;
use App\Filament\Resources\LendingStockAdjustmentResource\Concerns\HandlesLendingStockAdjustmentResourceTable;
use App\Filament\Resources\LendingStockAdjustmentResource\Pages\CreateLendingStockAdjustment;
use App\Filament\Resources\LendingStockAdjustmentResource\Pages\ListLendingStockAdjustments;
use App\Filament\Resources\LendingStockAdjustmentResource\Pages\ViewLendingStockAdjustment;
use App\Models\LendingStockAdjustment;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LendingStockAdjustmentResource extends Resource
{
    use HandlesLendingStockAdjustmentResourceForm;
    use HandlesLendingStockAdjustmentResourceTable;

    protected static ?string $model = LendingStockAdjustment::class;
    protected static ?string $modelLabel = 'ajuste de estoque';
    protected static ?string $pluralModelLabel = 'ajustes de estoque';
    protected static ?string $navigationGroup = 'Movimentação';
    protected static ?int $navigationSort = 4;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    /**
     * @inheritDoc
     */
    public static function getEloquentQuery(): Builder
    {
        return LendingStockAdjustment::query()
            ->orderByDesc('id');
    }

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ListLendingStockAdjustments::route('/'),
            'create' => CreateLendingStockAdjustment::route('/create'),
            'view' => ViewLendingStockAdjustment::route('/{record}'),
        ];
    }
}
