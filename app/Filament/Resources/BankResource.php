<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankResource\Pages;
use App\Filament\Resources\BankResource\RelationManagers\BankAccountsRelationManager;
use App\Models\Bank;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BankResource extends Resource
{
    protected static ?string $model = Bank::class;
    protected static ?string $modelLabel = 'banco';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Forms\Components\Toggle::make('active')
                    ->label(__('banks.forms.fields.active'))
                    ->default(true),
            ]),
            Grid::make(4)->schema([
                Forms\Components\TextInput::make('code')
                    ->label(__('banks.forms.fields.code'))
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->label(__('banks.forms.fields.name'))
                    ->required()
                    ->columnSpan(3),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label(__('banks.forms.fields.code')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('banks.forms.fields.name')),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ]),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            BankAccountsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanks::route('/'),
            'create' => Pages\CreateBank::route('/create'),
            'edit' => Pages\EditBank::route('/{record}/edit'),
        ];
    }
}
