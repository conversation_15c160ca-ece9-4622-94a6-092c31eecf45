<?php

namespace App\Filament\Resources\PaymentRecoverySettingResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;

trait HandlesPaymentRecoverySettingResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                TextInput::make('overdue_day_count_from')
                    ->label(__('payment_recovery_settings.forms.fields.overdue_day_count_from'))
                    ->required()
                    ->numeric()
                    ->minValue(1),
                TextInput::make('overdue_day_count_to')
                    ->label(__('payment_recovery_settings.forms.fields.overdue_day_count_to'))
                    ->numeric()
                    ->minValue(1),
            ]),
            Grid::make(1)->schema([
                Textarea::make('email_template')->label(__('payment_recovery_settings.forms.fields.email_template'))
            ]),
            Grid::make(1)->schema([
                Textarea::make('whatsapp_template')->label(__('payment_recovery_settings.forms.fields.whatsapp_template'))
            ]),
        ];
    }
}
