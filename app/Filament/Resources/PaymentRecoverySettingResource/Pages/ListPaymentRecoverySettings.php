<?php

namespace App\Filament\Resources\PaymentRecoverySettingResource\Pages;

use App\Filament\Resources\PaymentRecoverySettingResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPaymentRecoverySettings extends ListRecords
{
    protected static string $resource = PaymentRecoverySettingResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
