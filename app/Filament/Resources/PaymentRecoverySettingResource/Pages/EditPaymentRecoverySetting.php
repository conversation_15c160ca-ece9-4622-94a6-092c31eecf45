<?php

namespace App\Filament\Resources\PaymentRecoverySettingResource\Pages;

use App\Filament\Resources\PaymentRecoverySettingResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditPaymentRecoverySetting extends EditRecord
{
    protected static string $resource = PaymentRecoverySettingResource::class;

    protected function getActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
