<?php

namespace App\Filament\Resources;

use App\Enums\CategoryTypeEnum;
use App\Filament\Resources\CategoryResource\Pages\ManageCategories;
use App\Models\Category;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;
    protected static ?string $modelLabel = 'categoria';
    protected static ?string $navigationGroup = 'Produtos';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    /**
     * Configure the resource's main form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('name')
                    ->required()
                    ->label(__('categories.forms.fields.name'))
                    ->columnSpan(3),
                Select::make('type')
                    ->required()
                    ->options(CategoryTypeEnum::getTranslated())
                    ->label(__('categories.forms.fields.type'))
                    ->placeholder('Escolha um tipo')
                    ->columnSpan(1),
            ]),
            Grid::make(1)->schema([
                TableRepeater::make('subcategories')
                    ->relationship('subcategories')
                    ->hiddenLabel()
                    ->addActionLabel('Adicionar subcategoria')
                    ->headers([
                        Header::make((__('subcategories.forms.fields.name'))),
                    ])
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                    ]),
            ]),
        ]);
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label(__('categories.forms.fields.id')),
                TextColumn::make('name')->label(__('categories.forms.fields.name')),
                TextColumn::make('type')
                    ->label(__('categories.forms.fields.type'))
                    ->formatStateUsing(function (string $state) {
                        return CategoryTypeEnum::getTranslated()[$state];
                    }),
            ])
            ->filters([
                Filter::make('name')
                    ->form([
                        TextInput::make('name')->label(__('categories.forms.fields.name')),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->where('name', 'like', "%{$data['name']}%");
                    }),
                SelectFilter::make('type')
                    ->label(__('categories.forms.fields.type'))
                    ->options(CategoryTypeEnum::getTranslated()),
                TernaryFilter::make('Lançamentos excluídos')
                    ->placeholder('Retirar lançamentos excluídos')
                    ->trueLabel('Incluir lançamentos excluídos')
                    ->falseLabel('Somente lançamentos excluídos')
                    ->queries(
                        true: fn (Builder $query) => $query->withTrashed(),
                        false: fn (Builder $query) => $query->onlyTrashed(),
                        blank: fn (Builder $query) => $query->withoutTrashed(),
                    ),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                DeleteBulkAction::make(),
            ]);
    }

    /**
     * Get the resource's pages.
     *
     * @return array<string, array<string, string>>
     */
    public static function getPages(): array
    {
        return [
            'index' => ManageCategories::route('/'),
        ];
    }
}
