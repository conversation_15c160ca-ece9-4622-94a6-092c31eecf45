<?php

namespace App\Filament\Resources\IntegrationLogResource\Concerns;

use App\Enums\IntegrationLogDataFlowEnum;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;

trait HandlesIntegrationLogResourceTable
{
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label(__('integration_logs.forms.fields.id')),
            TextColumn::make('integration_type')
                ->label(__('integration_logs.forms.fields.integration_type'))
                ->formatStateUsing(function (?string $state): ?string {
                    return '';
                }),
            TextColumn::make('entity_id')->label(__('integration_logs.forms.fields.entity_id')),
            TextColumn::make('entity_type')->label(__('integration_logs.forms.fields.entity_type')),
            TextColumn::make('data_flow')
                ->label(__('integration_logs.forms.fields.data_flow'))
                ->formatStateUsing(function (?string $state): ?string {
                    return IntegrationLogDataFlowEnum::getTranslated()[$state];
                })
        ];
    }

    public static function getTableFilters(): array
    {
        return [];
    }

    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make()
            ])
        ];
    }

    public static function getTableBulkActions(): array
    {
        return [];
    }
}
