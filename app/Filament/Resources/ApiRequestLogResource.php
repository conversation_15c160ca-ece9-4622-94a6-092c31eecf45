<?php

namespace App\Filament\Resources;

use App\Core\Filament\Filters\P4SellDateFilter;
use App\Enums\RoleEnum;
use App\Filament\Resources\ApiRequestLogResource\Pages;
use App\Models\ApiRequestLog;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Novadaemon\FilamentPrettyJson\PrettyJson;

class ApiRequestLogResource extends Resource
{
    protected static ?string $model = ApiRequestLog::class;
    protected static ?string $modelLabel = 'Log de API';
    protected static ?string $pluralModelLabel = 'Logs de API';
    protected static ?string $navigationGroup = 'Logs de API';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    public static function getEloquentQuery(): Builder
    {
        return ApiRequestLog::query()
            ->orderByDesc('id');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('status_code')
                    ->label('Status Code'),
            ]),
            Grid::make(2)->schema([
                PrettyJson::make('request_headers')
                    ->label('Headers do JSON de requisição')
                    ->formatStateUsing(fn(Get $get) => openssl_decrypt($get('request_headers'), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'))),
                PrettyJson::make('request_body')
                    ->label('JSON de requisição')
                    ->formatStateUsing(fn(Get $get) => openssl_decrypt($get('request_body'), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'))),
            ]),
            Grid::make(2)->schema([
                PrettyJson::make('response_headers')
                    ->label('Headers do JSON de resposta')
                    ->formatStateUsing(fn(Get $get) => openssl_decrypt($get('response_headers'), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'))),
                PrettyJson::make('response_body')
                    ->label('JSON de resposta')
                    ->formatStateUsing(fn(Get $get) => openssl_decrypt($get('response_body'), 'aes-256-cbc', config('app.qi'), iv: config('app.ve'))),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID'),
                TextColumn::make('model_id')
                    ->label('ID do modelo'),
                TextColumn::make('model_type')
                    ->label('Classe'),
                IconColumn::make('success')
                    ->label('Sucesso')
                    ->boolean(),
                TextColumn::make('status_code')
                    ->label('Status Code'),
                TextColumn::make('created_at')
                    ->label('Criado em')
                    ->formatStateUsing(fn(ApiRequestLog $apiRequestLog): string => format_datetime($apiRequestLog->created_at)),
            ])
            ->filters([
                SelectFilter::make('success')
                    ->label(__('api_request_logs.forms.fields.success'))
                    ->options([
                        true => 'Sim',
                        false => 'Não',
                    ]),
                P4SellDateFilter::buildFrom('api_request_logs', 'created_at', 'created_at_from'),
                P4SellDateFilter::buildTo('api_request_logs', 'created_at', 'created_at_to'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApiRequestLogs::route('/'),
            'view' => Pages\ViewApiRequestLog::route('/{record}'),
        ];
    }
}
