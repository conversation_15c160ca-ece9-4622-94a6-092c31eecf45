<?php

namespace App\Filament\Resources\ReceivableResource\Pages;

use App\Actions\Income\SendBillingReceiptEmail;
use App\Actions\Receivable\Integrations\Omie\CreateReceivableInOmie;
use App\Actions\Receivable\ReopenReceivable;
use App\Actions\Receivable\UpdateReceivable;
use App\Core\Http\Requests\Tokenizer;
use App\Enums\BankSlipStatusEnum;
use App\Filament\Resources\ReceivableResource;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\CustomerCoupon;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Throwable;

class EditReceivable extends EditRecord
{
    protected static string $resource = ReceivableResource::class;

    /**
     * Configure the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        /** @var \App\Models\BankSlip $bankSlip */
        $latestBankSlip = $this->record->bankSlips()
            ->where('status', BankSlipStatusEnum::Open->value)
            ->latest()
            ->first();

        if ($latestBankSlip) {
            $latestBankSlip->update(['omie_data' => OmieBankSlipService::make()->get($this->record, new OmieGetBankSlipDto($this->record->omie_id))]);
        }

        $bankSlipUrl = $latestBankSlip
            ? ($latestBankSlip->omie_data['cLinkBoleto'] ?? ('https://plugboleto.com.br/api/v1/boletos/impressao/' . $latestBankSlip->tecnospeed_id))
            : '';

        return [
            Action::make('Ver Pdf')
                ->url(function (): string {
                    return route('receivables.billing', [
                        'token' => base64_encode($this->record->document_id . '-' . Str::random(32))
                    ]);
                })
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->openUrlInNewTab(),
            Action::make('Ver boleto')
                ->url(fn(): string => route('receivables.bank_slip', ['token' => base64_encode($this->record->document_id . '-' . Str::random(32))]))
                ->hidden(
                    fn(): bool => $this->record->bankSlips()
                        ->where('status', BankSlipStatusEnum::Open->value)
                        ->count() === 0
                )
                ->icon('heroicon-o-document')
                ->color('gray')
                ->openUrlInNewTab(),
            ActionGroup::make([
                Action::make('Integrar com Omie')
                    ->icon('heroicon-o-arrow-path')
                    ->hidden(fn(): bool => !is_null($this->record->omie_id))
                    ->action(function (): void {
                        try {
                            CreateReceivableInOmie::run($this->record, auth()->id());
                            success_notification(__('receivables.responses.integrate_with_omie.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    }),
                Action::make('Gerar boleto')
                    ->url(function (): string {
                        return route('receivables.create_bank_slip', ['records' => Tokenizer::make([$this->record->id])]);
                    })
                    ->icon('heroicon-o-document-text')
                    ->hidden(fn(): bool => !is_null($this->record->settled_at))
                    ->requiresConfirmation(),
                Action::make('Enviar e-mail de fatura')
                    ->action(function (): void {
                        try {
                            SendBillingReceiptEmail::dispatch($this->record, false, false, auth()->id());
                            success_notification(__('receivables.responses.send_billing_receipt_email.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->icon('heroicon-s-envelope')
                    ->color('gray')
                    ->requiresConfirmation(),
                Action::make('Enviar cobrança via WhatsApp')
                    ->url(function (): string {
                        return route('receivables.load_wa_me_link', ['record' => $this->record->id]);
                    })
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-exclamation-circle')
                    ->color('gray'),
                Action::make('Quitar fatura')
                    ->url(function (): string {
                        return route('receivables.settle', ['record' => $this->record->id]);
                    })
                    ->icon('heroicon-s-banknotes')
                    ->hidden(fn(): bool => !is_null($this->record->settled_at))
                    ->requiresConfirmation(),
                Action::make('Reabrir fatura')
                    ->action(function (): void {
                        try {
                            ReopenReceivable::run($this->record);
                            success_notification(__('receivables.responses.reopen.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->hidden(function (): bool {
                        return !is_null($this->record->deleted_at)
                            || !is_null($this->record->settled_at)
                            || !$this->record->customer->active;
                    })
                    ->icon('heroicon-s-trash')
                    ->color('danger')
                    ->requiresConfirmation(),
            ]),

        ];
    }

    /** @inheritDoc */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['income_customer_coupons'] = CustomerCoupon::query()
            ->whereIn('id', $this->record->document->incomeCustomerCoupons->pluck('customer_coupon_id')->toArray())
            ->get()
            ->map(fn(CustomerCoupon $customerCoupon): array => $customerCoupon->toArray())
            ->toArray();

        return $data;
    }

    /**
     * Handle the record update process.
     *
     * @param  \Illuminate\Database\Eloquent\Model $record
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return UpdateReceivable::run($record, $data);
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('receivables.responses.update.success'))->send();
    }
}
