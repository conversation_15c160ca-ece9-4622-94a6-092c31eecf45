<?php

namespace App\Filament\Resources\ReceivableResource\Pages;

use App\Actions\Income\GenerateIncomesForCustomers;
use App\Actions\Receivable\ConsolidateReceivables;
use App\Core\Filament\Form\Fields\TextInput;
use App\Core\Http\Requests\Tokenizer;
use App\Enums\CustomerDefaultBillingPeriodEnum;
use App\Enums\RoleEnum;
use App\Filament\Resources\ReceivableResource;
use App\Filament\Resources\ReceivableResource\Widgets\ReceivablesOverview;
use App\Models\Customer;
use App\Models\Index;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

class ListReceivables extends ListRecords
{
    protected static string $resource = ReceivableResource::class;

    /**
     * Configure the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('generate_incomes')
                    ->label('Gerar faturas')
                    ->icon('heroicon-o-banknotes')
                    ->form([
                        Wizard::make([
                            Step::make('Parâmetros de faturamento')
                                ->description('Indique aqui os parâmetros usados para agrupar as faturas.')
                                ->schema([
                                    Grid::make(1)->schema([
                                        Select::make('default_billing_period')
                                            ->label(__('customers.forms.fields.default_billing_period'))
                                            ->required()
                                            ->reactive()
                                            ->options(array_merge(CustomerDefaultBillingPeriodEnum::getTranslated(), [
                                                'customer' => 'Cliente específico',
                                            ])),
                                    ]),
                                    Grid::make(1)->schema([
                                        Select::make('customer_id')
                                            ->label(__('collections.forms.fields.customer_id'))
                                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                                            ->reactive()
                                            ->visible(fn(\Filament\Forms\Get $get): bool => $get('default_billing_period') === 'customer')
                                            ->searchable()
                                            ->getSearchResultsUsing(function (string $search) {
                                                $taxIdNumber = get_numbers($search);

                                                return Customer::query()
                                                    ->where('name', 'like', "%$search%")
                                                    ->orWhere('trading_name', 'like', "%$search%")
                                                    ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                                        return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                                    })
                                                    ->get()
                                                    ->map(fn(Customer $customer) => [
                                                        'id' => $customer->id,
                                                        'name' => "$customer->name | $customer->trading_name"
                                                    ])
                                                    ->pluck('name', 'id');
                                            })
                                    ]),
                                    Grid::make(1)->schema([
                                        TextInput::make('date_to')
                                            ->label('Data até')
                                            ->type('date')
                                            ->required(),
                                    ]),
                                ]),
                            Step::make('Reajuste de valores')
                                ->description('Verifique se você deseja aplicar o reajuste das tabelas de preços dos clientes.')
                                ->schema([
                                    Grid::make(2)->schema([
                                        Select::make('index_id')
                                            ->label('Índice a ser utilizado')
                                            ->options(array_merge([
                                                null => 'Índice de cada cliente',
                                            ], Index::query()->orderBy('name')->get()->pluck('name', 'id')->toArray()))
                                            ->selectablePlaceholder(false),
                                        TextInput::make('readjustment_month_count')
                                            ->label('Quantidade de meses')
                                            ->default(12)
                                            ->numeric()
                                            ->required(),
                                    ]),
                                    Grid::make(1)->schema([
                                        TableRepeater::make('indices')
                                            ->label('Índices')
                                            ->default(Index::query()->orderBy('name')->get()->map(fn(Index $index): array => ['id' => $index->id, 'name' => $index->name, 'amount' => mask_percentage($index->amount)])->toArray())
                                            ->addable(false)
                                            ->deletable(false)
                                            ->reorderable(false)
                                            ->headers([
                                                Header::make('index_id')->label('Índice'),
                                                Header::make('index_amount')->label('Valor'),
                                            ])
                                            ->schema([
                                                TextInput::make('name')
                                                    ->readOnly(),
                                                TextInput::make('amount')
                                                    ->required()
                                                    ->mask(function () {
                                                        return \Filament\Support\RawJs::make(<<<'JS'
                                                            $money($input, ',') + '%'
                                                        JS);
                                                    }),
                                            ])
                                    ]),
                                    Grid::make(1)->schema([
                                        Toggle::make('readjust_amounts')
                                            ->label('Reajustar valores?')
                                            ->default(true),
                                    ]),
                                ]),
                        ]),
                    ])
                    ->action(function (array $data): void {
                        try {
                            GenerateIncomesForCustomers::dispatchSync($data, auth()->id());
                            success_notification(__('receivables.responses.generate_incomes.success'))->send();
                        } catch (Throwable $th) {
                            error($th);
                            error_notification($th->getMessage())->send();
                        }
                    }),
                Action::make('consolidate_receivables')
                    ->label('Consolidar faturas')
                    ->icon('heroicon-o-document')
                    ->action(function (): void {
                        try {
                            ConsolidateReceivables::dispatch(auth()->id());
                            success_notification(__('receivables.responses.consolidate_incomes.pending_process'))->send();
                        } catch (Throwable $th) {
                            error($th);
                            throw_error($th);
                        }
                    })
                    ->requiresConfirmation(),
            ])
        ];
    }

    /**
     * Get the page header's widgets.
     *
     * @return array
     */
    protected function getHeaderWidgets(): array
    {
        return [
            ReceivablesOverview::class,
        ];
    }
}
