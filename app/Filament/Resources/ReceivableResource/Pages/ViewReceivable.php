<?php

namespace App\Filament\Resources\ReceivableResource\Pages;

use App\Filament\Resources\ReceivableResource;
use App\Models\CustomerCoupon;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewReceivable extends ViewRecord
{
    protected static string $resource = ReceivableResource::class;

    /**
     * Configure the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            EditAction::make(),
        ];
    }

    /** @inheritDoc */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['income_customer_coupons'] = CustomerCoupon::query()
            ->whereIn('id', $this->record->document->incomeCustomerCoupons->pluck('customer_coupon_id')->toArray())
            ->get()
            ->map(fn(CustomerCoupon $customerCoupon): array => $customerCoupon->toArray())
            ->toArray();

        return $data;
    }
}
