<?php

namespace App\Filament\Resources\ReceivableResource\Widgets;

use App\Enums\RoleEnum;
use App\Models\Receivable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ReceivablesOverview extends BaseWidget
{
    /**
     * Get the widget's cards.
     *
     * @return array
     */
    protected function getCards(): array
    {
        if (!auth()->user()->hasRole(RoleEnum::Administrator->value)) {
            return [
                Stat::make('Valor faturado', '-')
                    ->description('Referente às faturas filtradas no período')
                    ->descriptionIcon('heroicon-s-banknotes')
                    ->color('secondary'),
                Stat::make('Valor recebido', '-')
                    ->description('Referente às faturas filtradas no período')
                    ->descriptionIcon('heroicon-s-banknotes')
                    ->color('success'),
                Stat::make('Valor em aberto', '-')
                    ->description('Referente às faturas filtradas no período')
                    ->descriptionIcon('heroicon-s-arrow-trending-up')
                    ->color('warning'),
                Stat::make('Valor atrasado', '-')
                    ->description('Referente às faturas filtradas no período')
                    ->descriptionIcon('heroicon-s-arrow-trending-down')
                    ->color('danger'),
            ];
        }

        $billedAmount = Receivable::query()
            ->select([DB::raw('COALESCE(SUM(receivables.updated_amount), 0) as billed_amount')])
            ->when(
                isset(session('tables')['ListReceivables_filters']['id']['id']) && session('tables')['ListReceivables_filters']['id']['id'] !== '',
                fn (Builder $query): Builder => $query->where('id', session('tables')['ListReceivables_filters']['id']['id'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name']) && session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'trading_name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from']) && session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '>=', session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to']) && session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '<=', session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from']) && session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '>=', session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to']) && session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '<=', session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from']) && session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '>=', session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_to']) && session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_to'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '<=', session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id']) && session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.customerGroup', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['salesman_name']['salesman_name']) && session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.contracts.salesman', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']) && !empty(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']),
                fn (Builder $query): Builder => $query->whereHas('customer.customerCustomerTags', function (Builder $query): Builder {
                    return $query->whereIn('customer_tag_id', session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']);
                })
            )
            ->first()
            ->billed_amount;

        $receivedAmount = Receivable::query()
            ->select([DB::raw('COALESCE(SUM(receivables.updated_amount), 0) as received_amount')])
            ->when(
                isset(session('tables')['ListReceivables_filters']['id']['id']) && session('tables')['ListReceivables_filters']['id']['id'] !== '',
                fn (Builder $query): Builder => $query->where('id', session('tables')['ListReceivables_filters']['id']['id'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name']) && session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'trading_name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['status']['status']) && session('tables')['ListReceivables_filters']['status']['status'] !== '',
                fn (Builder $query): Builder => $query
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] === 'settled', fn (Builder $query): Builder => $query->whereNotNull('settled_at'))
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] !== 'settled', fn (Builder $query): Builder => $query->whereRaw('0 = 1')),
                fn (Builder $query): Builder => $query->whereNotNull('settled_at')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from']) && session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '>=', session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to']) && session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '<=', session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from']) && session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '>=', session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to']) && session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '<=', session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from']) && session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '>=', session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to']) && session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '<=', session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id']) && session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.customerGroup', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['salesman_name']['salesman_name']) && session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.contracts.salesman', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']) && !empty(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']),
                fn (Builder $query): Builder => $query->whereHas('customer.customerCustomerTags', function (Builder $query): Builder {
                    return $query->whereIn('customer_tag_id', session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']);
                })
            )
            ->first()
            ->received_amount;

        $openAmount = Receivable::query()
            ->select([DB::raw('COALESCE(SUM(receivables.updated_amount), 0) as open_amount')])
            ->where('expires_at', '>=', now()->format('Y-m-d'))
            ->when(
                isset(session('tables')['ListReceivables_filters']['id']['id']) && session('tables')['ListReceivables_filters']['id']['id'] !== '',
                fn (Builder $query): Builder => $query->where('id', session('tables')['ListReceivables_filters']['id']['id'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name']) && session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'trading_name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['status']['status']) && session('tables')['ListReceivables_filters']['status']['status'] !== '',
                fn (Builder $query): Builder => $query
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] === 'open', fn (Builder $query): Builder => $query->whereNull('settled_at')->where('expires_at', '>=', now()->format('Y-m-d')))
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] !== 'open', fn (Builder $query): Builder => $query->whereRaw('0 = 1')),
                fn (Builder $query): Builder => $query->whereNull('settled_at')->where('expires_at', '>=', now()->format('Y-m-d'))
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from']) && session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '>=', session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to']) && session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '<=', session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from']) && session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '>=', session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to']) && session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '<=', session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from']) && session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '>=', session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to']) && session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '<=', session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id']) && session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.customerGroup', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['salesman_name']['salesman_name']) && session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.contracts.salesman', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']) && !empty(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']),
                fn (Builder $query): Builder => $query->whereHas('customer.customerCustomerTags', function (Builder $query): Builder {
                    return $query->whereIn('customer_tag_id', session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']);
                })
            )
            ->first()
            ->open_amount;

        $overdueAmount = Receivable::query()
            ->select([DB::raw('COALESCE(SUM(receivables.updated_amount), 0) as overdue_amount')])
            ->when(
                isset(session('tables')['ListReceivables_filters']['id']['id']) && session('tables')['ListReceivables_filters']['id']['id'] !== '',
                fn (Builder $query): Builder => $query->where('id', session('tables')['ListReceivables_filters']['id']['id'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name']) && session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'trading_name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_trading_name']['customer_trading_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['status']['status']) && session('tables')['ListReceivables_filters']['status']['status'] !== '',
                fn (Builder $query): Builder => $query
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] === 'overdue', fn (Builder $query): Builder => $query->whereNull('settled_at')->where('expires_at', '<', now()->format('Y-m-d')))
                    ->when(session('tables')['ListReceivables_filters']['status']['status'] !== 'overdue', fn (Builder $query): Builder => $query->whereRaw('0 = 1')),
                fn (Builder $query): Builder => $query->whereNull('settled_at')->where('expires_at', '<', now()->format('Y-m-d'))
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from']) && session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '>=', session('tables')['ListReceivables_filters']['issued_at_from']['issued_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to']) && session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('issued_at', '<=', session('tables')['ListReceivables_filters']['issued_at_to']['issued_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from']) && session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '>=', session('tables')['ListReceivables_filters']['expires_at_from']['expires_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to']) && session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'] !== '',
                fn (Builder $query): Builder => $query->where('expires_at', '<=', session('tables')['ListReceivables_filters']['expires_at_to']['expires_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from']) && session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '>=', session('tables')['ListReceivables_filters']['term_started_at_from']['term_started_at_from'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to']) && session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer', 'service_started_at', '<=', session('tables')['ListReceivables_filters']['term_started_at_to']['term_started_at_to'])
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id']) && session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.customerGroup', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['customer_group_id']['customer_group_id'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['salesman_name']['salesman_name']) && session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] !== '',
                fn (Builder $query): Builder => $query->whereRelation('customer.contracts.salesman', 'name', 'like', '%' . session('tables')['ListReceivables_filters']['salesman_name']['salesman_name'] . '%')
            )
            ->when(
                isset(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']) && !empty(session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']),
                fn (Builder $query): Builder => $query->whereHas('customer.customerCustomerTags', function (Builder $query): Builder {
                    return $query->whereIn('customer_tag_id', session('tables')['ListReceivables_filters']['customer_tags']['customer_tags']);
                })
            )
            ->first()
            ->overdue_amount;

        return [
            Stat::make('Valor faturado', mask_money($billedAmount))
                ->description('Referente às faturas filtradas no período')
                ->descriptionIcon('heroicon-s-banknotes')
                ->color('secondary'),
            Stat::make('Valor recebido', mask_money($receivedAmount))
                ->description('Referente às faturas filtradas no período')
                ->descriptionIcon('heroicon-s-banknotes')
                ->color('success'),
            Stat::make('Valor em aberto', mask_money($openAmount))
                ->description('Referente às faturas filtradas no período')
                ->descriptionIcon('heroicon-s-arrow-trending-up')
                ->color('warning'),
            Stat::make('Valor atrasado', mask_money($overdueAmount))
                ->description('Referente às faturas filtradas no período')
                ->descriptionIcon('heroicon-s-arrow-trending-down')
                ->color('danger'),
        ];
    }
}
