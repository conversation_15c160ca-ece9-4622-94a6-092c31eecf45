<?php

namespace App\Filament\Resources\ReceivableResource\Concerns;

use App\Actions\BankSlip\GenerateBankSlipFromReceivable;
use App\Actions\Income\SendBillingReceiptEmail;
use App\Actions\Receivable\ReopenReceivable;
use App\Core\Http\Requests\Tokenizer;
use App\Enums\BankSlipStatusEnum;
use App\Http\Integrations\Omie\DataTransferObjects\OmieGetBankSlipDto;
use App\Http\Integrations\Omie\Services\OmieBankSlipService;
use App\Models\CustomerTag;
use App\Models\Receivable;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Throwable;

trait HandlesReceivableResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('document.document_number')
                ->label('ID Fatura')
                ->sortable(),
            TextColumn::make('customer.trading_name')
                ->label(__('receivables.forms.fields.customer_trading_name'))
                ->sortable(),
            TextColumn::make('updated_amount')
                ->label('Valor')
                ->money('brl')
                ->sortable(),
            TextColumn::make('issued_at')
                ->label('Emissão')
                ->date('d/m/Y')
                ->sortable(),
            TextColumn::make('expires_at')
                ->label('Venc.')
                ->date('d/m/Y')
                ->sortable(),
            TextColumn::make('settled_at')
                ->label('Baixa')
                ->date('d/m/Y')
                ->sortable(),
            TextColumn::make('collection_count')
                ->label('Qtde. col.')
                ->sortable()
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('customer_trading_name')
                ->form([
                    TextInput::make('customer_trading_name')->label(__('receivables.forms.fields.customer_trading_name'))
                ])
                ->query(function (Builder $query, array $data) {
                    return $query->whereHas('customer', function (Builder $query) use ($data) {
                        return $query->where('trading_name', 'like', "%{$data['customer_trading_name']}%");
                    });
                }),
            Filter::make('customer_tags')
                ->form([
                    Select::make('customer_tags')
                        ->label('Tags de cliente')
                        ->multiple()
                        ->options(CustomerTag::orderBy('name')->get()->pluck('name', 'id')->toArray())
                ])
                ->query(function (Builder $query, array $data) {
                    return $query->when(!is_null($data['customer_tags']) && !empty($data['customer_tags']), function (Builder $query) use ($data): Builder {
                        return $query->whereHas('customer.customerCustomerTags', function (Builder $query) use ($data): Builder {
                            return $query->whereIn('customer_tag_id', $data['customer_tags']);
                        });
                    });
                }),
            Filter::make('status')
                ->form([
                    Select::make('status')
                        ->label('Status')
                        ->selectablePlaceholder(false)
                        ->options([
                            '' => 'Todos',
                            'settled' => 'Pago',
                            'open' => 'Em aberto',
                            'overdue' => 'Vencido',
                        ])
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->when(!empty($data['status']), function (Builder $query) use ($data): Builder {
                        return $query
                            ->when($data['status'] === 'settled', function (Builder $query): Builder {
                                return $query->whereNotNull('settled_at');
                            })
                            ->when($data['status'] === 'open', function (Builder $query): Builder {
                                return $query
                                    ->whereNull('settled_at')
                                    ->where('expires_at', '>=', now()->format('Y-m-d'));
                            })
                            ->when($data['status'] === 'overdue', function (Builder $query) use ($data): Builder {
                                return $query
                                    ->whereNull('settled_at')
                                    ->where('expires_at', '<', now()->format('Y-m-d'));
                            });
                    });
                }),
            Filter::make('issued_at_from')
                ->form([
                    TextInput::make('issued_at_from')
                        ->type('date')
                        ->label('Emitido em de')
                        ->default(now()->subDays(15)->format('Y-m-d')),
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['issued_at_from']), function (Builder $query) use ($data) {
                            return $query->where('issued_at', '>=', $data['issued_at_from']);
                        });
                }),
            Filter::make('issued_at_to')
                ->form([
                    TextInput::make('issued_at_to')
                        ->type('date')
                        ->label('Emitido em até')
                        ->default(now()->format('Y-m-d'))
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['issued_at_to']), function (Builder $query) use ($data) {
                            return $query->where('issued_at', '<=', $data['issued_at_to']);
                        });
                }),
            Filter::make('expires_at_from')
                ->form([
                    TextInput::make('expires_at_from')
                        ->type('date')
                        ->label('Vencimento em de'),
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['expires_at_from']), function (Builder $query) use ($data) {
                            return $query->where('expires_at', '>=', $data['expires_at_from']);
                        });
                }),
            Filter::make('expires_at_to')
                ->form([
                    TextInput::make('expires_at_to')
                        ->type('date')
                        ->label('Vencimento em até')
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['expires_at_to']), function (Builder $query) use ($data) {
                            return $query->where('expires_at', '<=', $data['expires_at_to']);
                        });
                }),
            Filter::make('term_started_at_from')
                ->form([
                    TextInput::make('term_started_at_from')
                        ->type('date')
                        ->label('Ini vig. contrato de'),
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['term_started_at_from']), function (Builder $query) use ($data) {
                            return $query->whereRelation('customer', 'service_started_at', '>=', $data['term_started_at_from']);
                        });
                }),
            Filter::make('term_started_at_to')
                ->form([
                    TextInput::make('term_started_at_to')
                        ->type('date')
                        ->label('Ini vig. contrato até')
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['term_started_at_to']), function (Builder $query) use ($data) {
                            return $query->whereRelation('customer', 'service_started_at', '<=', $data['term_started_at_to']);
                        });
                }),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make(),
                Action::make('Ver Pdf')
                    ->url(function (Receivable $record): string {
                        return route('receivables.billing', [
                            'token' => base64_encode($record->document_id . '-' . Str::random(32))
                        ]);
                    })
                    ->icon('heroicon-o-document-text')
                    ->openUrlInNewTab(),
                Action::make('Ver boleto')
                    ->url(fn(Receivable $record): string => route('receivables.bank_slip', ['token' => base64_encode($record->document_id . '-' . Str::random(32))]))
                    ->hidden(
                        fn(Receivable $record): bool => $record->bankSlips()
                            ->where('status', BankSlipStatusEnum::Open->value)
                            ->count() === 0
                    )
                    ->icon('heroicon-o-document')
                    ->openUrlInNewTab(),
                Action::make('Gerar boleto')
                    ->action(function (Receivable $record) {
                        try {
                            GenerateBankSlipFromReceivable::run($record, null, auth()->id());
                            success_notification('O boleto foi gerado.')->send();
                        } catch (Throwable $th) {
                            error_notification($th)->send();
                        }
                    })
                    ->icon('heroicon-o-document-text')
                    ->hidden(fn(Receivable $record): bool => !is_null($record->settled_at))
                    ->requiresConfirmation(),
                Action::make('Enviar e-mail de fatura')
                    ->form([
                        TextInput::make('to_emails')
                            ->label('E-mails')
                            ->helperText('Separe os e-mails por vírgula.')
                            ->default(fn(Receivable $record): string => $record->customer->billing_email)
                            ->required()
                    ])
                    ->action(function (Receivable $record, array $data): void {
                        try {
                            SendBillingReceiptEmail::run($record, false, false, auth()->id(), false, $data['to_emails']);
                            success_notification(__('receivables.responses.send_billing_receipt_email.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->icon('heroicon-s-envelope')
                    ->requiresConfirmation(),
                Action::make('Enviar cobrança via WhatsApp')
                    ->url(fn(Receivable $record): string => route('receivables.load_wa_me_link', $record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-exclamation-circle'),
                Action::make('Quitar fatura')
                    ->url(function (Receivable $record): string {
                        return route('receivables.settle', ['record' => $record->id]);
                    })
                    ->icon('heroicon-s-banknotes')
                    ->hidden(fn(Receivable $record): bool => !is_null($record->settled_at))
                    ->requiresConfirmation(),
                Action::make('Reabrir fatura')
                    ->action(function (Receivable $record): void {
                        try {
                            ReopenReceivable::run($record);
                            success_notification(__('receivables.responses.reopen.success'))->send();
                        } catch (Throwable $th) {
                            error_notification($th->getMessage())->send();
                        }
                    })
                    ->hidden(function (Receivable $record): bool {
                        return !is_null($record->deleted_at)
                            || !is_null($record->settled_at)
                            || !$record->customer->active;
                    })
                    ->icon('heroicon-s-trash')
                    ->color('danger')
                    ->requiresConfirmation(),
            ])
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            BulkAction::make('Gerar boletos')
                ->icon('heroicon-s-document')
                ->modalSubmitAction(function (Collection $records) {
                    return Action::make('Confirmar')
                        ->url(route('receivables.create_bank_slip', ['records' => Tokenizer::make($records->pluck('id')->toArray())]))
                        ->openUrlInNewTab();
                })
                ->requiresConfirmation()
                ->deselectRecordsAfterCompletion(),
            BulkAction::make('Enviar e-mails')
                ->icon('heroicon-s-envelope')
                ->action(function (Collection $records) {
                    $records->each(function (Receivable $receivable): void {
                        if ($receivable->email_sent) {
                            return;
                        }

                        SendBillingReceiptEmail::dispatch($receivable, false, false, auth()->id());
                    });

                    success_notification(__('receivables.responses.send_billing_receipt_email_batch.success'))->send();
                })
                ->requiresConfirmation()
                ->deselectRecordsAfterCompletion(),
        ];
    }
}
