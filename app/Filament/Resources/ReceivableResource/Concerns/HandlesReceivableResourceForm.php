<?php

namespace App\Filament\Resources\ReceivableResource\Concerns;

use App\Core\Filament\Form\Fields\TextInput;
use App\Models\Receivable;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Illuminate\Database\Eloquent\Builder;

trait HandlesReceivableResourceForm
{
    public static function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                Tabs::make('')->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('customer.name')
                                ->label(__('receivables.forms.fields.customer_id'))
                                ->disabled()
                                ->columnSpan(2)
                                ->formatStateUsing(function (Receivable $record) {
                                    return $record->customer->name;
                                }),
                            TextInput::make('customer.trading_name')
                                ->label(__('customers.forms.fields.trading_name'))
                                ->disabled()
                                ->formatStateUsing(function (Receivable $record) {
                                    return $record->customer->trading_name;
                                }),
                            TextInput::make('customer.tax_id_number')
                                ->label(__('customers.forms.fields.tax_id_number'))
                                ->disabled()
                                ->formatStateUsing(function (Receivable $record) {
                                    return $record->customer->friendly_tax_id_number;
                                })
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('updated_amount')
                                ->label(__('receivables.forms.fields.updated_amount'))
                                ->disabled(fn(Receivable $record): bool => !is_null($record->settled_at))
                                ->formatStateUsing(fn(Receivable $record): string => $record->friendly_updated_amount)
                                ->mask(\Filament\Support\RawJs::make(
                                    <<<'JS'
                                    'R$ ' + $money($input, ',')
                                JS
                                )),
                            TextInput::make('issued_at')
                                ->type('date')
                                ->label(__('receivables.forms.fields.issued_at'))
                                ->disabled(),
                            TextInput::make('expires_at')
                                ->type('date')
                                ->label(__('receivables.forms.fields.expires_at'))
                                ->required(),
                            TextInput::make('settled_at')
                                ->type('date')
                                ->label(__('receivables.forms.fields.settled_at'))
                                ->disabled(),
                        ]),
                    ]),
                    Tab::make('Cupons')->schema([
                        TableRepeater::make('income_customer_coupons')
                            ->hiddenLabel()
                            ->disabled()
                            ->addable(false)
                            ->deletable(false)
                            ->reorderable(false)
                            ->headers([
                                Header::make(__('customers.forms.fields.customer_coupons.amount')),
                                Header::make(__('customers.forms.fields.customer_coupons.expires_at')),
                                Header::make(__('customers.forms.fields.customer_coupons.additional_info')),
                            ])
                            ->schema([
                                Hidden::make('id'),
                                TextInput::make('amount')
                                    ->required()
                                    ->mask(\Filament\Support\RawJs::make(
                                        <<<'JS'
                                        'R$ ' + $money($input, ',')
                                    JS
                                    )),
                                TextInput::make('expires_at')
                                    ->required()
                                    ->type('date'),
                                TextInput::make('additional_info'),
                            ]),
                    ]),
                    Tab::make('Boletos')->schema([
                        TableRepeater::make('bank_slips')
                            ->relationship('bankSlips')
                            ->hiddenLabel()
                            ->disabled()
                            ->addable(false)
                            ->deletable(false)
                            ->reorderable(false)
                            ->headers([
                                Header::make(__('bank_slips.forms.fields.id')),
                                Header::make(__('bank_slips.forms.fields.amount')),
                                Header::make(__('bank_slips.forms.fields.expires_at')),
                            ])
                            ->schema([
                                TextInput::make('id'),
                                TextInput::make('amount')
                                    ->prefix('R$')
                                    ->numeric(),
                                TextInput::make('expires_at')
                                    ->type('date'),
                            ])
                    ]),
                    Tab::make('Baixas')->schema([
                        TableRepeater::make('receivable_settlements')
                            ->relationship('receivableSettlements')
                            ->hiddenLabel()
                            ->disabled()
                            ->addable(false)
                            ->reorderable(false)
                            ->deletable(false)
                            ->headers([
                                Header::make(__('receivable_settlements.forms.fields.operator_name')),
                                Header::make(__('receivable_settlements.forms.fields.settled_at')),
                            ])
                            ->schema([
                                TextInput::make('operator_name'),
                                TextInput::make('settled_at')
                                    ->type('date'),
                            ])
                    ]),
                    Tab::make('Emails')->schema([
                        Repeater::make('receivable_emails')
                            ->relationship('receivableEmails', fn(Builder $query) => $query->latest())
                            ->hiddenLabel()
                            ->collapsible()
                            ->collapsed()
                            ->disabled()
                            ->itemLabel(fn(array $state): string => implode(',', $state['to_emails']) . ' - ' . carbon($state['email_sent_at'])->format('d/m/Y H:i:s'))
                            ->addable(false)
                            ->deletable(false)
                            ->reorderable(false)
                            ->schema([
                                Grid::make(4)->schema([
                                    TextInput::make('mailgun_message_id')
                                        ->label(__('receivable_emails.forms.fields.mailgun_message_id'))
                                        ->disabled()
                                        ->columnSpan(2),
                                    TextInput::make('to_emails')
                                        ->label(__('receivable_emails.forms.fields.to_emails'))
                                        ->disabled(),
                                    DateTimePicker::make('email_sent_at')
                                        ->label(__('receivable_emails.forms.fields.email_sent_at'))
                                        ->timezone('-3:00'),
                                ]),
                                Grid::make(1)->schema([
                                    TableRepeater::make('receivable_email_interactions')
                                        ->relationship('receivableEmailInteractions')
                                        ->hiddenLabel()
                                        ->disabled()
                                        ->addable(false)
                                        ->deletable(false)
                                        ->reorderable(false)
                                        ->headers([
                                            Header::make(__('receivable_email_interactions.forms.fields.event')),
                                            Header::make(__('receivable_email_interactions.forms.fields.timestamp')),
                                        ])
                                        ->schema([
                                            TextInput::make('event')
                                                ->formatStateUsing(fn(string $state): string => match($state) {
                                                    'delivered' => 'Entregue',
                                                    'opened' => 'Aberto',
                                                    'clicked' => 'Clicado',
                                                    'complained' => 'Reclamado',
                                                    'failed' => 'Falhou',
                                                    'unsubscribed' => 'Cancelado',
                                                    default => 'Desconhecido',
                                                }),
                                            DateTimePicker::make('timestamp')
                                                ->formatStateUsing(fn(string $state): string => carbon(date('Y-m-d H:i:s', $state))->setTimezone('-3:00')->format('Y-m-d H:i:s')),
                                        ])
                                ])
                            ])
                    ]),
                ]),
            ]),
        ];
    }
}
