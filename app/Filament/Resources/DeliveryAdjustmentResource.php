<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DeliveryAdjustmentResource\Concerns\HandlesDeliveryAdjustmentResourceForm;
use App\Filament\Resources\DeliveryAdjustmentResource\Concerns\HandlesDeliveryAdjustmentResourceTable;
use App\Filament\Resources\DeliveryAdjustmentResource\Pages;
use App\Models\DeliveryAdjustment;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DeliveryAdjustmentResource extends Resource
{
    use HandlesDeliveryAdjustmentResourceForm;
    use HandlesDeliveryAdjustmentResourceTable;

    protected static ?string $model = DeliveryAdjustment::class;
    protected static ?string $modelLabel = 'ajuste de entrega';
    protected static ?string $pluralModelLabel = 'ajustes de entrega';
    protected static ?string $navigationGroup = 'Movimentação';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getEloquentQuery(): Builder
    {
        return DeliveryAdjustment::query()
            ->orderByDesc('id');
    }

    /**
     * Configure the resource's form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Configure the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeliveryAdjustments::route('/'),
            'create' => Pages\CreateDeliveryAdjustment::route('/create'),
            'edit' => Pages\EditDeliveryAdjustment::route('/{record}/edit'),
            'view' => Pages\ViewDeliveryAdjustment::route('/{record}'),
        ];
    }
}
