<?php

namespace App\Filament\Resources\CollectionResource\Concerns;

use App\Models\Collection;
use App\Models\CustomerProduct;
use App\Models\Customer;
use App\Models\Product;
use App\Models\StockMovementSummary;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

trait HandlesCollectionResourceForm
{
    /**
     * Get the form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        $collectionItemsCount = 0;
        $componentItemsCount = 0;

        return [
            Section::make('Geral')
                ->compact()
                ->schema([
                    Grid::make(4)->schema([
                        TextInput::make('collected_at')
                            ->type('date')
                            ->label(__('collections.forms.fields.collected_at'))
                            ->required()
                            ->lazy()
                            ->default(now()->format('Y-m-d'))
                            ->afterStateUpdated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                $set('collected_at_weekday', get_br_weekday($get('collected_at')));
                            })
                            ->afterStateHydrated(function (\Filament\Forms\Get $get, \Filament\Forms\Set $set): void {
                                $set('collected_at_weekday', get_br_weekday($get('collected_at')));
                            }),
                        TextInput::make('collected_at_weekday')
                            ->label(__('collections.forms.fields.collected_at_weekday'))
                            ->disabled()
                            ->default(get_br_weekday(now())),
                    ]),
                    Grid::make(4)->schema([
                        Select::make('customer_id')
                            ->label(__('collections.forms.fields.customer_id'))
                            ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                            ->columnSpan(2)
                            ->reactive()
                            ->required()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Customer::query()
                                    ->where('active', true)
                                    ->where(function (Builder $query) use ($search): Builder {
                                        $taxIdNumber = Str::remove(['.', '-', '/'], $search);

                                        return $query
                                            ->where('name', 'like', "%$search%")
                                            ->orWhere('trading_name', 'like', "%$search%")
                                            ->when($taxIdNumber !== '' && strlen($taxIdNumber) > 1, function (Builder $query) use ($taxIdNumber) {
                                                return $query->orWhere('tax_id_number', 'like', "%$taxIdNumber%");
                                            });
                                    })
                                    ->get()
                                    ->map(fn (Customer $customer) => [
                                        'id' => $customer->id,
                                        'name' => "$customer->name | $customer->trading_name"
                                    ])
                                    ->pluck('name', 'id');
                            })
                            ->getOptionLabelUsing(function (Collection $record): ?string {
                                return $record->customer->name;
                            })
                            ->afterStateHydrated(function (?string $state, \Filament\Forms\Set $set, ?Collection $record): void {
                                self::postProcessEntityIdSelectDataLoad($state, $set, $record);
                            })
                            ->afterStateUpdated(function (?string $state, \Filament\Forms\Set $set, ?Collection $record): void {
                                self::postProcessEntityIdSelectDataLoad($state, $set, $record);
                            }),
                        TextInput::make('customer_trading_name')
                            ->disabled()
                            ->label(__('collections.forms.fields.customer_trading_name')),
                        TextInput::make('customer_tax_id_number')
                            ->disabled()
                            ->label(__('collections.forms.fields.customer_tax_id_number'))
                    ]),
                    Grid::make(1)->schema([
                        Textarea::make('additional_info')
                            ->label(__('collections.forms.fields.additional_info'))
                            ->rows(3)
                            ->maxLength(500)
                    ])
                ]),
            Section::make('Itens')
                ->compact()
                ->schema([
                    Grid::make(1)->schema([
                        TableRepeater::make('products')
                            ->label('')
                            ->columns(4)
                            ->addActionLabel('Adicionar produto')
                            ->defaultItems(0)
                            ->afterStateHydrated(function (TableRepeater $component, ?Collection $record) use (&$collectionItemsCount, &$componentItemsCount) {
                                if ($collectionItemsCount === 0 && !is_null($record)) {
                                    if ($record->relationLoaded('collectionItems')) {
                                        $record->load('collectionItems');
                                    }

                                    $collectionItemsCount = $record->collectionItems->count();
                                }

                                $componentItemsCount = $component->getItemsCount();
                            })
                            ->headers([
                                Header::make(__('collections.forms.fields.products.product_id')),
                                Header::make(__('collections.forms.fields.products.quantity')),
                                Header::make(__('collections.forms.fields.products.current_stock_quantity')),
                            ])
                            ->schema([
                                Select::make('product_id')
                                    ->required()
                                    ->autofocus(function () use (&$collectionItemsCount, &$componentItemsCount) {
                                        return $componentItemsCount !== $collectionItemsCount;
                                    })
                                    ->searchable()
                                    ->getSearchResultsUsing(fn (string $search) => Product::pluckForSearchableSelect($search))
                                    ->getOptionLabelUsing(fn ($value) => Product::find($value)?->name ?? '')
                                    ->afterStateHydrated(function (Select $component, ?Collection $record) use (&$componentItemsCount, &$collectionItemsCount) {
                                        if (!is_null($record)) {
                                            if (!$record->relationLoaded('collectionItems')) {
                                                $record->load('collectionItems');
                                            }

                                            $collectionItemsCount = $record->collectionItems->count();
                                        }

                                        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
                                    })
                                    ->afterStateUpdated(function (Select $component, ?Collection $record) use (&$componentItemsCount, &$collectionItemsCount) {
                                        if (!is_null($record)) {
                                            if (!$record->relationLoaded('collectionItems')) {
                                                $record->load('collectionItems');
                                            }

                                            $collectionItemsCount = $record->collectionItems->count();
                                        }

                                        $componentItemsCount = $component->getContainer()->getParentComponent()->getItemsCount();
                                    })
                                    ->columnSpan(3),
                                TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->lazy()
                                    ->suffix(function (?int $state, \Filament\Forms\Get $get) {
                                        if (is_null($state)) {
                                            return '';
                                        }

                                        $stats = is_array($get('../../customer_basic_stock_stats'))
                                            ? collect($get('../../customer_basic_stock_stats'))
                                            : $get('../../customer_basic_stock_stats');

                                        $weekdayGroupedStats = $stats
                                            ->groupBy('weekday')
                                            ->toArray();

                                        if (!isset($weekdayGroupedStats[carbon($get('../../collected_at'))->dayOfWeek])) {
                                            return '';
                                        }

                                        $weekdayStats = $weekdayGroupedStats[carbon($get('../../collected_at'))->dayOfWeek];

                                        $weekdayStats = collect($weekdayStats)->groupBy('product_id');

                                        if (!isset($weekdayStats[$get('product_id')])) {
                                            return '';
                                        }

                                        $indexedWeekdayStats = $weekdayStats[$get('product_id')]->mapWithKeys(fn (array $statsItem): array => [
                                            $statsItem['stats_type'] => $statsItem
                                        ])->toArray();

                                        $max = (int)$indexedWeekdayStats['basic-mean']['amount'] + (int) $indexedWeekdayStats['standard-deviation']['amount'];
                                        $min = (int)$indexedWeekdayStats['basic-mean']['amount'] - (int) $indexedWeekdayStats['standard-deviation']['amount'];

                                        if ($state > $max || $state < $min) {
                                            return new HtmlString("<span style='color: red;'><strong>Atenção!</strong></span><br><span style='font-size: 1rem; color: black'>Max: $max | Min: $min</span><br>");
                                        }

                                        return '';
                                    }),
                                TextInput::make('current_stock_quantity')
                                    ->disabled()
                                    ->numeric()
                            ])
                    ])
                ]),
            Grid::make(1)
                ->hiddenOn('view')
                ->schema([
                    Toggle::make('send_email')
                        ->label('Enviar e-mail de recibo?')
                        ->default(true)
                ]),
            Section::make('E-mails de recibo')
                ->compact()
                ->hiddenOn('create')
                ->schema([
                    TableRepeater::make('collection_receipt_emails')
                        ->relationship('collectionReceiptEmails')
                        ->label('')
                        ->columns(4)
                        ->addable(false)
                        ->deletable(false)
                        ->reorderable(false)
                        ->headers([
                            Header::make(__('collections.forms.fields.collection_receipt_emails.email_sent_at')),
                            Header::make(__('collections.forms.fields.collection_receipt_emails.to_emails')),
                        ])
                        ->schema([
                            TextInput::make('email_sent_at')
                                ->type('date')
                                ->disabled(),
                            TextInput::make('to_emails')
                                ->disabled()
                                ->columnSpan(3)
                        ])
                ])
        ];
    }

    /**
     * Handle the customer ID select data load post processing.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @param  \App\Models\Collection|null $collection
     * @return void
     */
    protected static function postProcessEntityIdSelectDataLoad(
        ?string $customerId,
        \Filament\Forms\Set $set,
        ?Collection $collection
    ): void {
        /** @var \App\Models\Customer|null $customer */
        $customer = self::handleCustomerDependantFields($customerId, $set);

        if (is_null($customer)) {
            return;
        }

        if ($collection) {
            return;
        }

        $set('customer_basic_stock_stats', $customer->customerBasicStockStats);

        $products = $customer->customerProducts
            ->load('product:id,name')
            ->sortBy('product.name')
            ->filter(fn (CustomerProduct $customerProduct) => $customerProduct->visible_in_collections)
            ->map(function (CustomerProduct $customerProduct) use ($customer): array {
                $quantity = StockMovementSummary::query()
                    ->where('customer_id', $customer->id)
                    ->where('product_id', $customerProduct->product_id)
                    ->orderByDesc('movement_date')
                    ->first()
                    ?->stock_quantity;

                return [
                    'product_id' => $customerProduct->product_id,
                    'product_name' => $customerProduct->product?->name,
                    'quantity' => 0,
                    'current_stock_quantity' => $quantity
                        ? (int) $quantity
                        : 0,
                ];
            })
            ->toArray();

        $orderedItems = [];

        foreach ($products as $product) {
            $orderedItems[] = $product;
        }

        $set('products', $orderedItems);
    }

    /**
     * Handle the customer dependant fields.
     *
     * @param  string|null $customerId
     * @param  \Filament\Forms\Set $set
     * @return \App\Models\Customer|null
     */
    protected static function handleCustomerDependantFields(?string $customerId, \Filament\Forms\Set $set): ?Customer
    {
        if (is_null($customerId)) {
            return null;
        }

        /** @var \App\Models\Customer|null $customer */
        $customer = Customer::find($customerId);

        $set('customer_trading_name', $customer?->trading_name ?? '');
        $set('customer_tax_id_number', $customer?->friendly_tax_id_number ?? '');

        return $customer;
    }
}
