<?php

namespace App\Filament\Resources\CollectionResource\Concerns;

use App\Actions\Collection\DeleteCollection;
use App\Actions\Collection\SendCollectionReceiptEmail;
use App\Models\Collection;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

trait HandlesCollectionResourceTable
{
    /**
     * Get the table columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('id')->label(__('collections.forms.fields.id')),
            TextColumn::make('customer.trading_name')->label(__('collections.forms.fields.customer_trading_name')),
            TextColumn::make('collected_at')
                ->label(__('collections.forms.fields.collected_at'))
                ->dateTime('d/m/Y'),
            TextColumn::make('created_at')
                ->label(__('collections.forms.fields.created_at'))
                ->dateTime('d/m/Y'),
        ];
    }

    /**
     * Get the table filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            Filter::make('customer_trading_name')
                ->form([TextInput::make('customer_trading_name')->label(__('collections.forms.fields.customer_trading_name'))])
                ->query(fn (Builder $query, array $data): Builder => $query->whereRelation('customer', 'trading_name', 'like', "%{$data['customer_trading_name']}%")),
            Filter::make('collected_at')
                ->form([
                    TextInput::make('collected_at_from')
                        ->type('date')
                        ->label('Coletado em de'),
                    TextInput::make('collected_at_to')
                        ->type('date')
                        ->label('Coletado em até')
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['collected_at_from']), fn (Builder $query): Builder => $query->where('collected_at', '>=', $data['collected_at_from']))
                        ->when(!is_null($data['collected_at_to']), fn (Builder $query): Builder => $query->where('collected_at', '<=', $data['collected_at_to']));
                }),
            Filter::make('created_at')
                ->form([
                    TextInput::make('created_at_from')
                        ->type('date')
                        ->label('Criado em de'),
                    TextInput::make('created_at_to')
                        ->type('date')
                        ->label('Criado em até')
                ])
                ->query(function (Builder $query, array $data) {
                    return $query
                        ->when(!is_null($data['created_at_from']), fn (Builder $query): Builder => $query->whereDate('created_at', '>=', $data['created_at_from']))
                        ->when(!is_null($data['created_at_to']), fn (Builder $query): Builder => $query->whereDate('created_at', '<=', $data['created_at_to']));
                }),
            TernaryFilter::make('Lançamentos excluídos')
                ->placeholder('Retirar lançamentos excluídos')
                ->trueLabel('Incluir lançamentos excluídos')
                ->falseLabel('Somente lançamentos excluídos')
                ->queries(
                    true: fn (Builder $query) => $query->withTrashed(),
                    false: fn (Builder $query) => $query->onlyTrashed(),
                    blank: fn (Builder $query) => $query->withoutTrashed(),
                )
        ];
    }

    /**
     * Get the table actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                EditAction::make()
                    ->hidden(function (Collection $record): bool {
                        return $record->incomeCollections->count() > 0;
                    }),
                Action::make('Gerar Pdf')
                    ->url(fn (Collection $record): string => route('collections.generate_pdf', $record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-document')
                    ->requiresConfirmation(),
                Action::make('Imprimir')
                    ->url(fn (Collection $record): string => route('collections.generate_receipt', $record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-s-printer')
                    ->requiresConfirmation(),
                Action::make('Enviar email de recibo')
                    ->action(function (Collection $record) {
                        try {
                            SendCollectionReceiptEmail::dispatch($record, false, false, auth()->id());
                            success_notification(__('collections.responses.send_collection_receipt_email.success'))->send();
                        } catch (Throwable) {
                            error_notification()->send();
                        }
                    })
                    ->icon('heroicon-s-envelope')
                    ->requiresConfirmation(),
                DeleteAction::make()
                    ->hidden(function (Collection $record): bool {
                        return $record->incomeCollections->count() > 0;
                    })
                    ->form([
                        Toggle::make('send_email')
                            ->label('Enviar email de confirmação')
                            ->default(true)
                    ])
                    ->using(function (Collection $record, array $data): void {
                        try {
                            DeleteCollection::run($record, auth()->id(), (bool) $data['send_email']);
                            success_notification(__('collections.responses.delete.success'))->send();
                        } catch (Throwable $th) {
                            error($th);
                            error_notification()->send();
                        }
                    }),
            ])
        ];
    }

    /**
     * Get the table bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [
            BulkAction::make('bulk_print')
                ->label('Imprimir lote')
                ->color('gray')
                ->icon('heroicon-s-envelope')
                ->modalSubmitAction(function (\Illuminate\Support\Collection $records) {
                    $recordsIds = implode(',', $records->pluck('id')->toArray());

                    return Action::make('Confirmar')
                        ->url(route('collections.generate_receipts', base64_encode($recordsIds)))
                        ->openUrlInNewTab();
                })
                ->requiresConfirmation()
                ->deselectRecordsAfterCompletion()
        ];
    }
}
