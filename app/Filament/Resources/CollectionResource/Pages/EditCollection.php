<?php

namespace App\Filament\Resources\CollectionResource\Pages;

use App\Actions\Collection\DeleteCollection;
use App\Actions\Collection\SendCollectionReceiptEmail;
use App\Filament\Resources\CollectionResource;
use App\Models\Collection;
use App\Models\CollectionItem;
use App\Models\Customer;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Throwable;

class EditCollection extends EditRecord
{
    protected static string $resource = CollectionResource::class;

    /**
     * Get the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('Gerar Pdf')
                    ->url(fn (): string => route('collections.generate_pdf', $this->record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-document')
                    ->requiresConfirmation(),
                Action::make('Imprimir')
                    ->url(fn (): string => route('collections.generate_receipt', $this->record))
                    ->openUrlInNewTab()
                    ->icon('heroicon-s-printer')
                    ->requiresConfirmation(),
                Action::make('Enviar email de recibo')
                    ->action(function (): void {
                        try {
                            SendCollectionReceiptEmail::dispatch($this->record, false, false, auth()->id());
                            success_notification(__('collections.responses.send_collection_receipt_email.success'))->send();
                        } catch (Throwable) {
                            error_notification()->send();
                        }
                    })
                    ->icon('heroicon-s-envelope')
                    ->requiresConfirmation(),
                    DeleteAction::make()
                    ->form([
                        Toggle::make('send_email')
                            ->label('Enviar email de confirmação')
                            ->default(true)
                    ])
                    ->using(function (Collection $record, array $data) {
                        try {
                            DeleteCollection::run($record, auth()->id(), (bool) $data['send_email']);
                            success_notification(__('collections.responses.delete.success'))->send();
                            return redirect()->route('filament.app.resources.collections.index');
                        } catch (Throwable $th) {
                            error($th);
                            error_notification()->send();
                        }
                    }),
            ])
        ];
    }

    /**
     * Mutate the data array before filling the form.
     *
     * @param  array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Collection $collection */
        $collection = Collection::find($data['id']);

        $data['products'] = $collection->collectionItems->map(function (CollectionItem $collectionItem) {
            return [
                'product_id' => $collectionItem->product_id,
                'quantity' => (float) $collectionItem->quantity
            ];
        })->toArray();

        $data['customer_basic_stock_stats'] = $collection->customer->customerBasicStockStats;

        return $data;
    }

    /**
     * Runs before the form fields are validated when the form is submitted.
     *
     * @return void
     */
    protected function beforeValidate(): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($this->data['customer_id']);

        if (carbon($customer->service_started_at)->startOfDay()->gt(carbon($this->data['collected_at'])->startOfDay())) {
            error_notification('A data de assinatura do contrato é posterior à data de coleta.')->send();
            $this->halt();
        }
    }

    /**
     * Perform record update action.
     *
     * @param  \Illuminate\Database\Eloquent\Model $record
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return \App\Actions\Collection\EditCollection::run($record, $data, auth()->id());
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('collections.responses.update.success'))->send();
    }
}
