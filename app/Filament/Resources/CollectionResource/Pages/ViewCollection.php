<?php

namespace App\Filament\Resources\CollectionResource\Pages;

use App\Filament\Resources\CollectionResource;
use App\Models\Collection;
use App\Models\CollectionItem;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewCollection extends ViewRecord
{
    protected static string $resource = CollectionResource::class;

    /**
     * Get the page's actions.
     *
     * @return array
     */
    protected function getActions(): array
    {
        return [
            EditAction::make(),
        ];
    }

    /**
     * Mutate the data array before filling the form.
     *
     * @param  array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Collection $collection */
        $collection = Collection::find($data['id']);

        $data['products'] = $collection->collectionItems->map(function (CollectionItem $collectionItem) {
            return [
                'product_id' => $collectionItem->product_id,
                'quantity' => (float) $collectionItem->quantity
            ];
        })->toArray();

        $data['customer_basic_stock_stats'] = $collection->customer->customerBasicStockStats;

        return $data;
    }
}
