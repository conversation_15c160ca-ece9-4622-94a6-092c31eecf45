<?php

namespace App\Filament\Resources\CollectionResource\Pages;

use App\Filament\Resources\CollectionResource;
use App\Models\Customer;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateCollection extends CreateRecord
{
    protected static string $resource = CollectionResource::class;

    /**
     * Runs before the form fields are validated when the form is submitted.
     *
     * @return void
     */
    protected function beforeValidate(): void
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::find($this->data['customer_id']);

        if (carbon($customer->service_started_at)->startOfDay()->gt(carbon($this->data['collected_at'])->startOfDay())) {
            error_notification('A data de assinatura do contrato é posterior à data de coleta.')->send();
            $this->halt();
        }
    }

    /**
     * Handle the record creation.
     *
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordCreation(array $data): Model
    {
        return \App\Actions\Collection\CreateCollection::run($data, auth()->id());
    }

    /**
     * Build the notification for the process.
     *
     * @return \Filament\Notifications\Notification|null
     */
    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('collections.responses.create.success'))->send();
    }
}
