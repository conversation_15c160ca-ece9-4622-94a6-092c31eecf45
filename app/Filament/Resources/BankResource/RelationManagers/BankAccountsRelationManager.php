<?php

namespace App\Filament\Resources\BankResource\RelationManagers;

use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BankAccountsRelationManager extends RelationManager
{
    protected static string $relationship = 'bankAccounts';
    protected static ?string $modelLabel = 'conta';
    protected static ?string $title = 'Contas';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(1)->schema([
                    Toggle::make('active')
                        ->label(__('bank_accounts.forms.fields.active'))
                        ->default(true),
                ]),
                Forms\Components\Grid::make(4)->schema([
                    TextInput::make('number')
                        ->label(__('bank_accounts.forms.fields.number'))
                        ->required(),
                    TextInput::make('digit')
                        ->label(__('bank_accounts.forms.fields.digit'))
                        ->required(),
                    TextInput::make('branch_number')
                        ->label(__('bank_accounts.forms.fields.branch_number'))
                        ->required(),
                    TextInput::make('branch_digit')
                        ->label(__('bank_accounts.forms.fields.branch_digit'))
                        ->required(),
                ]),
                Forms\Components\Grid::make(1)->schema([
                    TableRepeater::make('bank_account_wallets')
                        ->relationship('bankAccountWallets')
                        ->label('Carteiras')
                        ->columns(4)
                        ->headers([
                            Header::make(__('bank_account_wallets.forms.fields.number')),
                            Header::make(__('bank_account_wallets.forms.fields.drawee_tax_id_number')),
                            Header::make(__('bank_account_wallets.forms.fields.omie_id')),
                            Header::make(__('bank_account_wallets.forms.fields.active')),
                        ])
                        ->schema([
                            TextInput::make('number')
                                ->required(),
                            TextInput::make('drawee_tax_id_number')
                                ->required(),
                            TextInput::make('omie_id')
                                ->required(),
                            Toggle::make('active')
                                ->default(true),
                        ]),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('number'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
