<?php

namespace App\Filament\Resources;

use App\Core\Filament\Form\Fields\TextInput;
use App\Filament\Resources\IndexResource\Pages;
use App\Models\Index;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class IndexResource extends Resource
{
    protected static ?string $model = Index::class;
    protected static ?string $modelLabel = 'índice';
    protected static ?string $navigationGroup = 'Financeiro';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('name')
                    ->required()
                    ->label(__('indices.forms.fields.name'))
                    ->columnSpan(3),
                TextInput::make('amount')
                    ->label(__('indices.forms.fields.amount'))
                    ->mask(function () {
                        return \Filament\Support\RawJs::make(<<<'JS'
                            $money($input, ',') + '%'
                        JS);
                    }),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('indices.forms.fields.name')),
                TextColumn::make('amount')
                    ->label(__('indices.forms.fields.amount'))
                    ->formatStateUsing(fn(Index $index): string => $index->friendly_amount),
            ])
            ->filters([
                Filter::make('name')
                    ->form([TextInput::make('name')->label(__('customer_tags.forms.fields.name'))])
                    ->query(fn(Builder $query, array $data): Builder => $query->where('name', 'like', "%{$data['name']}%")),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageIndices::route('/'),
        ];
    }
}
