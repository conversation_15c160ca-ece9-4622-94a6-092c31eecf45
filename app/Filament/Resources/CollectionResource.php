<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CollectionResource\Concerns\HandlesCollectionResourceForm;
use App\Filament\Resources\CollectionResource\Concerns\HandlesCollectionResourceTable;
use App\Filament\Resources\CollectionResource\Pages\CreateCollection;
use App\Filament\Resources\CollectionResource\Pages\EditCollection;
use App\Filament\Resources\CollectionResource\Pages\ListCollections;
use App\Filament\Resources\CollectionResource\Pages\ViewCollection;
use App\Models\Collection;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CollectionResource extends Resource
{
    use HandlesCollectionResourceForm;
    use HandlesCollectionResourceTable;

    protected static ?string $model = Collection::class;
    protected static ?string $modelLabel = 'coleta';
    protected static ?string $navigationGroup = 'Movimentação';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-truck';

    /**
     * @inheritDoc
     */
    public static function getEloquentQuery(): Builder
    {
        return Collection::query()
            ->orderByDesc('id')
            ->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    /**
     * Build the resource's main form.
     *
     * @param  \Filament\Forms\Form $form
     * @return \Filament\Forms\Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema(self::getFormSchema());
    }

    /**
     * Build the resource's table.
     *
     * @param  \Filament\Tables\Table $table
     * @return \Filament\Tables\Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(self::getTableColumns())
            ->filters(self::getTableFilters())
            ->actions(self::getTableActions())
            ->bulkActions(self::getTableBulkActions());
    }

    /**
     * Get the resource's pages.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => ListCollections::route('/'),
            'create' => CreateCollection::route('/create'),
            'edit' => EditCollection::route('/{record}/edit'),
            'view' => ViewCollection::route('/{record}'),
        ];
    }
}
