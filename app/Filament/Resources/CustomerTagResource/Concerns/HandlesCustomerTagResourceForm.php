<?php

namespace App\Filament\Resources\CustomerTagResource\Concerns;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;

trait HandlesCustomerTagResourceForm
{
    /**
     * Configure the resource's form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(1)->schema([
                TextInput::make('name')
                    ->label(__('customer_tags.forms.fields.name'))
                    ->required()
            ])
        ];
    }
}
