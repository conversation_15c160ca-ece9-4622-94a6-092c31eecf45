<?php

namespace App\Filament\Resources\StockLocationResource\Pages;

use App\Filament\Resources\StockLocationResource;
use App\Models\StockLocationProduct;
use Filament\Resources\Pages\ViewRecord;

class ViewStockLocation extends ViewRecord
{
    protected static string $resource = StockLocationResource::class;

    /**
     * Handle the form data before fillinf the form.
     *
     * @param  array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var App\Models\StockLocation $stockLocation */
        $stockLocation = $this->getRecord();

        $stockLocation->stockLocationProducts->each(function (StockLocationProduct $stockLocationProduct) use (&$data) {
            $data['stock_location_products'][] = [
                'product_id' => $stockLocationProduct->product_id,
                'current_quantity' => $stockLocationProduct->current_quantity
            ];
        });

        return $data;
    }

    /**
     * Get the resource's actions.
     *
     * @return array<int, string>
     */
    protected function getActions(): array
    {
        return [];
    }
}
