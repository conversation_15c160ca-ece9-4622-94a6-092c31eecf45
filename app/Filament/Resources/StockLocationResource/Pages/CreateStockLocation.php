<?php

namespace App\Filament\Resources\StockLocationResource\Pages;

use App\Filament\Resources\StockLocationResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CreateStockLocation extends CreateRecord
{
    protected static string $resource = StockLocationResource::class;

    /**
     * Handle a custom process for the record creation.
     *
     * @param  array $data
     * @return \Illuminate\Database\Eloquent\Model
     */
    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            /** @var App\Models\StockLocation $stockLocation */
            $stockLocation = static::getModel()::create($data);

            collect($data['stock_location_products'])->each(function (array $product) use ($stockLocation) {
                $stockLocation->stockLocationProducts()->create([
                    'product_id' => $product['product_id'],
                    'initial_quantity' => $product['current_quantity'],
                    'current_quantity' => $product['current_quantity']
                ]);
            });

            return $stockLocation;
        });
    }
}
