<?php

namespace App\Filament\Resources\StockLocationResource\Pages;

use App\Filament\Resources\StockLocationResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListStockLocations extends ListRecords
{
    protected static string $resource = StockLocationResource::class;

    /**
     * Get the resource's actions.
     *
     * @return array<int, string>
     */
    protected function getActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
