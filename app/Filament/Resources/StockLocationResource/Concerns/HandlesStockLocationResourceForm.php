<?php

namespace App\Filament\Resources\StockLocationResource\Concerns;

use App\Models\StockLocationProduct;
use App\Models\StockNature;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

trait HandlesStockLocationResourceForm
{
    /**
     * Get the form schema.
     *
     * @return array
     */
    public static function getFormSchema(): array
    {
        return [
            Grid::make(2)->schema([
                TextInput::make('name')
                    ->label(__('stock_locations.forms.fields.name'))
                    ->required(),
                Select::make('stock_nature_id')
                    ->label(__('stock_locations.forms.fields.stock_nature_name'))
                    ->required()
                    ->options(StockNature::all()->pluck('name', 'id')),
            ]),
            Grid::make(1)->schema([
                TableRepeater::make('stock_location_products')
                    ->relationship('stockLocationProducts')
                    ->label('Itens')
                    ->headers([
                        Header::make(__('stock_locations.forms.fields.product_name')),
                        Header::make(__('stock_locations.forms.fields.current_quantity')),
                    ])
                    ->schema([
                        TextInput::make('product_id')
                            ->required()
                            ->formatStateUsing(function (?StockLocationProduct $record): ?string {
                                return $record?->product->name ?? '';
                            })
                            ->columnSpan(3),
                        TextInput::make('current_quantity')
                            ->required()
                            ->numeric(),
                    ])
                    ->columns(4),
            ]),
        ];
    }
}
