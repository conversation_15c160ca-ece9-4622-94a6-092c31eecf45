<?php

namespace App\Filament\Resources\StockLocationResource\Concerns;

use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;

trait HandlesStockLocationResourceTable
{
    /**
     * Get the table's columns.
     *
     * @return array
     */
    public static function getTableColumns(): array
    {
        return [
            TextColumn::make('name')
                ->label(__('stock_locations.forms.fields.name')),
            TextColumn::make('stockNature.name')
                ->label(__('stock_locations.forms.fields.stock_nature_name')),
        ];
    }

    /**
     * Get the table's filters.
     *
     * @return array
     */
    public static function getTableFilters(): array
    {
        return [
            TernaryFilter::make('Lançamentos excluídos')
                ->placeholder('Retirar lançamentos excluídos')
                ->trueLabel('Incluir lançamentos excluídos')
                ->falseLabel('Somente lançamentos excluídos')
                ->queries(
                    true: fn (Builder $query) => $query->withTrashed(),
                    false: fn (Builder $query) => $query->onlyTrashed(),
                    blank: fn (Builder $query) => $query->withoutTrashed(),
                ),
        ];
    }

    /**
     * Get the table's actions.
     *
     * @return array
     */
    public static function getTableActions(): array
    {
        return [
            ActionGroup::make([
                ViewAction::make(),
                DeleteAction::make(),
            ]),
        ];
    }

    /**
     * Get the table's bulk actions.
     *
     * @return array
     */
    public static function getTableBulkActions(): array
    {
        return [];
    }
}
