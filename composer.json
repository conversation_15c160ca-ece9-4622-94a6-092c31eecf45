{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "awcodes/filament-table-repeater": "^3.0", "barryvdh/laravel-dompdf": "^2.2", "cheesegrits/filament-google-maps": "^3.0", "filament/filament": "^3.2", "guzzlehttp/guzzle": "^7.8", "imanghafoori/laravel-masterpass": "^2.2", "laravel/framework": "^10.48", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.27", "leandrocfe/filament-apex-charts": "^3.1", "lorisleiva/laravel-actions": "^2.8", "maatwebsite/excel": "^3.1", "novadaemon/filament-pretty-json": "^2.3", "psr/simple-cache": "^3.0", "sammyjo20/saloon-laravel": "^1.6", "spatie/laravel-permission": "^6.7", "stancl/tenancy": "^3.8"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.19", "laravel/pint": "^1.0", "laravel/sail": "^1.14", "mockery/mockery": "^1.5", "nunomaduro/collision": "^6.1", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "spatie/laravel-ignition": "^2.7"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Support/Functions/datetime.php", "app/Support/Functions/error.php", "app/Support/Functions/itts.php", "app/Support/Functions/notifications.php", "app/Support/Functions/omie.php", "app/Support/Functions/p4m_tenant.php", "app/Support/Functions/report.php", "app/Support/Functions/text.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}