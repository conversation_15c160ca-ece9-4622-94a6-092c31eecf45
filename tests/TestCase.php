<?php

namespace Tests;

use App\Actions\Contract\CreateContract;
use App\Enums\ContractProcessTypeEnum;
use App\Enums\ContractTypeEnum;
use App\Enums\StockNatureLocationTypeEnum;
use App\Models\Contract;
use App\Models\Customer;
use App\Models\Product;
use App\Models\StockNature;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    /**
     * Setup the base test case.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        tenancy()->initialize('dev');

        auth()->loginUsingId(1);
    }

    /**
     * Create an example customer with its contract.
     *
     * @return \App\Models\Contract
     */
    protected function createExampleCustomerWithContract(): Contract
    {
        /** @var \App\Models\Customer $customer */
        $customer = Customer::factory()->create();

        $customer->stockLocations()->create([
            'stock_nature_id' => StockNature::query()
                ->where('location_type', StockNatureLocationTypeEnum::InThirdParty->value)
                ->first()
                ->id,
            'name' => $customer->name
        ]);

        $contractBaseDate = now()->subMonths(random_int(0, 11));

        return CreateContract::run([
            'alternate_id' => null,
            'type' => ContractTypeEnum::Customer->value,
            'process_type' => ContractProcessTypeEnum::Loan->value,
            'entity_id' => $customer->id,
            'term_started_at' => $contractBaseDate->format('Y-m-d'),
            'term_ended_at' => carbon($contractBaseDate)->addYear()->format('Y-m-d'),
            'signed_at' => $contractBaseDate->format('Y-m-d'),
            'items' => Product::query()
                ->inRandomOrder()
                ->take(random_int(2, 5))
                ->get()
                ->map(fn (Product $product) => [
                    'item_id' => (string) $product->id,
                    'quantity' => (string) random_int(10, 100),
                    'unit_amount' => (string) (random_int(100, 1000) / 100),
                    'visible_in_collections' => true
                ])
                ->toArray()
        ]);
    }
}
