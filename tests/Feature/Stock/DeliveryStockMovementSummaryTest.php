<?php

namespace Tests\Feature\Stock;

use App\Actions\Delivery\CreateDelivery;
use App\Actions\Delivery\EditDelivery;
use App\Models\ContractItem;
use App\Models\StockMovementSummary;
use Tests\TestCase;

class DeliveryStockMovementSummaryTest extends TestCase
{
    /**
     * 1) Basic case for single delivery.
     *
     * @return void
     */
    public function test_create_single_delivery_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Delivery $delivery */
        $delivery = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 10.0);
        });
    }

    /**
     * 2) Basic case for cumulative delivery quantities.
     *
     * @return void
     */
    public function test_create_deliveries_for_same_day_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Delivery $delivery1 */
        $delivery1 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        /** @var \App\Models\Delivery $delivery2 */
        $delivery2 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery1, $delivery2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery1->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 20.0);
        });
    }

    /**
     * 3) Basic case for delivery editing.
     *
     * @return void
     */
    public function test_create_and_edit_single_delivery_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Delivery $delivery */
        $delivery = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 10.0);
        });

        $delivery = EditDelivery::run($delivery, [
            'customer_id' => $delivery->customer_id,
            'delivered_at' => $delivery->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 20.0);
        });
    }

    /**
     * 4) Basic case for cumulative delivery quantities in a delivery editing scenario.
     *
     * @return void
     */
    public function test_create_and_edit_deliveries_for_the_same_day_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Delivery $delivery1 */
        $delivery1 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery1->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 10.0);
        });

        $delivery1 = EditDelivery::run($delivery1, [
            'customer_id' => $delivery1->customer_id,
            'delivered_at' => $delivery1->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery1->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 20.0);
        });

        /** @var \App\Models\Delivery $delivery2 */
        $delivery2 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 30.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 30.0);
        });

        $delivery2 = EditDelivery::run($delivery2, [
            'customer_id' => $delivery2->customer_id,
            'delivered_at' => $delivery2->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 40.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 40.0);
        });
    }

    /**
     * 5) Retroactive delivery creation and editing.
     *
     * @return void
     */
    public function test_create_and_edit_deliveries_with_retroactivity_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Delivery $delivery1 */
        $delivery1 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery1->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 10.0);
        });

        $delivery1 = EditDelivery::run($delivery1, [
            'customer_id' => $delivery1->customer_id,
            'delivered_at' => $delivery1->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery1->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 20.0);
        });

        /** @var \App\Models\Delivery $delivery2 */
        $delivery2 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now()->subDays(2),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 10.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 30.0);
        });

        $delivery2 = EditDelivery::run($delivery2, [
            'customer_id' => $delivery2->customer_id,
            'delivered_at' => $delivery2->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 20.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($delivery2->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 40.0);
        });

        /** @var \App\Models\Delivery $delivery3 */
        $delivery3 = CreateDelivery::run([
            'customer_id' => $contract->entity_id,
            'delivered_at' => now()->subDay(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 15
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery3->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 15.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 35.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($delivery3->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 35.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 55.0);
        });

        $delivery3 = EditDelivery::run($delivery3, [
            'customer_id' => $delivery3->customer_id,
            'delivered_at' => $delivery3->delivered_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 19
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($delivery3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($delivery3->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 19.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 39.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($delivery3->delivered_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 39.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === 0.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === 59.0);
        });
    }
}
