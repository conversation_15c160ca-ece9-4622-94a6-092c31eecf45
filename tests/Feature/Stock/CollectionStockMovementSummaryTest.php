<?php

namespace Tests\Feature\Stock;

use App\Actions\Collection\CreateCollection;
use App\Actions\Collection\EditCollection;
use App\Actions\LendingStockAdjustment\CreateLendingStockAdjustment;
use App\Actions\LendingStockAdjustment\DeleteLendingStockAdjustment;
use App\Models\ContractItem;
use App\Models\StockMovementSummary;
use Tests\TestCase;

class CollectionStockMovementSummaryTest extends TestCase
{
    /**
     * 1) Basic case for single collection.
     *
     * @return void
     */
    public function test_create_single_collection_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection */
        $collection = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);
        });
    }

    /**
     * 2) Basic case for cumulative collection quantities.
     *
     * @return void
     */
    public function test_create_collection_for_same_day_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection1 */
        $collection1 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        /** @var \App\Models\Collection $collection2 */
        $collection2 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1, $collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);
        });
    }

    /**
     * 3) Basic case for collection editing.
     *
     * @return void
     */
    public function test_create_and_edit_single_collection_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection */
        $collection = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);
        });

        $collection = EditCollection::run($collection, [
            'customer_id' => $collection->customer_id,
            'collected_at' => $collection->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);
        });
    }

    /**
     * 4) Basic case for cumulative collection quantities in a collection editing scenario.
     *
     * @return void
     */
    public function test_create_and_edit_collections_for_the_same_day_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection1 */
        $collection1 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);
        });

        $collection1 = EditCollection::run($collection1, [
            'customer_id' => $collection1->customer_id,
            'collected_at' => $collection1->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);
        });

        /** @var \App\Models\Collection $collection2 */
        $collection2 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 30.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -30.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -30.0);
        });

        $collection2 = EditCollection::run($collection2, [
            'customer_id' => $collection2->customer_id,
            'collected_at' => $collection2->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 40.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -40.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -40.0);
        });
    }

    /**
     * 5) Retroactive collection creation and editing.
     *
     * @return void
     */
    public function test_create_and_edit_collection_with_retroactivity_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection1 */
        $collection1 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);
        });

        $collection1 = EditCollection::run($collection1, [
            'customer_id' => $collection1->customer_id,
            'collected_at' => $collection1->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);
        });

        /** @var \App\Models\Collection $collection2 */
        $collection2 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now()->subDays(2),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -10.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -30.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -30.0);
        });

        $collection2 = EditCollection::run($collection2, [
            'customer_id' => $collection2->customer_id,
            'collected_at' => $collection2->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -40.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -40.0);
        });

        /** @var \App\Models\Collection $collection3 */
        $collection3 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now()->subDay(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 15
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 15.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -35.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -35.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -35.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -55.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -55.0);
        });

        $collection3 = EditCollection::run($collection3, [
            'customer_id' => $collection3->customer_id,
            'collected_at' => $collection3->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 19
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 19.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -39.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -39.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -39.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -59.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -59.0);
        });
    }

    /**
     * 6) Retroactive collection creation and editing with lending stock adjustment.
     *
     * @return void
     */
    public function test_create_and_edit_collection_with_retroactivity_and_adjustments_and_check_stock_movement_summary()
    {
        $contract = $this->createExampleCustomerWithContract();

        /** @var \App\Models\Collection $collection1 */
        $collection1 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);
        });

        $collection1 = EditCollection::run($collection1, [
            'customer_id' => $collection1->customer_id,
            'collected_at' => $collection1->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection1): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection1->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);
        });

        /** @var \App\Models\Collection $collection2 */
        $collection2 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now()->subDays(2),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 10
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 10.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -10.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -10.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -10.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -30.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -30.0);
        });

        $collection2 = EditCollection::run($collection2, [
            'customer_id' => $collection2->customer_id,
            'collected_at' => $collection2->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 20
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection2): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -20.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -20.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection2->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -40.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -40.0);
        });

        /** @var \App\Models\Collection $collection3 */
        $collection3 = CreateCollection::run([
            'customer_id' => $contract->entity_id,
            'collected_at' => now()->subDay(),
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 15
                ];
            })->toArray()
        ], 1, false);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 15.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -35.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -35.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -35.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -55.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -55.0);
        });

        $collection3 = EditCollection::run($collection3, [
            'customer_id' => $collection3->customer_id,
            'collected_at' => $collection3->collected_at,
            'products' => $contract->contractItems->map(function (ContractItem $contractItem) {
                return [
                    'product_id' => $contractItem->item_id,
                    'quantity' => 19
                ];
            })->toArray()
        ], 1);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 19.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -39.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -39.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -39.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -59.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -59.0);
        });

        $product = $contract->contractItems->first()->item;

        /** @var \App\Models\LendingStockAdjustment $lendingStockAdjustment */
        $lendingStockAdjustment = CreateLendingStockAdjustment::run([
            'customer_id' => $contract->entity_id,
            'adjusted_at' => now()->subDay(),
            'products' => [StockMovementSummary::query()
                ->where('customer_id', $contract->entity_id)
                ->where('product_id', $product->id)
                ->where('movement_date', now()->subDay()->format('Y-m-d'))
                ->get()
                ->map(function (StockMovementSummary $stockMovementSummary): array {
                    return [
                        'product_id' => $stockMovementSummary->product_id,
                        'old_stock_quantity' => (int) $stockMovementSummary->previous_stock_quantity,
                        'collected_quantity' => (int) $stockMovementSummary->collected_quantity,
                        'delivered_quantity' => (int) $stockMovementSummary->delivered_quantity,
                        'out_of_movement_quantity' => -29,
                        'adjustment_quantity' => 10,
                        'current_stock_quantity' => -29,
                    ];
                })
                ->first()]
        ]);

        /** @var \App\Models\StockMovementSummary $stockMovementSummary */
        $stockMovementSummary = StockMovementSummary::query()
            ->where('customer_id', $lendingStockAdjustment->customer_id)
            ->where('product_id', $product->id)
            ->where('movement_date', carbon($lendingStockAdjustment->adjusted_at)->format('Y-m-d'))
            ->first();

        $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
        $this->assertTrue((float)$stockMovementSummary->collected_quantity === 19.0);
        $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
        $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -29.0);
        $this->assertTrue((float)$stockMovementSummary->adjustment_quantity === 10.0);
        $this->assertTrue((float)$stockMovementSummary->stock_quantity === -29.0);

        /** @var \App\Models\StockMovementSummary $stockMovementSummary */
        $stockMovementSummary = StockMovementSummary::query()
            ->where('customer_id', $lendingStockAdjustment->customer_id)
            ->where('product_id', $product->id)
            ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
            ->first();

        $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -29.0);
        $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
        $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
        $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -49.0);
        $this->assertNull($stockMovementSummary->adjustment_quantity);
        $this->assertTrue((float)$stockMovementSummary->stock_quantity === -49.0);

        $lendingStockAdjustment->refresh();

        DeleteLendingStockAdjustment::run($lendingStockAdjustment);

        $contract->contractItems->each(function (ContractItem $contractItem) use ($collection3): void {
            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -20.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 19.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -39.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -39.0);

            /** @var \App\Models\StockMovementSummary $stockMovementSummary */
            $stockMovementSummary = StockMovementSummary::query()
                ->where('customer_id', $contractItem->contract->entity_id)
                ->where('product_id', $contractItem->item_id)
                ->where('movement_date', '>', carbon($collection3->collected_at)->format('Y-m-d'))
                ->first();

            $this->assertTrue((float)$stockMovementSummary->previous_stock_quantity === -39.0);
            $this->assertTrue((float)$stockMovementSummary->collected_quantity === 20.0);
            $this->assertTrue((float)$stockMovementSummary->delivered_quantity === 0.0);
            $this->assertTrue((float)$stockMovementSummary->out_of_movement_quantity === -59.0);
            $this->assertNull($stockMovementSummary->adjustment_quantity);
            $this->assertTrue((float)$stockMovementSummary->stock_quantity === -59.0);
        });
    }
}
