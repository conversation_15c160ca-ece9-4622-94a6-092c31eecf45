<?php

namespace Tests\Feature;

use App\Filament\Pages\Delivery\GenerateDeliveryMap;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GenerateDeliveryMapTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_instantiate_generate_delivery_map_page()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $page = new GenerateDeliveryMap();
        
        $this->assertInstanceOf(GenerateDeliveryMap::class, $page);
    }

    public function test_convert_itinerary_to_number_works_correctly()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $page = new GenerateDeliveryMap();
        
        // Test numeric itineraries
        $this->assertEquals('1', $page->convertItineraryToNumber('1'));
        $this->assertEquals('5', $page->convertItineraryToNumber('5'));
        
        // Test alphabetic itineraries
        $this->assertEquals('1', $page->convertItineraryToNumber('A'));
        $this->assertEquals('2', $page->convertItineraryToNumber('B'));
        $this->assertEquals('3', $page->convertItineraryToNumber('C'));
    }

    public function test_get_itinerary_color_returns_correct_colors()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $page = new GenerateDeliveryMap();
        
        $this->assertEquals('blue', $page->getItineraryColor('1'));
        $this->assertEquals('red', $page->getItineraryColor('2'));
        $this->assertEquals('green', $page->getItineraryColor('3'));
        $this->assertEquals('grey', $page->getItineraryColor('999'));
    }

    public function test_get_itinerary_color_hex_returns_correct_hex_colors()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $page = new GenerateDeliveryMap();
        
        $this->assertEquals('#0000FF', $page->getItineraryColorHex('1'));
        $this->assertEquals('#FF0000', $page->getItineraryColorHex('2'));
        $this->assertEquals('#008000', $page->getItineraryColorHex('3'));
        $this->assertEquals('#808080', $page->getItineraryColorHex('999'));
    }
}
