<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Faturamento nº{{ $document->id }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])

    <style>
        p {
            line-height: 0.9em;
        }
    </style>
</head>

<body>
    <div>
        <div id="report-header">
            <div class="header float-left">
                <p style="margin-top: 0; margin-bottom: 0;"><strong>{{ $document->customer->trading_name }}</strong></p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>{{ $document->customer->name }}</strong></p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>CNPJ:</strong> {{ mask_cnpj($document->customer->tax_id_number) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Fatura nº{{ $document->document_number }}</strong></p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Valor mínimo de faturamento:</strong> {{ mask_money($minimumBillingAmount) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Emitido em:</strong> {{ format_date($document->issued_at) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Recibo emitido em:</strong> {{ format_datetime(now()) }}</p><br>
            </div>
            <div class="float-right">
                <img class="p4m-img-nav" src="{{ $logoSrc }}" alt="Logo" />
            </div>
        </div>

        <hr class="clear-both">

        <div id="report-body">
            <div style="page-break-after: always;">
                <table width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; position: relative; line-height: 0.9em; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; text-align: center; width: 100%;">
                    <tr>
                        <td>
                            <p style="margin-top: 0; margin-bottom: 0; font-size: 1.2em"><strong>Valor da fatura:</strong> {{ mask_money($document->amount) }}</p>
                        </td>
                        <td>
                            <p style="margin-top: 0; margin-bottom: 0; font-size: 1.2em"><strong>Vencimento em:</strong> {{ format_date($document->receivables[0]->expires_at) }}</p>
                        </td>
                    </tr>
                </table>
                <hr>
                <hr>

                <p style="margin-bottom: 0; font-size: 1.2em">
                    <span><strong>Resumo das movimentações</strong></span>
                </p>
                <table class="table-sm table-borderless table-striped table" style="margin-bottom: 30px;">
                    <thead>
                        <th scope="col" style="text-align: center;">Item</th>
                        <th scope="col" style="text-align: center;">Quantidade</th>
                        <th scope="col" style="text-align: center;">Valor unitário</th>
                        <th scope="col" style="text-align: center;">Valor total</th>
                    </thead>
                    <tbody>
                        @foreach ($resumeItems as $item)
                            <tr style="line-height: 0.7em;">
                                <td scope="col" style="text-align: center;">{{ $item['product_name'] }}</td>
                                <td scope="col" style="text-align: center;">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                                <td scope="col" style="text-align: center;">{{ mask_money($item['unit_amount']) }}</td>
                                <td scope="col" style="text-align: center;">{{ mask_money($item['unit_amount'] * $item['quantity']) }}</td>
                            </tr>
                        @endforeach
                        <br>
                        <tr style="line-height: 1em; background-color: #ddd">
                            <td scope="col" style="text-align: center;"><strong>Total de itens</strong></td>
                            <td scope="col" style="text-align: center;"><strong>{{ number_format($resumeItems->sum(fn ($item) => $item['quantity']), 0, ',', '.') }}</strong></td>
                            <td scope="col" style="text-align: center;"></td>
                            <td scope="col" style="text-align: center;">{{ mask_money($resumeItems->sum(fn ($item) => $item['quantity'] * $item['unit_amount'])) }}</td>
                        </tr>
                        @if ($document->incomeCustomerCoupons->count() > 0)
                            @foreach ($document->incomeCustomerCoupons as $incomeCustomerCoupon)
                                <tr style="line-height: 1em; background-color: #eee">
                                    <td scope="col" style="text-align: center;">Desconto</td>
                                    <td scope="col" style="text-align: center;">1</td>
                                    <td scope="col" style="text-align: center;"></td>
                                    <td scope="col" style="text-align: center;">{{ $incomeCustomerCoupon->customerCoupon->type === \App\Enums\CouponTypeEnum::PERCENTAGE ? '-' . $incomeCustomerCoupon->customerCoupon->amount . '%' : '-' . mask_money($incomeCustomerCoupon->customerCoupon->amount) }}</td>
                                </tr>
                            @endforeach
                            <tr style="line-height: 1em; background-color: #ddd">
                                <td scope="col" style="text-align: center;">Total da fatura</td>
                                <td scope="col" style="text-align: center;">&nbsp;</td>
                                <td scope="col" style="text-align: center;">&nbsp;</td>
                                <td scope="col" style="text-align: center;">{{ mask_money($document->amount) }}</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <table width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; position: relative; line-height: 0.9em; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; text-align: center; width: 100%;">
                <tr>
                    <td>
                        <p style="margin-top: 0; margin-bottom: 0; font-size: 1.2em"><strong>Movimentação das coletas</strong></p>
                    </td>
                </tr>
            </table>
            <hr>
            <hr>

            @foreach ($document->incomeCollections as $incomeCollection)
                <p style="margin-bottom: 0">
                <table width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; position: relative; line-height: 0.9em; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; width: 100%;">
                    <tr>
                        <td><span style="margin: 0; padding: 0; font-size: 1.2em;"><strong>Coleta nº: {{ $incomeCollection->collection->id }}</strong></span></td>
                        <td style="text-align: right;"><span style="margin: 0; padding: 0;">{{ format_date($incomeCollection->collection->collected_at) }}</span></td>
                    </tr>
                </table>
                </p>
                <table class="table-sm table-borderless table-striped table" style="margin-bottom: 30px;">
                    <thead>
                        <th scope="col" style="text-align: center;">Item</th>
                        <th scope="col" style="text-align: center;">Quantidade</th>
                    </thead>
                    <tbody>
                        @foreach ($incomeCollection->collection->collectionItems as $item)
                            <tr style="line-height: 0.7em;">
                                <td scope="col" style="text-align: center;">{{ $item->product->name ?? \App\Models\Product::query()->withTrashed()->findOrFail($item->product_id)->name }}</td>
                                <td scope="col" style="text-align: center;">{{ number_format($item->quantity, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @endforeach
        </div>
    </div>
</body>

</html>
