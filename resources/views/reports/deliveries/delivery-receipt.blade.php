<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Recibo de entrega {{ $delivery->id }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <style type="text/css">
        @font-face {
            font-family: 'Lato';
            font-weight: 400;
            font-style: normal;
            font-variant: normal;
        }

        @font-face {
            font-family: 'Lato';
            font-weight: 700;
            font-style: normal;
            font-variant: normal;
        }

        body {
            font-family: 'Lato', serif;
            font-weight: bold;
        }

        h1,
        h2,
        h3,
        h5 {
            font-weight: 700;
        }

        table {
            font-size: 0.8rem;
        }

        .receipt-line {
            font-size: 0.8rem;
        }

        .clear-both {
            clear: both;
        }

        .header p:first-child {
            font-size: 1.3em;
            font-weight: bold;
        }

        @page {
            size: auto;
            margin: 0;
        }

        .delivery {
            width: 280px;
            height: 718px;
            position: relative;
            margin: 0;
            display: block;
        }

        .text-center {
            text-align: center;
        }

        .my-2 {
            margin-top: 0.5rem !important;
            margin-bottom: 0.5rem !important;
        }

        .w-75 {
            width: 75% !important;
        }

        .table {
            width: 100%;
            color: #212529;
            margin-top: 4px;
            margin-bottom: 4px;
            padding-left: 0;
            padding-right: 0;
        }

        .table th,
        .table td {
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
            padding-left: 0;
            padding-right: 0;
            vertical-align: top;
            border-top: 1px solid #dee2e6;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
        }

        .table tbody+tbody {
            border-top: 2px solid #dee2e6;
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }

        .table-bordered thead th,
        .table-bordered thead td {
            border-bottom-width: 2px;
        }

        .table-borderless th,
        .table-borderless td,
        .table-borderless thead th,
        .table-borderless tbody+tbody {
            border: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }
    </style>
</head>

<body>
    <div class="delivery">
        <div class="my-2 text-center">
            <img class="w-75" src="{{ $logoSrc }}" alt="Logo" />
        </div>

        <div style="padding-left: 7px; padding-right: 7px">
            <table class="table-borderless table">
                <tbody>
                    <td scope="col" class="receipt-line" style="text-align: left;"><strong>ENTREGA</strong></td>
                    <td scope="col" class="receipt-line" style="text-align: right;"><strong>Nº{{ $delivery->id }}</strong></td>
                </tbody>
            </table>
            <hr style="height: 1px; border: none; color: #000; background-color: #000; margin-top: 5px; margin-bottom: 0">
            <div class="receipt-line">
                <span><strong>CLIENTE: {{ $delivery->customer->trading_name }}</strong></span><br>
            </div>
            <div class="receipt-line">
                <span><strong>Razão social: {{ $delivery->customer->name }}</strong></span><br>
            </div>
            <div class="receipt-line">
                <span><strong>Endereço:</strong> {{ $delivery->customer->long_delivery_address }}</span><br>
            </div>
            <div class="receipt-line">
                <span><strong>Bairro:</strong> {{ $delivery->customer->delivery_address_district }}</span><br>
            </div>
            <div class="receipt-line">
                <span><strong>CEP:</strong> {{ $delivery->customer->friendly_delivery_address_zipcode }}</span>
            </div>
            <hr style="height: 1px; border: none; color: #000; background-color: #000; margin-top: 5px; margin-bottom: 3px">
            <div class="receipt-line">
                <span><strong>DATA: </strong>{{ format_date($delivery->delivered_at) }}</span>
            </div>
            <hr style="border: 2px dotted #000; border-style: none none dotted; color: #fff; background-color: #fff; margin-top: 5px; margin-bottom: 5px">
            <table class="table-borderless table-striped table">
                <tbody>
                    @foreach ($deliveryItems as $item)
                        <tr>
                            <td scope="col" class="receipt-line" style="line-height: 0.6rem; text-align: left;">{{ $item['name'] }}</td>
                            <td scope="col" class="receipt-line" style="line-height: 0.6rem; text-align: right;">{{ number_format($item['quantity'], 2, ',', '.') }} un</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <hr style="height: 1px; border: none; color: #000; background-color: #000; margin-top: 5px; margin-bottom: 3px">
            <div class="receipt-line">
                <span><strong>Total de unidades:</strong> {{ number_format($delivery->deliveryItems->sum(fn($deliveryItem) => $deliveryItem->quantity), 2, ',', '.') }}</span><br>
                <span><strong>Peso total:</strong> {{ number_format($delivery->deliveryItems->sum(fn($deliveryItem) => ($deliveryItem->quantity * ($deliveryItem->product->net_weight ?? \App\Models\Product::query()->withTrashed()->findOrFail($deliveryItem->product_id)->net_weight)) / 1000), 2, ',', '.') }}kg</span>
            </div>
            <hr style="height: 1px; border: none; color: #000; background-color: #000; margin-top: 5px; margin-bottom: 5px">
        </div>
    </div>
</body>

</html>
