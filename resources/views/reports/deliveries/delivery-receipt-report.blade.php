<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Recibo da entrega nº{{ $delivery->id }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-header" class="mb-2">
            <div class="header float-left">
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Entrega nº{{ $delivery->id }}</strong></p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Cliente:</strong> {{ $delivery->customer->name }} | {{ $delivery->customer->trading_name }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>CNPJ:</strong> {{ mask_cnpj($delivery->customer->tax_id_number) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Entregue em:</strong> {{ format_date($delivery->collected_at) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Recibo emitido em:</strong> {{ format_datetime(now()) }}</p>
            </div>
            <div class="float-right">
                <img class="p4m-img-nav" src="{{ $logoSrc }}" alt="Logo" />
            </div>
        </div>

        <hr class="clear-both">

        <div id="report-body">
            <table class="table-sm table-borderless table">
                <thead>
                    <th scope="col" style="text-align: left;">Item</th>
                    <th scope="col" style="text-align: left;">Quantidade</th>
                </thead>
                @foreach ($delivery->deliveryItems as $item)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $item->product?->name ?? \App\Models\Product::query()->withTrashed()->findOrFail($item->product_id)->name }}</td>
                        <td scope="col" style="text-align: left;">{{ number_format($item->quantity, 2, ',', '.') }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
