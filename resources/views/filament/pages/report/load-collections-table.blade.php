<x-filament::page>
    <form method="post" class="filament-form space-y-6" wire:submit.prevent="generate">
        @csrf
        {{ $this->form }}

        <div class="filament-page-actions filament-form-actions flex flex-wrap items-center justify-start gap-4">
            <x-filament::button type="submit">
                Gerar
            </x-filament::button>
            <a class="filament-button filament-button-size-md focus:ring-primary-600 focus:text-primary-600 focus:bg-primary-50 focus:border-primary-600 filament-page-button-action text-gray inline-flex min-h-[2.25rem] items-center justify-center gap-1 rounded-lg border border-gray-300 bg-white py-1 px-4 text-sm font-medium outline-none transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-inset focus:ring-offset-2" wire:click="cancel">
                Cancelar
            </a>
        </div>
    </form>
</x-filament::page>
