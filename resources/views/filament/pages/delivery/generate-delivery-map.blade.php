<x-filament::page>
    <div class="space-y-6">
        <form class="filament-form space-y-6">
            {{ $this->form }}
        </form>

        {{-- @if (!empty($this->mapData)) --}}
        <div wire:ignore id="map" style="width: 100%; height: 500px"></div>
        {{-- <div class="bg-white rounded-lg shadow p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    Resumo dos Clientes ({{ count($this->mapData) }} encontrados)
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach (collect($this->mapData)->groupBy('roteiro') as $itinerary => $customers)
                        <div class="border rounded-lg p-3">
                            <div class="flex items-center mb-2">
                                <div class="w-4 h-4 rounded-full mr-2"
                                     style="background-color: {{ $this->getItineraryColorHex($itinerary) }}"></div>
                                <span class="font-medium">Roteiro {{ $itinerary }}</span>
                                <span class="ml-auto text-sm text-gray-500">({{ count($customers) }} clientes)</span>
                            </div>
                            <div class="text-sm text-gray-600">
                                @foreach ($customers as $customer)
                                    <div>{{ $customer['name'] }}</div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div> --}}
        <script>
            let locations = [];
            document.addEventListener('livewire:init', () => {
                Livewire.on('update-map-data', (event) => {
                    initMap(event);
                })
            })
            // 2) Array de dados (ID, nome, latitude, longitude, roteiro)
        </script>
        {{-- @endif --}}
    </div>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCGsfXqz7uzaZYqKumbZGxr9U517r0GAmI&callback=initMap" async defer></script>
    <script>
        // 3) Função de inicialização do mapa
        function initMap(locations = []) {
            // Centraliza no primeiro ponto (ou você pode calcular a média)
            const center = {
                lat: -23.5489,
                lng: -46.6388
            };
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 11,
                center: center
            });

            // 4) Cria um InfoWindow único para reaproveitar
            const infoWindow = new google.maps.InfoWindow();

            if (locations.length === 0) {
                return;
            }

            // 5) Loop para criar marcadores
            locations[0].forEach(loc => {
                const marker = new google.maps.Marker({
                    position: {
                        lat: loc.lat,
                        lng: loc.lng
                    },
                    map: map,
                    title: loc.name,
                    // opcional: cor do pin de acordo com roteiro
                    icon: {
                        url: `http://maps.google.com/mapfiles/ms/icons/${loc.roteiro}-dot.png`
                    }
                });

                // evento de clique para abrir InfoWindow
                marker.addListener("click", () => {
                    infoWindow.setContent(
                        `<strong>${loc.name}</strong><br/>ID: ${loc.id}<br/>Roteiro: ${loc.roteiro}`
                    );
                    infoWindow.open(map, marker);
                });
            });
        }
    </script>
</x-filament::page>
