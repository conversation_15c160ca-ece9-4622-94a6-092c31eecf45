<x-forms::field-wrapper :id="$getId()" :label="$getLabel()" :label-sr-only="$isLabelHidden()" :helper-text="$getHelperText()" :hint="$getHint()" :hint-icon="$getHintIcon()" :required="$isRequired()" :state-path="$getStatePath()">
    <div x-data="{ state: $wire.entangle('{{ $getStatePath() }}') }">
        <input {!! $isDisabled() ? 'disabled' : null !!} class="focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-500 block w-full rounded-lg border-gray-300 shadow-sm transition duration-75 focus:ring-1 focus:ring-inset disabled:opacity-70 dark:border-gray-600 dark:bg-gray-700 dark:text-white" wire:model.defer="{{ $getStatePath() }}" placeholder="" type="text" maxlength="15" x-mask:dynamic="$input.length >= 15 ? '(99) 99999-9999' : '(99) 9999-9999'" />
    </div>
</x-forms::field-wrapper>
