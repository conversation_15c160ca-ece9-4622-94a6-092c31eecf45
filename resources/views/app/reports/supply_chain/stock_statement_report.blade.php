<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Extrato de estoque</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-header" class="mb-2">
            <div class="header float-left">
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Extrato de estoque</strong></p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Cliente:</strong> {{ $customer->name }} | {{ $customer->trading_name }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>CNPJ:</strong> {{ mask_cnpj($customer->tax_id_number) }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Período:</strong> {{ $dateFrom }} a {{ $dateTo }}</p>
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Relatório emitido em:</strong> {{ format_datetime(now()) }}</p>
            </div>
            <div class="float-right">
                <img class="p4m-img-nav" src="{{ $logoSrc }}" alt="Logo" />
            </div>
        </div>

        <hr class="clear-both">

        <div id="report-body">
            <table class="table-sm table-borderless table-striped table">
                <thead>
                    <th scope="col" style="text-align: left;">Data</th>
                    <th scope="col" style="text-align: left;">Item</th>
                    <th scope="col" style="text-align: left;">Quantidade coletada</th>
                    <th scope="col" style="text-align: left;">Quantidade entregue</th>
                    <th scope="col" style="text-align: left;">Ajuste</th>
                    <th scope="col" style="text-align: left;">Saldo</th>
                </thead>
                @foreach ($data as $date => $groupLines)
                    @for ($i = 0; $i < count($groupLines); $i++)
                        <tr>
                            <td scope="col" style="text-align: left;">{{ $i === 0 ? format_date($groupLines[$i]['movement_date']) : '' }}</td>
                            <td scope="col" style="text-align: left;">{{ $groupLines[$i]['product']['name'] }}</td>
                            <td scope="col" style="text-align: left;">{{ number_format($groupLines[$i]['collected_quantity'], 0) }}</td>
                            <td scope="col" style="text-align: left;">{{ number_format($groupLines[$i]['delivered_quantity'], 0) }}</td>
                            <td scope="col" style="text-align: left;">{{ !is_null($groupLines[$i]['adjustment_quantity']) ? number_format($groupLines[$i]['adjustment_quantity'], 0) : '' }}</td>
                            <td scope="col" style="text-align: left;">{{ number_format($groupLines[$i]['stock_quantity'], 0) }}</td>
                        </tr>
                    @endfor
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                @endforeach
            </table>
        </div>
        <div>
            <p><strong>Saldo atual geral:</strong></p>
            <ul>
                @foreach ($customerStockLocationProducts as $customerStockLocationProduct)
                    <li><span style="font-size: 0.8rem; line-height: 1.2em">{{ $customerStockLocationProduct->product?->name ?? \App\Models\Product::query()->withTrashed()->findOrFail($customerStockLocationProduct->product_id)->name }}: {{ $customerStockLocationProduct->current_quantity }}</span></li>
                @endforeach
            </ul>
        </div>
    </div>
</body>

</html>
