<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Tabela de clientes</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-body">
            <table class="table-sm table-borderless table-striped table">
                <thead>
                    <th scope="col" style="text-align: left;">id</th>
                    <th scope="col" style="text-align: left;">salesman_id</th>
                    <th scope="col" style="text-align: left;">customer_group_id</th>
                    <th scope="col" style="text-align: left;">payment_recovery_setting_id</th>
                    <th scope="col" style="text-align: left;">name</th>
                    <th scope="col" style="text-align: left;">trading_name</th>
                    <th scope="col" style="text-align: left;">tax_id_number</th>
                    <th scope="col" style="text-align: left;">exempt_from_state_registration</th>
                    <th scope="col" style="text-align: left;">state_registration</th>
                    <th scope="col" style="text-align: left;">city_registration</th>
                    <th scope="col" style="text-align: left;">email</th>
                    <th scope="col" style="text-align: left;">billing_email</th>
                    <th scope="col" style="text-align: left;">billing_phone</th>
                    <th scope="col" style="text-align: left;">operation_email</th>
                    <th scope="col" style="text-align: left;">phone_1</th>
                    <th scope="col" style="text-align: left;">in_charge_person_1</th>
                    <th scope="col" style="text-align: left;">phone_2</th>
                    <th scope="col" style="text-align: left;">in_charge_person_2</th>
                    <th scope="col" style="text-align: left;">address_zipcode</th>
                    <th scope="col" style="text-align: left;">address_address</th>
                    <th scope="col" style="text-align: left;">address_number</th>
                    <th scope="col" style="text-align: left;">address_additional_info</th>
                    <th scope="col" style="text-align: left;">address_district</th>
                    <th scope="col" style="text-align: left;">address_city_id</th>
                    <th scope="col" style="text-align: left;">address_state_id</th>
                    <th scope="col" style="text-align: left;">latitude</th>
                    <th scope="col" style="text-align: left;">longitude</th>
                    <th scope="col" style="text-align: left;">preferred_charging_method</th>
                    <th scope="col" style="text-align: left;">previous_collection_count</th>
                    <th scope="col" style="text-align: left;">minimum_billing_amount</th>
                    <th scope="col" style="text-align: left;">default_due_day</th>
                    <th scope="col" style="text-align: left;">default_billing_period</th>
                    <th scope="col" style="text-align: left;">default_tax_condition</th>
                    <th scope="col" style="text-align: left;">default_delivery_model</th>
                    <th scope="col" style="text-align: left;">stock_safety_percentage</th>
                    <th scope="col" style="text-align: left;">active</th>
                    <th scope="col" style="text-align: left;">created_at</th>
                    <th scope="col" style="text-align: left;">updated_at</th>
                </thead>
                @foreach ($data as $lines)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $lines['id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['salesman_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['customer_group_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['payment_recovery_setting_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['trading_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['tax_id_number'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['exempt_from_state_registration'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['state_registration'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['city_registration'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['email'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['billing_email'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['billing_phone'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['operation_email'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['phone_1'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['in_charge_person_1'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['phone_2'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['in_charge_person_2'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_zipcode'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_address'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_number'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_additional_info'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_district'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_city_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['address_state_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['latitude'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['longitude'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['preferred_charging_method'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['previous_collection_count'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['minimum_billing_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['default_due_day'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['default_billing_period'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['default_tax_condition'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['default_delivery_model'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['stock_safety_percentage'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['active'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['updated_at'] }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
