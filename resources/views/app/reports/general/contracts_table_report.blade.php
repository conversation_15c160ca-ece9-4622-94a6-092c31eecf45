<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Tabela de contratos</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-body">
            <table class="table-sm table-borderless table-striped table">
                <thead>
                    <th scope="col" style="text-align: left;">id</th>
                    <th scope="col" style="text-align: left;">alternate_id</th>
                    <th scope="col" style="text-align: left;">entity_id</th>
                    <th scope="col" style="text-align: left;">entity_type</th>
                    <th scope="col" style="text-align: left;">salesman_id</th>
                    <th scope="col" style="text-align: left;">type</th>
                    <th scope="col" style="text-align: left;">process_type</th>
                    <th scope="col" style="text-align: left;">tax_condition</th>
                    <th scope="col" style="text-align: left;">due_day</th>
                    <th scope="col" style="text-align: left;">amount</th>
                    <th scope="col" style="text-align: left;">minimum_billing_amount</th>
                    <th scope="col" style="text-align: left;">status</th>
                    <th scope="col" style="text-align: left;">term_started_at</th>
                    <th scope="col" style="text-align: left;">term_ended_at</th>
                    <th scope="col" style="text-align: left;">signed_at</th>
                    <th scope="col" style="text-align: left;">cancelled_at</th>
                    <th scope="col" style="text-align: left;">created_at</th>
                    <th scope="col" style="text-align: left;">updated_at</th>
                    <th scope="col" style="text-align: left;">deleted_at</th>
                    <th scope="col" style="text-align: left;">contract_item_id</th>
                    <th scope="col" style="text-align: left;">contract_item_contract_id</th>
                    <th scope="col" style="text-align: left;">contract_item_item_id</th>
                    <th scope="col" style="text-align: left;">contract_item_item_type</th>
                    <th scope="col" style="text-align: left;">contract_item_quantity</th>
                    <th scope="col" style="text-align: left;">contract_item_unit_amount</th>
                    <th scope="col" style="text-align: left;">contract_item_total_amount</th>
                    <th scope="col" style="text-align: left;">contract_item_visible_in_collections</th>
                    <th scope="col" style="text-align: left;">contract_item_created_at</th>
                    <th scope="col" style="text-align: left;">contract_item_updated_at</th>
                    <th scope="col" style="text-align: left;">contract_item_deleted_at</th>
                    <th scope="col" style="text-align: left;">product_id</th>
                    <th scope="col" style="text-align: left;">product_subcategory_id</th>
                    <th scope="col" style="text-align: left;">product_code</th>
                    <th scope="col" style="text-align: left;">product_name</th>
                    <th scope="col" style="text-align: left;">product_description</th>
                    <th scope="col" style="text-align: left;">product_gross_weight</th>
                    <th scope="col" style="text-align: left;">product_net_weight</th>
                    <th scope="col" style="text-align: left;">product_default_price</th>
                    <th scope="col" style="text-align: left;">product_created_at</th>
                    <th scope="col" style="text-align: left;">product_updated_at</th>
                </thead>
                @foreach ($data as $lines)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $lines['id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['alternate_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['entity_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['entity_type'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['salesman_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['type'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['process_type'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['tax_condition'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['due_day'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['minimum_billing_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['status'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['term_started_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['term_ended_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['signed_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['cancelled_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['deleted_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_contract_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_item_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_item_type'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_quantity'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_unit_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_total_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_visible_in_collections'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_item_deleted_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_subcategory_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_code'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_description'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_gross_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_net_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_default_price'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_updated_at'] }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
