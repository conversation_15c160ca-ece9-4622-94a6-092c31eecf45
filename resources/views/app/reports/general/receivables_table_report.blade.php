<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Tabela de faturas</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-body">
            <table class="table-sm table-borderless table-striped table">
                <thead>
                    <th scope="col" style="text-align: left;">id</th>
                    <th scope="col" style="text-align: left;">customer_id</th>
                    <th scope="col" style="text-align: left;">document_number</th>
                    <th scope="col" style="text-align: left;">amount</th>
                    <th scope="col" style="text-align: left;">additional_info</th>
                    <th scope="col" style="text-align: left;">issued_at</th>
                    <th scope="col" style="text-align: left;">created_at</th>
                    <th scope="col" style="text-align: left;">updated_at</th>
                    <th scope="col" style="text-align: left;">product_id</th>
                    <th scope="col" style="text-align: left;">product_subcategory_id</th>
                    <th scope="col" style="text-align: left;">product_code</th>
                    <th scope="col" style="text-align: left;">product_name</th>
                    <th scope="col" style="text-align: left;">product_description</th>
                    <th scope="col" style="text-align: left;">product_gross_weight</th>
                    <th scope="col" style="text-align: left;">product_net_weight</th>
                    <th scope="col" style="text-align: left;">product_default_price</th>
                    <th scope="col" style="text-align: left;">product_created_at</th>
                    <th scope="col" style="text-align: left;">product_updated_at</th>
                    <th scope="col" style="text-align: left;">receivable_id</th>
                    <th scope="col" style="text-align: left;">receivable_customer_id</th>
                    <th scope="col" style="text-align: left;">receivable_document_id</th>
                    <th scope="col" style="text-align: left;">receivable_document_type</th>
                    <th scope="col" style="text-align: left;">receivable_sequence</th>
                    <th scope="col" style="text-align: left;">receivable_original_amount</th>
                    <th scope="col" style="text-align: left;">receivable_pis_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_cofins_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_csll_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_irrf_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_inss_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_iss_wht_amount</th>
                    <th scope="col" style="text-align: left;">receivable_addition_amount</th>
                    <th scope="col" style="text-align: left;">receivable_discount_amount</th>
                    <th scope="col" style="text-align: left;">receivable_updated_amount</th>
                    <th scope="col" style="text-align: left;">receivable_status</th>
                    <th scope="col" style="text-align: left;">receivable_email_sent</th>
                    <th scope="col" style="text-align: left;">receivable_collection_count</th>
                    <th scope="col" style="text-align: left;">receivable_issued_at</th>
                    <th scope="col" style="text-align: left;">receivable_expires_at</th>
                    <th scope="col" style="text-align: left;">receivable_settled_at</th>
                    <th scope="col" style="text-align: left;">receivable_created_at</th>
                    <th scope="col" style="text-align: left;">receivable_updated_at</th>
                </thead>
                @foreach ($data as $lines)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $lines['id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['customer_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['document_number'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['additional_info'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['issued_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_subcategory_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_code'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_description'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_gross_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_net_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_default_price'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_customer_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_document_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_document_type'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_sequence'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_original_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_pis_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_cofins_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_csll_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_irrf_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_inss_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_iss_wht_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_addition_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_discount_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_updated_amount'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_status'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_email_sent'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_collection_count'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_issued_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_expires_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_settled_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['receivable_updated_at'] }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
