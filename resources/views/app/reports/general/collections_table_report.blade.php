<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>P4M - Tabela de coletas</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet">

    @vite(['resources/css/report-default.css'])
</head>

<body>
    <div>
        <div id="report-body">
            <table class="table-sm table-borderless table-striped table">
                <thead>
                    <th scope="col" style="text-align: left;">id</th>
                    <th scope="col" style="text-align: left;">customer_id</th>
                    <th scope="col" style="text-align: left;">contract_id</th>
                    <th scope="col" style="text-align: left;">additional_info</th>
                    <th scope="col" style="text-align: left;">email_sent</th>
                    <th scope="col" style="text-align: left;">collected_at</th>
                    <th scope="col" style="text-align: left;">created_at</th>
                    <th scope="col" style="text-align: left;">updated_at</th>
                    <th scope="col" style="text-align: left;">collection_item_id</th>
                    <th scope="col" style="text-align: left;">collection_item_collection_id</th>
                    <th scope="col" style="text-align: left;">collection_item_product_id</th>
                    <th scope="col" style="text-align: left;">collection_item_quantity</th>
                    <th scope="col" style="text-align: left;">collection_item_created_at</th>
                    <th scope="col" style="text-align: left;">collection_item_updated_at</th>
                    <th scope="col" style="text-align: left;">product_id</th>
                    <th scope="col" style="text-align: left;">product_subcategory_id</th>
                    <th scope="col" style="text-align: left;">product_code</th>
                    <th scope="col" style="text-align: left;">product_name</th>
                    <th scope="col" style="text-align: left;">product_description</th>
                    <th scope="col" style="text-align: left;">product_gross_weight</th>
                    <th scope="col" style="text-align: left;">product_net_weight</th>
                    <th scope="col" style="text-align: left;">product_default_price</th>
                    <th scope="col" style="text-align: left;">product_created_at</th>
                    <th scope="col" style="text-align: left;">product_updated_at</th>
                </thead>
                @foreach ($data as $lines)
                    <tr>
                        <td scope="col" style="text-align: left;">{{ $lines['id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['customer_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['contract_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['additional_info'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['email_sent'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collected_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_collection_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_product_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_quantity'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['collection_item_updated_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_subcategory_id'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_code'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_name'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_description'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_gross_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_net_weight'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_default_price'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_created_at'] }}</td>
                        <td scope="col" style="text-align: left;">{{ $lines['product_updated_at'] }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
