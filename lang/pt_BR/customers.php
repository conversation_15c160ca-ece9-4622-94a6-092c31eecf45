<?php

return [

    'forms' => [
        'fields' => [
            'id' => 'ID',
            'name' => 'Razão social',
            'trading_name' => 'Nome fantasia',
            'tax_id_number' => 'CPF/CNPJ',
            'exempt_from_state_registration' => 'Isenção inscr. estadual?',
            'state_registration' => 'Inscrição estadual',
            'city_registration' => 'Inscrição municipal',
            'email' => 'E-mail',
            'billing_email' => 'E-mail de faturamento (separado por vírgula)',
            'billing_phone' => 'Telefone de faturamento',
            'operation_email' => 'E-mail de movimentação (separado por vírgula)',
            'phone_1' => 'Telefone 1',
            'in_charge_person_1' => 'Responsável 1',
            'phone_2' => 'Telefone 2',
            'in_charge_person_2' => 'Responsável 2',
            'address_zipcode' => 'CEP',
            'address_address' => 'Endereço',
            'address_number' => 'Número',
            'address_additional_info' => 'Complemento',
            'address_district' => 'Bairro',
            'address_city' => 'Cidade',
            'address_city_id' => 'Cidade',
            'address_state' => 'Estado',
            'address_state_id' => 'Estado',
            'specific_latitude' => 'Latitude específica',
            'specific_longitude' => 'Longitude específica',
            'customer_group_id' => 'Grupo de cliente',
            'preferred_charging_method' => 'Forma de cobrança',
            'payment_recovery_day_count' => 'Regra de cobrança',
            'minimum_billing_amount' => 'Valor mínimo fat.',
            'default_due_day' => 'Dia de vencimento',
            'default_billing_period' => 'Período de faturamento',
            'default_tax_condition' => 'Condição fiscal',
            'default_delivery_model' => 'Modelo de entrega',
            'stock_safety_percentage' => 'Folga de estoque',
            'previous_collection_count' => 'Qtde. coletas anteriores',
            'active' => 'Ativo',
            'tags' => 'Tags',
            'service_started_at' => 'Serviço iniciado em',
            'index_id' => 'Índice',
            'default_bank_account_wallet_id' => 'Banco/carteira padrão',
            'customer_histories' => [
                'operation_id' => 'ID doc. origem',
                'operation_type' => 'Origem',
                'history' => 'Histórico'
            ],
            'customer_coupons' => [
                'amount' => 'Valor',
                'expires_at' => 'Vencimento',
                'additional_info' => 'Observações',
                'type' => 'Tipo',
                'starting_at' => 'Início',
                'apply_to_minimum_amount' => 'Aplicar em faturamento mínimo',
                'apply_to_regular_amount' => 'Aplicar em faturamento regular',
            ],
            'customer_contacts' => [
                'type' => 'Tipo',
                'name' => 'Nome',
                'email' => 'E-mail',
                'phone' => 'Telefone',
            ],
        ]
    ],

    'responses' => [
        'create' => [
            'success' => 'O cliente foi criado.'
        ],
        'update' => [
            'success' => 'O cliente foi atualizado.'
        ],
        'activate' => [
            'success' => 'O cliente foi ativado.'
        ],
        'inactivate' => [
            'success' => 'O cliente foi inativado.'
        ],
        'delete' => [
            'success' => 'O cliente foi excluído.',
            'errors' => [
                'existing_collections' => 'Existem coletas vinculadas a este cliente.',
                'existing_deliveries' => 'Existem entregas vinculadas a este cliente.',
                'existing_customer_products' => 'Existem itens vinculados a este cliente.',
                'existing_lending_stock_adjustments' => 'Existem ajustes de estoque (comodato) vinculados a este cliente.',
                'existing_billings' => 'Existem faturamentos vinculados a este cliente.',
            ]
        ],
    ]

];
