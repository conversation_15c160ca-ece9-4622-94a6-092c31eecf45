<?php

return [

    'forms' => [
        'fields' => [
            'id' => 'ID',
            'alternate_id' => 'Código alternativo',
            'type' => 'Tipo',
            'process_type' => 'Tipo de processo',
            'entity_id' => 'Razão social',
            'entity_trading_name' => 'Nome fantasia',
            'entity_tax_id_number' => 'CPF/CNPJ',
            'salesman_id' => 'Vendedor',
            'term_started_at' => 'Início de vigência',
            'term_ended_at' => 'Fim de vigência',
            'current_term_started_at' => 'Ini. vig. atual',
            'current_term_ended_at' => 'Fim vig. atual',
            'signed_at' => 'Assinado em',
            'amount' => 'Valor',
            'minimum_billing_amount' => 'Valor mínimo fat.',
            'due_day' => 'Dia de vencimento',
            'tax_condition' => 'Condição fiscal',
            'contract_item_code' => 'Código do item',
            'contract_item_id' => 'Item',
            'contract_item_quantity' => 'Quantidade',
            'contract_item_amount' => 'Valor',
            'contract_item_visible_in_collections' => 'Visível em coletas',
            'contract_item_print_product_id' => 'Imprimir como',
        ]
    ],

    'responses' => [
        'create' => [
            'success' => 'O contrato foi criado.'
        ],
        'update' => [
            'success' => 'O contrato foi atualizado.'
        ],
        'delete' => [
            'success' => 'O contrato foi excluído.',
            'errors' => [
                'existing_collections_for_entity' => 'Existem coletas registradas para este cliente.',
                'existing_deliveries_for_entity' => 'Existem entregas registradas para este cliente.',
            ]
        ],
    ]

];
