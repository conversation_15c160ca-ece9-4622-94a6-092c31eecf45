<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400"></a></p>

# Preparando o ambiente

## Introdução

O primeiro passo para a preparação do ambiente da plataforma *P4 Wash* é entender que o modelo de separação de clientes é por banco de dados. O motor central da plataforma é identificado a partir de um banco de dados central (geralmente nomeado "*p4m_main*", mas você pode colocar qualquer nome que desejar), e cada cliente é identificado por um banco de dados adicional, que leva o prefixo "*p4m_t_*" em cada ID de banco de dados.

Também, antes de iniciar é importante lembrar que, por conta da forma como a _package_ __stancl/tenancy__ é implementada, os comandos das _migrations_ devem ser rodados como:
```
php artisan tenants:migrate
php artisan tenants:rollback
php artisan tenants:migrate-fresh
php artisan tenants:seed
```

Obs.: você sempre pode procurar pela documentação da _package_ __stancl/tenancy__ na web, geralmente encontrada no próprio site da _package_ (https://tenancyforlaravel.com).

## Pré-requisitos

- PHP 8.1
- MySQL 8
- Node 12.22+
- NPM 8.5+

## Criando uma nova base

Para iniciar, você pode criar uma nova base de dados pelo próprio terminal através do client MySQL:
```
$ mysql -u <usuario> -p
(Usar sua senha definida na instalação do MySQL, seja root ou algum outro usuário)

$ create database p4m_main;
$ exit;
```

Em seguida, na pasta do projeto, executar os seguintes comandos:
```
$ php artisan ti
$ $tenant = Tenant::create(['id' => '<nome_da_base_de_teste>']);
(Onde nome_da_base_de_teste é o nome que você definir, como por exemplo "test")

$ $tenant->domains()->create(['domain' => '<nome_da_base_de_teste>.p4m-main.test']);
(Onde nome_da_base_de_teste é o nome que você definiu no comando anterior. Se você escolheu "test", o domain deverá ser test.ocmed.test)

$ exit
```

Depois disso, você pode popular a base de dados através do seguinte comando:
```
$ php artisan dev:env <nome_da_base_de_teste>
```

Será criado um usuário <EMAIL>.br_ com a senha _1234_, mas você também pode definir uma senha _master_ no seu arquivo .env, com a entrada MASTER_PASSWORD (você pode copiar do .env.example disponível na raiz do projeto). Ao definir essa senha, você poderá logar na conta de qualquer usuário com a senha _master_, sem a necessidade de redefinir as senhas para _1234_ ou qualquer outro valor.
