<?php

// @formatter:off
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON>l <<EMAIL>>
 */


namespace App\Models{
/**
 * Category model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $type
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Subcategory> $subcategories
 * @property-read int|null $subcategories_count
 * @method static \Illuminate\Database\Eloquent\Builder|Category newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category query()
 */
	class Category extends \Eloquent {}
}

namespace App\Models{
/**
 * City model.
 *
 * @package App\Models
 * @property int $id
 * @property int $state_id
 * @property string $name
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @property \App\Models\State $state
 * @method static \Illuminate\Database\Eloquent\Builder|City newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|City newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|City query()
 */
	class City extends \Eloquent {}
}

namespace App\Models{
/**
 * Collection model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property string $additional_info
 * @property bool $email_sent
 * @property \Carbon\Carbon $collected_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \Illuminate\Support\Collection|\App\Models\CollectionItem[] $collectionItems
 * @property \Illuminate\Support\Collection|\App\Models\CustomerHistory[] $customerHistories
 * @property \Illuminate\Support\Collection|\App\Models\CollectionReceiptEmail[] $collectionReceiptEmails
 * @property-read int|null $collection_items_count
 * @property-read int|null $collection_receipt_emails_count
 * @property-read int|null $customer_histories_count
 * @method static \Illuminate\Database\Eloquent\Builder|Collection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Collection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Collection query()
 */
	class Collection extends \Eloquent {}
}

namespace App\Models{
/**
 * Collection item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $collection_id
 * @property int $product_id
 * @property float $quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Collection $collection
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionItem query()
 */
	class CollectionItem extends \Eloquent {}
}

namespace App\Models{
/**
 * Collection receipt email model.
 *
 * @package App\Models
 * @property int $id
 * @property int $collection_id
 * @property array $to_emails
 * @property string $message
 * @property \Carbon\Carbon $email_sent_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Collection $collection
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionReceiptEmail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionReceiptEmail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CollectionReceiptEmail query()
 */
	class CollectionReceiptEmail extends \Eloquent {}
}

namespace App\Models{
/**
 * Contract model.
 *
 * @package App\Models
 * @property int $id
 * @property string $alternate_id
 * @property int $entity_id
 * @property string $entity_type
 * @property string $type
 * @property string $process_type
 * @property string $tax_condition
 * @property int $due_day
 * @property float $amount
 * @property float $minimum_billing_amount
 * @property string $status
 * @property \Carbon\Carbon $term_started_at
 * @property \Carbon\Carbon $term_ended_at
 * @property \Carbon\Carbon $signed_at
 * @property \Carbon\Carbon $cancelled_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 * @property \App\Models\Customer|\App\Models\Supplier $entity
 * @property \Illuminate\Support\Collection|\App\Models\ContractItem[] $contractItems
 * @property-read int|null $contract_items_count
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract query()
 */
	class Contract extends \Eloquent {}
}

namespace App\Models{
/**
 * Contract item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $contract_id
 * @property int $item_id
 * @property string $item_type
 * @property float $quantity
 * @property float $unit_amount
 * @property float $total_amount
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 * @property \App\Models\Contract $contract
 * @property \App\Models\Product $item
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractItem withoutTrashed()
 */
	class ContractItem extends \Eloquent {}
}

namespace App\Models{
/**
 * Contract term model.
 *
 * @package App\Models
 * @property int $id
 * @property int $contract_id
 * @property \Carbon\Carbon $started_at
 * @property \Carbon\Carbon $ended_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 * @property \App\Models\Contract $contract
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTerm withoutTrashed()
 */
	class ContractTerm extends \Eloquent {}
}

namespace App\Models{
/**
 * Customer model.
 *
 * @package App\Models
 * @property int $id
 * @property int $salesman_id
 * @property string $name
 * @property string $trading_name
 * @property string $tax_id_number
 * @property bool $exempt_from_state_registration
 * @property string $state_registration
 * @property string $city_registration
 * @property string $email
 * @property string $billing_email
 * @property string $operation_email
 * @property string $phone_1
 * @property string $in_charge_person_1
 * @property string $phone_2
 * @property string $in_charge_person_2
 * @property string $address_zipcode
 * @property string $address_address
 * @property string $address_number
 * @property string $address_additional_info
 * @property string $address_district
 * @property int $address_city_id
 * @property int $address_state_id
 * @property string $preferred_charging_method
 * @property int $previous_collection_count
 * @property float $minimum_billing_amount
 * @property int $default_due_day
 * @property string $default_tax_condition
 * @property string $default_delivery_model
 * @property float $stock_safety_percentage
 * @property bool $active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $friendly_tax_id_number
 * @property \App\Models\Salesman $salesman
 * @property \App\Models\City $city
 * @property \App\Models\State $state
 * @property \App\Models\Contract|null $activeLoanContract
 * @property \App\Models\Delivery|null $lastDelivery
 * @property \Illuminate\Support\Collection|\App\Models\CustomerHistory[] $customerHistories
 * @property \Illuminate\Support\Collection|\App\Models\Contract[] $contracts
 * @property \Illuminate\Support\Collection|\App\Models\Delivery[] $deliveries
 * @property \Illuminate\Support\Collection|\App\Models\Collection[] $collections
 * @property \Illuminate\Support\Collection|\App\Models\StockLocation[] $stockLocations
 * @property \Illuminate\Support\Collection|\App\Models\IntegrationLog[] $integrationLogs
 * @property-read int|null $collections_count
 * @property-read int|null $contracts_count
 * @property-read int|null $customer_histories_count
 * @property-read int|null $deliveries_count
 * @property-read int|null $integration_logs_count
 * @property-read int|null $stock_locations_count
 * @method static \Database\Factories\CustomerFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Customer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Customer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Customer query()
 */
	class Customer extends \Eloquent {}
}

namespace App\Models{
/**
 * Customer history model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property int $operation_id
 * @property string $operation_type
 * @property string $history
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \App\Models\Collection $operation
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerHistory query()
 */
	class CustomerHistory extends \Eloquent {}
}

namespace App\Models{
/**
 * Delivery model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property string $additional_info
 * @property \Carbon\Carbon $delivered_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \Illuminate\Support\Collection|\App\Models\DeliveryItem[] $deliveryItems
 * @property \Illuminate\Support\Collection|\App\Models\DeliveryReceiptEmail[] $deliveryReceiptEmails
 * @property-read int|null $delivery_items_count
 * @property-read int|null $delivery_receipt_emails_count
 * @method static \Illuminate\Database\Eloquent\Builder|Delivery newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Delivery newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Delivery query()
 */
	class Delivery extends \Eloquent {}
}

namespace App\Models{
/**
 * Delivery item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $delivery_id
 * @property int $product_id
 * @property float $quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Delivery $delivery
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryItem query()
 */
	class DeliveryItem extends \Eloquent {}
}

namespace App\Models{
/**
 * Delivery receipt email model.
 *
 * @package App\Models
 * @property int $id
 * @property int $delivery_id
 * @property array $to_emails
 * @property string $message
 * @property \Carbon\Carbon $email_sent_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Delivery $delivery
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryReceiptEmail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryReceiptEmail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryReceiptEmail query()
 */
	class DeliveryReceiptEmail extends \Eloquent {}
}

namespace App\Models{
/**
 * Integration log model.
 *
 * @package App\Models
 * @property int $id
 * @property int $integration_id
 * @property string $integration_type
 * @property int $entity_id
 * @property string $entity_type
 * @property string $data_flow
 * @property string $endpoint
 * @property array $data
 * @property string $message
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $entity
 * @method static \Illuminate\Database\Eloquent\Builder|IntegrationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IntegrationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IntegrationLog query()
 */
	class IntegrationLog extends \Eloquent {}
}

namespace App\Models{
/**
 * Lending stock adjustment model.
 *
 * @package App\Models
 * @property int $id
 * @property int $customer_id
 * @property \Carbon\Carbon $reference_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Customer $customer
 * @property \Illuminate\Support\Collection|\App\Models\LendingStockAdjustmentItem[] $lendingStockAdjustmentItems
 * @property-read int|null $lending_stock_adjustment_items_count
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustment query()
 */
	class LendingStockAdjustment extends \Eloquent {}
}

namespace App\Models{
/**
 * Lending stock adjustment item model.
 *
 * @package App\Models
 * @property int $id
 * @property int $lending_stock_adjustment_id
 * @property int $product_id
 * @property float $old_stock_quantity
 * @property float $collected_quantity
 * @property float $delivered_quantity
 * @property float $out_of_movement_quantity
 * @property float $adjustment_quantity
 * @property float $current_stock_quantity_before_update
 * @property float $current_stock_quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\LendingStockAdjustment $lendingStockAdjustment
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustmentItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustmentItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LendingStockAdjustmentItem query()
 */
	class LendingStockAdjustmentItem extends \Eloquent {}
}

namespace App\Models{
/**
 * Operator model.
 *
 * @package App\Models
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $email
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Operator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Operator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Operator query()
 */
	class Operator extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\Product
 *
 * @property-read \App\Models\Subcategory|null $subcategory
 * @method static \Database\Factories\ProductFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product query()
 */
	class Product extends \Eloquent implements \App\Core\Models\Interfaces\HandlesSearchableSelectData {}
}

namespace App\Models{
/**
 * App\Models\Receivable
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Receivable newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Receivable newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Receivable query()
 */
	class Receivable extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\Region
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Region newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Region newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Region query()
 */
	class Region extends \Eloquent {}
}

namespace App\Models{
/**
 * Salesman model.
 *
 * @package App\Models
 * @property int $id
 * @property int $operator_id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\Operator $operator
 * @method static \Illuminate\Database\Eloquent\Builder|Salesman newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Salesman newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Salesman query()
 */
	class Salesman extends \Eloquent {}
}

namespace App\Models{
/**
 * SMTP configuration model.
 *
 * @package App\Models
 * @property int $id
 * @property string $host
 * @property string $port
 * @property string $username
 * @property string $password
 * @property string $encryption
 * @property string $from_address
 * @property string $from_name
 * @property bool $active
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @method static \Database\Factories\SmtpConfigurationFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|SmtpConfiguration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmtpConfiguration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SmtpConfiguration query()
 */
	class SmtpConfiguration extends \Eloquent {}
}

namespace App\Models{
/**
 * State model.
 *
 * @package App\Models
 * @property int $id
 * @property int $region_id
 * @property string $abbreviation
 * @property string $name
 * @property \DateTime $created_at
 * @property \DateTime $updated_at
 * @property \App\Models\Region $region
 * @property \Illuminate\Support\Collection|\App\Models\City[] $cities
 * @property-read int|null $cities_count
 * @method static \Illuminate\Database\Eloquent\Builder|State newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|State newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|State query()
 */
	class State extends \Eloquent {}
}

namespace App\Models{
/**
 * Stock location model.
 *
 * @package App\Models
 * @property int $id
 * @property int $stock_nature_id
 * @property int $customer_id
 * @property string $name
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\StockNature $stockNature
 * @property \Illuminate\Support\Collection|\App\Models\StockLocationProduct[] $stockLocationProducts
 * @property-read \App\Models\Customer|null $customer
 * @property-read int|null $stock_location_products_count
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocation query()
 */
	class StockLocation extends \Eloquent {}
}

namespace App\Models{
/**
 * Stock location product model.
 *
 * @package App\Models
 * @property int $id
 * @property int $stock_location_id
 * @property int $product_id
 * @property float $initial_quantity
 * @property float $current_quantity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\StockLocation $stockLocation
 * @property \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocationProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocationProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockLocationProduct query()
 */
	class StockLocationProduct extends \Eloquent {}
}

namespace App\Models{
/**
 * Stock nature model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $location_type
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|StockNature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockNature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StockNature query()
 */
	class StockNature extends \Eloquent {}
}

namespace App\Models{
/**
 * App\Models\Subcategory
 *
 * @property-read \App\Models\Category|null $category
 * @method static \Illuminate\Database\Eloquent\Builder|Subcategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Subcategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Subcategory query()
 */
	class Subcategory extends \Eloquent {}
}

namespace App\Models{
/**
 * Supplier model.
 *
 * @package App\Models
 * @property int $id
 * @property string $name
 * @property string $trading_name
 * @property string $tax_id_number
 * @property bool $exempt_from_state_registration
 * @property string $state_registration
 * @property string $city_registration
 * @property string $email
 * @property string $billing_email
 * @property string $phone_1
 * @property string $phone_2
 * @property string $address_zipcode
 * @property string $address_address
 * @property string $address_number
 * @property string $address_additional_info
 * @property string $address_district
 * @property int $address_city_id
 * @property int $address_state_id
 * @property string $preferred_earning_method
 * @property bool $active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \App\Models\City $city
 * @property \App\Models\State $state
 * @property \Illuminate\Support\Collection|\App\Models\Contract[] $contracts
 * @property-read int|null $contracts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\IntegrationLog> $integrationLogs
 * @property-read int|null $integration_logs_count
 * @method static \Database\Factories\SupplierFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Supplier newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Supplier newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Supplier query()
 */
	class Supplier extends \Eloquent {}
}

namespace App\Models{
/**
 * Tenancy customer model.
 *
 * @package App\Models
 * @property int $id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property array $data
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Stancl\Tenancy\Database\Models\Domain> $domains
 * @property-read int|null $domains_count
 * @method static \Stancl\Tenancy\Database\TenantCollection<int, static> all($columns = ['*'])
 * @method static \Stancl\Tenancy\Database\TenantCollection<int, static> get($columns = ['*'])
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant query()
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tenant whereUpdatedAt($value)
 */
	class Tenant extends \Eloquent implements \Stancl\Tenancy\Contracts\TenantWithDatabase {}
}

namespace App\Models{
/**
 * App\Models\User
 *
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 */
	class User extends \Eloquent implements \Filament\Models\Contracts\FilamentUser {}
}
